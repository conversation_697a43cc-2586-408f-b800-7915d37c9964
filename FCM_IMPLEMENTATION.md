# FCM (Firebase Cloud Messaging) Implementation Guide

## Overview
This document outlines the comprehensive FCM implementation for the Gather Point Flutter application, providing proper notification handling across all app states.

## Features Implemented

### ✅ Core FCM Features
- **Token Management**: Automatic token generation, storage, and server synchronization
- **Permission Handling**: Proper notification permission requests for iOS and Android
- **Multi-State Support**: Foreground, background, and terminated app state handling
- **Local Notifications**: Enhanced local notification display with custom styling
- **Navigation Handling**: Smart navigation based on notification data payload

### ✅ Enhanced UI Components
- **In-App Notifications**: Beautiful overlay notifications for foreground messages
- **Permission Dialog**: User-friendly permission request dialog
- **Settings Screen**: Comprehensive notification settings management
- **Test Functionality**: Built-in notification testing capabilities

### ✅ Platform Configuration
- **Android**: Proper manifest configuration with notification channels and permissions
- **iOS**: AppDelegate configuration with Firebase integration
- **Cross-Platform**: Unified API across both platforms

## File Structure

```
lib/
├── core/
│   ├── services/
│   │   ├── fcm_service.dart              # Main FCM service
│   │   ├── navigation_service.dart       # Navigation handling
│   │   └── service_locator.dart          # Dependency injection
│   └── widgets/
│       └── notification_widgets.dart     # UI components
├── feature/
│   └── notifications/
│       └── presentation/
│           └── views/
│               └── notification_settings_view.dart
└── main.dart                             # App initialization

android/
├── app/src/main/
│   ├── AndroidManifest.xml              # Android permissions & config
│   └── res/values/colors.xml            # Notification colors

ios/
└── Runner/
    └── AppDelegate.swift                # iOS FCM configuration
```

## Key Components

### 1. FCMService (`lib/core/services/fcm_service.dart`)
Main service handling all FCM operations:
- Token management and API integration
- Message handling for all app states
- Local notification display
- Navigation routing based on notification data

### 2. NavigationService (`lib/core/services/navigation_service.dart`)
Centralized navigation management:
- Global navigator key
- Route navigation methods
- Dialog and bottom sheet utilities

### 3. NotificationWidgets (`lib/core/widgets/notification_widgets.dart`)
UI components for notifications:
- `InAppNotificationWidget`: Animated overlay notifications
- `NotificationOverlay`: Overlay management
- `NotificationPermissionDialog`: Permission request dialog

### 4. NotificationSettingsView
Complete settings screen for notification preferences:
- Permission status display
- Individual notification type toggles
- Test notification functionality
- FCM token display (debug)

## Usage

### Initialization
FCM is automatically initialized in `main.dart`:
```dart
await FCMService.initialize();
```

### Token Management
```dart
// Get current token
String? token = await FCMService.getCurrentToken();

// Update token for logged-in user
await FCMService.updateTokenForUser();

// Clear token on logout
await FCMService.clearTokenOnLogout();
```

### Topic Subscriptions
```dart
// Subscribe to topic
await FCMService.subscribeToTopic('events');

// Unsubscribe from topic
await FCMService.unsubscribeFromTopic('events');
```

### Navigation from Notifications
Notifications support automatic navigation based on data payload:
```json
{
  "type": "event",
  "event_id": "123"
}
```

Supported types:
- `event`: Navigate to event details
- `message`: Navigate to chat
- `profile`: Navigate to user profile
- Default: Navigate to home

### In-App Notifications
```dart
NotificationOverlay.show(
  context,
  notification,
  onTap: () => handleNotificationTap(),
  duration: Duration(seconds: 4),
);
```

## Backend Integration

### API Endpoints
The FCM service integrates with your Laravel backend:

1. **Token Update**: `POST /api/auth/edit_profile`
   ```json
   {
     "fb_token": "fcm_token_here"
   }
   ```

2. **Send Notification**: `POST /api/auth/notification`
   ```json
   {
     "id": 123,
     "title": "Notification Title",
     "body": "Notification Body"
   }
   ```

### Server-Side Implementation
Your backend already includes:
- `FcmService.php`: Server-side FCM sending
- `SendFcmNotification.php`: Laravel notification class
- User model with `fb_token` field

## Configuration

### Android Configuration
1. **Permissions** (AndroidManifest.xml):
   ```xml
   <uses-permission android:name="android.permission.WAKE_LOCK" />
   <uses-permission android:name="android.permission.VIBRATE" />
   <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
   ```

2. **FCM Metadata**:
   ```xml
   <meta-data
       android:name="com.google.firebase.messaging.default_notification_channel_id"
       android:value="gather_point_notifications" />
   ```

### iOS Configuration
AppDelegate.swift includes:
- Firebase configuration
- Notification permission requests
- Remote notification registration

## Testing

### Test Notifications
Use the notification settings screen to send test notifications:
1. Navigate to notification settings
2. Tap "اختبار الإشعارات" button
3. Verify in-app notification appears

### Debug Information
- FCM token is displayed in settings screen
- Console logs show all FCM operations
- Permission status is clearly indicated

## Best Practices

### 1. Token Management
- Tokens are automatically refreshed and synced
- Server receives updated tokens on user login
- Tokens are cleared on logout

### 2. User Experience
- Graceful permission requests with explanatory dialog
- Non-intrusive in-app notifications
- Comprehensive settings for user control

### 3. Error Handling
- All FCM operations include try-catch blocks
- Fallback to local notifications if needed
- Detailed logging for debugging

### 4. Performance
- Efficient token storage using SharedPreferences
- Minimal UI blocking operations
- Proper disposal of resources

## Troubleshooting

### Common Issues

1. **Notifications not received**:
   - Check permission status in settings
   - Verify FCM token is sent to server
   - Ensure app is not in battery optimization

2. **Navigation not working**:
   - Verify notification data payload format
   - Check NavigationService integration
   - Ensure routes are properly defined

3. **iOS notifications not working**:
   - Verify APNs certificate configuration
   - Check iOS notification permissions
   - Ensure proper Firebase project setup

### Debug Steps
1. Check FCM token in notification settings
2. Verify console logs for FCM operations
3. Test with notification settings test button
4. Check server logs for token updates

## Future Enhancements

### Planned Features
- [ ] Notification history storage
- [ ] Rich notifications with images
- [ ] Notification scheduling
- [ ] Advanced analytics
- [ ] Custom notification sounds

### Potential Improvements
- [ ] Notification categories
- [ ] Interactive notifications
- [ ] Notification grouping
- [ ] Deep linking integration

## Support

For issues or questions regarding the FCM implementation:
1. Check console logs for error messages
2. Verify Firebase project configuration
3. Test with the built-in test functionality
4. Review notification permissions and settings

This implementation provides a robust, user-friendly notification system that enhances the overall app experience while maintaining proper integration with your existing backend infrastructure.
