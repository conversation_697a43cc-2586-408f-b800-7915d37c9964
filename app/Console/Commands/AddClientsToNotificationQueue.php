<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Client;
use App\Models\ClientNotify;
use App\Models\Notification;
use Carbon\Carbon;

class AddClientsToNotificationQueue extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:SetClientsOnQueue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set client on notification queue';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $data = [];

        $notifications = Notification::where('sent', 0)->where('sending_time', '<=', Carbon::now())->get();

        foreach ($notifications as $notification) {

            $clients = $this->getClients($notification->place_filter, $notification->governorate_id, $notification->city_id, $notification->gender_filter, $notification->age_from, $notification->age_to);

            foreach ($clients as $client) {
                $data[] = ['client_id' => $client->id, 'notification_id' => $notification->id];
            }

            $chunk = array_chunk($data, 300);

            foreach ($chunk as $ch) :
                ClientNotify::insert($ch);
            endforeach;

            $notification->update(['sent' => 1]);
            $data = [];
        }
    }

    private function getClients($place_filter, $governorate_id, $city_id, $gender_filter, $age_from, $age_to) {

        $query = Client::select('clients.id');

        // place filter
        if ($place_filter != NULL) {
            if ($place_filter == 1) {
                // by governorate
                $query->join('addresses', 'clients.id', '=', 'addresses.client_id')
                        ->where('addresses.governorate_id', $governorate_id);
            } elseif ($place_filter == 2) {
                // by city
                $query->join('addresses', 'clients.id', '=', 'addresses.client_id')
                        ->where('addresses.city_id', $city_id);
            }
        }

        // gender filter
        if ($gender_filter != NULL) {
            if ($gender_filter == 1) {
                // male
                $query->where('gender', 1);
            } elseif ($place_filter == 2) {
                // female
                $query->where('gender', 2);
            }
        }

        // age filter
        if ($age_from != NULL && $age_to != NULL) {
            // between range
            $minDate = Carbon::today()->subYears($age_from)->format('Y');
            $maxDate = Carbon::today()->subYears($age_to)->format('Y');
            $query->whereYear('clients.birthdate', '<=', $minDate)->whereNotNull('clients.birthdate');
            $query->whereYear('clients.birthdate', '>=', $maxDate)->whereNotNull('clients.birthdate');
        } elseif ($age_from != NULL && $age_to == NULL) {
            // >= age
            $minDate = Carbon::today()->subYears($age_from)->format('Y');
            $query->whereYear('clients.birthdate', '<=', $minDate)->whereNotNull('clients.birthdate');
        } elseif ($age_from == NULL && $age_to != NULL) {
            // <= age
            $maxDate = Carbon::today()->subYears($age_to)->format('Y');
            $query->whereYear('clients.birthdate', '>=', $maxDate)->whereNotNull('clients.birthdate');
        }

        $clients = $query->whereNotNull('clients.fb_token')->get();

        return $clients;
    }

}
