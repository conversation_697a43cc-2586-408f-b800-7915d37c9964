<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Notification;
use Carbon\Carbon;
use App\Helpers\FCM;

class SendNotificationsToClients extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:SendNotificationsToClients';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Notifications To Clients';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $limit = 999;
        $clients = DB::table('client_notifies')
                ->join('clients', 'clients.id', '=', 'client_notifies.client_id')
                ->select('clients.fb_token', 'client_notifies.id', 'client_notifies.notification_id', 'client_notifies.client_id')
                ->where('sent', 0)
                ->limit($limit)
                ->get();
        $fb_tokens = [];
        foreach ($clients as $item) {
            $fb_tokens[$item->notification_id]['tokens'][] = $item->fb_token;
            $fb_tokens[$item->notification_id]['ids'][] = $item->id;
            if (!isset($fb_tokens[$item->notification_id]['notification'])) {
                $fb_tokens[$item->notification_id]['notification'] = Notification::find($item->notification_id);
            }
        }
        foreach ($fb_tokens as $nid => $v):
            //send push notif.
            $notifId = DB::table('notification_cont')->insertGetId(['created_at' => Carbon::now()]);
            $FCM_response = json_decode(FCM::fcm($v['tokens'], $v['notification']->title, strip_tags(html_entity_decode($v['notification']->body)), strip_tags(html_entity_decode($v['notification']->message)), 'info', $nid, 0, $notifId));
            $v['notification']->update(['success_count' => $v['notification']->success_count + $FCM_response->success, 'fail_count' => $v['notification']->fail_count + $FCM_response->failure]);
            DB::table('client_notifies')->where('notification_id', $nid)->whereIn('id', $v['ids'])->update(['sent' => 1]);
        endforeach;
    }

}
