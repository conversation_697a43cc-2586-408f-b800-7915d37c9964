<?php

namespace App\DataTables;

use App\Models\Admin\Attendance;
use Carbon\Carbon;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AttendanceDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->addColumn('actions', 'content.attendances.actions')
            ->editColumn('type', function ($row) {
                $types = [1 => __('check_in'), 2 => __('check_out')];
                return $types[$row->type];
            })
            ->editColumn('updated_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->updated_at));
            })
            ->rawColumns(['actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\Attendance $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Attendance $model)
    {
        $user = auth()->user();
        $allData = $user ? $user->can('read all_data') : false;
        if (!$allData):
            $superAdmin = $user ? $user->hasRole('super_admin') : false;
            $fetch = $model->with('user')->where('user_id', $user->id);
            if (!$superAdmin):
                $start = Carbon::now()->subWeek();
                $end = Carbon::now();
                $fetch->whereBetween('created_at', [$start, $end]);
            endif;
            return $fetch->newQuery();
        else:
            return $model->with('user')->newQuery();
        endif;
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    // end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('attendances-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(4, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(false)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $cols = [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('user.name')->defaultContent('')->addClass('text-center')->title(__('name')),
            Column::make('date')->defaultContent('')->addClass('text-center')->title(__('date')),
            Column::make('type')->defaultContent('')->addClass('text-center')->title(__('type')),
            Column::make('updated_at')->addClass('text-center')->title(__('updated_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
        return $cols;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Attendance_' . date('YmdHis');
    }

}
