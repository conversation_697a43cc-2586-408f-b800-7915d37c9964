<?php
namespace App\DataTables;

use App\Models\Admin\Facilities;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class FacilitiesDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->editColumn('icon', function ($row) {
                if ($row->icon) {
                    return '<img src="' . asset('storage/' . $row->icon) . '" alt="Icon" style="max-width: 50px;" />';
                }
                return __('no_icon');
            })
            ->editColumn('have_count', function ($row) {
                return __($row->have_count ? 'yes' : 'no');
            })
            ->addColumn('actions', 'content.facilities.actions')
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['actions', 'icon']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\ServiceCategory $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Facilities $model)
    {
        return $model->newQuery();
    }

    /**
     * Get language file for datatable.
     *
     * @return string
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('facilities-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(5, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('icon')->addClass('text-center')->title(__('icon')),
            Column::make('title_' . __('lang'))->addClass('text-center')->title(__('title')),
            Column::make('order')->addClass('text-center')->title(__('order')),
            Column::make('have_count')->addClass('text-center')->title(__('have_count')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Facilities_' . date('YmdHis');
    }
}
