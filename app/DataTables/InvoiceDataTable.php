<?php

namespace App\DataTables;

use App\Models\Admin\Invoice;
use Carbon\Carbon;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class InvoiceDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        $lang = app()->getLocale();
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->addColumn('actions', 'content.invoices.actions')
            ->editColumn('category.title_' . $lang, function ($row) {
                $colors = ['success', 'danger'];
                return $row->category ? '<span class="badge bg-' . ($row->category->id !== 3 ? (isset($colors[$row->category->deduct]) ? $colors[$row->category->deduct] : 'primary') : 'primary') . '">' . ($row->category->id !== 3 ? ($row->category->deduct ? '-' : '+') : '') . '</span>' . ' ' . $row->category->{'title_' . __('lang')} : '';
            })
            ->editColumn('transaction', function ($row) {
                $info = '';
                if (!empty($row->relationmodel)) {
                    switch (get_class($row->relationmodel)):
                case 'App\Models\Admin\Transactions':
                    if ($row->relationmodel->products) {
                            foreach ($row->relationmodel->products as $product) {
                                if (!empty($product->item)) {
                                    $info .= '<div><small>' . $product->item->{'title_' . __('lang')} . '</small></div>';
                                }
                            }
                    }
                    $info .= '<div><small>' . $row->relationmodel->number . '</small></div>';
                    $info .= '<div><small>' . $row->relationmodel->total . ' ' . __('currency') . '</small></div>';
                    break;
                    endswitch;
                }
                return $info;
            })
            ->editColumn('status', function ($row) {
                $colors = ['warning', 'success', 'danger', 'warning'];
                return '<span class="badge bg-' . $colors[$row->status] . '">' . __('invoice_status_' . $row->status) . '</span>';
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['category.title_' . $lang, 'transaction', 'status', 'actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\Invoice $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Invoice $model)
    {
        $fetch = $model->select('invoices.*')->with(['category', 'relationmodel', 'user', 'payments']);
        $user = auth()->user();
        $allData = $user ? $user->can('read all_data') : false;
        $superAdmin = $user ? $user->hasRole('super_admin') : false;
        if (!$superAdmin):
            $start = Carbon::now()->subWeek();
            $end = Carbon::now();
            $fetch->whereBetween('created_at', [$start, $end]);
        endif;
        if (!$allData):
            $fetch->where('user_id', $user->user_id);
        endif;
        return $fetch->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    // end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('Invoice-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(8, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(false)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $lang = app()->getLocale();
        $cols = [
            Column::make('number')->title('#'),
            Column::make('category.title_' . $lang)->defaultContent('')->title(__('financial_category')),
            Column::make('transaction')->defaultContent('')->addClass('text-center')->title(__('transaction'))->searchable(false)->orderable(false),
            Column::make('relationmodel.number')->defaultContent('')->addClass('text-center')->title(__('number'))->orderable(false),
            Column::make('amount')->addClass('text-center')->title(__('amount')),
            Column::make('paid')->addClass('text-center')->title(__('paid')),
            Column::make('remaining')->addClass('text-center')->title(__('remaining')),
            Column::make('status')->addClass('text-center')->title(__('status')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::make('comment')->addClass('text-center')->title(__('comment')),
            Column::make('user.name')->defaultContent('')->addClass('text-center')->title(__('user')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
        return $cols;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Invoice_' . date('YmdHis');
    }

}
