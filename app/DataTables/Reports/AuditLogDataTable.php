<?php

namespace App\DataTables\Reports;

use App\Models\AuditLog;
use Carbon\Carbon;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AuditLogDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        $lang = app()->getLocale();
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->editColumn('type', function ($row) {
                return __("log_type_{$row->type}");
            })
            ->editColumn('transaction', function ($row) {
                $info = '';
                if (!empty($row->relationmodel)) {
                    switch (get_class($row->relationmodel)):
                case 'App\Models\Admin\Transactions':
                    $info .= '<div><small>' . $row->relationmodel->number . '</small></div>';
                    $info .= '<div><small>' . __('order_total') . ': ' . $row->relationmodel->order_total . ' ' . __('currency') . '</small></div>';
                    $info .= '<div><small>' . __('order_collect') . ': ' . $row->relationmodel->collected . ' ' . __('currency') . '</small></div>';
                    break;
                    endswitch;
                }
                return $info;
            })
            ->editColumn('created_at', function ($row) {
                    return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['transaction']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\AuditLog $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(AuditLog $model)
    {
        $fetch = $model->select('audit_logs.*')->with(['relationmodel', 'user']);
        $user = auth()->user();
        $allData = $user ? $user->can('read all_data') : false;
        $superAdmin = $user ? $user->hasRole('super_admin') : false;
        if (!$superAdmin):
            $start = Carbon::now()->subWeek();
            $end = Carbon::now();
            $fetch->whereBetween('created_at', [$start, $end]);
        endif;
        if (!$allData):
            $fetch->where('user_id', $user->id);
        endif;
        if ($this->from_date):
            $fetch->where('created_at', '>=', $this->from_date);
        endif;
        if ($this->to_date):
            $fetch->where('created_at', '<=', $this->to_date);
        endif;
        if ($this->user_id):
            $fetch->where('user_id', $this->user_id);
        endif;
        if ($this->type):
            $fetch->where('type', $this->type);
        endif;
        return $fetch->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    // end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('AuditLog-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(5, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(false)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $lang = app()->getLocale();
        $cols = [
            Column::make('id')->title('#'),
            Column::make('type')->defaultContent('')->addClass('text-center')->title(__('type')),
            Column::make('description')->addClass('text-center')->title(__('description')),
            Column::make('transaction')->defaultContent('')->addClass('text-center')->title(__('transaction'))->searchable(false)->orderable(false),
            Column::make('user.name')->defaultContent('')->addClass('text-center')->title(__('user')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
        ];
        return $cols;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'AuditLog_' . date('YmdHis');
    }

}
