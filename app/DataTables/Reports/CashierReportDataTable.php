<?php

namespace App\DataTables\Reports;

use App\Models\Admin\Invoice;
use Carbon\Carbon;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class CashierReportDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        $lang = app()->getLocale();
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->editColumn('transaction', function ($row) {
                $info = '';
                if (!empty($row->relationmodel)) {
                    switch (get_class($row->relationmodel)):
                        case 'App\Models\Admin\Transactions':
                            $info .= '<div><small>' . $row->relationmodel->number . '</small></div>';
                            $info .= '<div><small>' . __('order_total') . ': ' . $row->relationmodel->order_total . ' ' . __('currency') . '</small></div>';
                            $info .= '<div><small>' . __('order_collect') . ': ' . $row->relationmodel->collected . ' ' . __('currency') . '</small></div>';
                            break;
                    endswitch;
                }
                return $info;
            })
            ->editColumn('status', function ($row) {
                    $colors = ['warning', 'success', 'danger', 'warning'];
                    return '<span class="badge bg-' . $colors[$row->status] . '">' . __('invoice_status_' . $row->status) . '</span>';
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['transaction', 'status']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\Invoice $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Invoice $model)
    {
        $fetch = $model->with(['category', 'user', 'relationmodel']);
        $user = auth()->user();
        $superAdmin = $user ? $user->hasRole('super_admin') : false;
        if (!$superAdmin):
            $start = Carbon::now()->subWeek();
            $end = Carbon::now();
            $fetch->whereBetween('created_at', [$start, $end]);
        endif;
        if ($this->from_date):
            $fetch->where('created_at', '>=', $this->from_date);
        endif;
        if ($this->to_date):
            $fetch->where('created_at', '<=', $this->to_date);
        endif;
        if ($this->user_id):
            $fetch->where('user_id', $this->user_id);
        endif;
        if ($this->financial_category_id):
            $fetch->where('financial_category_id', $this->financial_category_id);
        endif;
        if ($this->type):
            $fetch->whereHas('category', function ($query) {
                $query->where('deduct', $this->type);
                $query->whereNotIn('id', [1, 2, 3, 6]);
            });
        endif;
        if ($this->status !== null):
            $fetch->where('status', $this->status);
        endif;
        return $fetch->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    // end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('cashier-report-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(9, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(false)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $lang = app()->getLocale();
        $cols = [
            Column::make('number')->addClass('text-center')->title('#'),
            Column::make('category.title_' . $lang)->defaultContent('')->addClass('text-center')->title(__('financial_category')),
            Column::make('transaction')->addClass('text-center')->title(__('transaction')),
            Column::make('relationmodel.number')->defaultContent('')->addClass('text-center')->title(__('number'))->orderable(false),
            Column::make('amount')->addClass('text-center')->title(__('total'))->searchable(false),
            Column::make('paid')->addClass('text-center')->title(__('paid'))->searchable(false),
            Column::make('remaining')->addClass('text-center')->title(__('remaining'))->searchable(false),
            Column::make('comment')->addClass('text-center')->title(__('comment')),
            Column::make('status')->addClass('text-center')->title(__('status'))->searchable(false),
            Column::make('created_at')->addClass('text-center')->title(__('created_at'))->searchable(false),
            Column::make('user.name')->defaultContent('')->addClass('text-center')->title(__('user'))->searchable(false),
        ];
        return $cols;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'CashierReport_' . date('YmdHis');
    }

}
