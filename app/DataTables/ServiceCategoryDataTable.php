<?php
namespace App\DataTables;

use App\Models\Admin\ServiceCategory;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServiceCategoryDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->editColumn('icon', function ($row) {
                if ($row->icon) {
                    return '<img src="' . asset('storage/' . $row->icon) . '" alt="Icon" style="max-width: 50px;" />';
                }
                return __('no_icon');
            })
            ->editColumn('reservation_type', function ($row) {
                return __('reservation_type_' . $row->reservation_type);
            })
            ->addColumn('actions', 'content.service_categories.actions')
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['actions', 'icon']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\ServiceCategory $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(ServiceCategory $model)
    {
        return $model->with(['userType', 'parentCategory'])->newQuery();
    }

    /**
     * Get language file for datatable.
     *
     * @return string
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('service-categories-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(7, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('icon')->addClass('text-center')->title(__('icon')),
            Column::make('title_' . __('lang'))->addClass('text-center')->title(__('title')),
            Column::make('user_type.title_' . __('lang'))->defaultContent('')->addClass('text-center')->title(__('user_type')),
            Column::make('parent_category.title_' . __('lang'))->defaultContent('')->addClass('text-center')->title(__('parent_category')),
            Column::make('order')->addClass('text-center')->title(__('order')),
            Column::make('reservation_type')->addClass('text-center')->title(__('reservation_type')),
            Column::make('commission')->addClass('text-center')->title(__('commission')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'ServiceCategories_' . date('YmdHis');
    }
}
