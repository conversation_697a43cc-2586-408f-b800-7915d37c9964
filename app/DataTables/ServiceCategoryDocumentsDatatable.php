<?php

namespace App\DataTables;

use App\Models\Admin\ServiceCategoryDocument;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServiceCategoryDocumentsDatatable extends DataTable
{
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addColumn('actions', 'content.service_categories_documents.actions')
            ->editColumn('icon', function ($row) {
                if ($row->icon) {
                    return '<img src="' . asset('storage/' . $row->icon) . '" alt="Icon" style="max-width: 50px;" />';
                }
                return __('no_icon');
            })
            ->editColumn('example', function ($row) {
                if ($row->example) {
                    return '<a href="' . asset('storage/' . $row->example) . '" target="_blank">' . __('view_example') . '</a>';
                }
                return __('no_example');
            })
            ->editColumn('display', function ($row) {
                return $row->display ? __('yes') : __('no');
            })
            ->editColumn('required', function ($row) {
                return $row->required ? __('yes') : __('no');
            })
            ->editColumn('multiple', function ($row) {
                return $row->multiple ? __('yes') : __('no');
            })
            ->rawColumns(['icon', 'example', 'actions']);
    }

    public function query(ServiceCategoryDocument $model)
    {
        return $model->newQuery();
    }

    public function html()
    {
        return $this->builder()
            ->setTableId('service-category-documents-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(1)
            ->buttons(
                Button::make('excel'),
                Button::make('print')
            );
    }

    protected function getColumns()
    {
        return [
            Column::make('id')->title('#'),
            Column::make('title_' . app()->getLocale())->title(__('title')),
            Column::make('icon')->title(__('icon')),
            Column::make('example')->title(__('example')),
            Column::make('display')->title(__('display')),
            Column::make('required')->title(__('required')),
            Column::make('multiple')->title(__('multiple')),
            Column::computed('actions')->title(__('actions'))->exportable(false)->printable(false),
        ];
    }
}