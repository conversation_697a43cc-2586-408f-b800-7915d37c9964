<?php

namespace App\DataTables;

use App\Models\Admin\ServiceCategoryField;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServiceCategoryFieldsDatatable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->addColumn('actions', 'content.service_categories_fields.actions')
            ->editColumn('type', function ($row) {
                $types = [
                    1 => __('text'),
                    2 => __('numeric'),
                    3 => __('numeric_decimal'),
                    4 => __('phone'),
                    5 => __('password_text'),
                    6 => __('password_numeric'),
                    7 => __('checkbox'),
                    8 => __('radio'),
                    9 => __('text_area'),
                    10 => __('date'),
                    11 => __('date_time'),
                    12 => __('time'),
                    13 => __('date_from_to'),
                    14 => __('date_time_from_to'),
                ];
                return $types[$row->type] ?? __('unknown');
            })
            ->editColumn('icon', function ($row) {
                if ($row->icon) {
                    return '<img src="' . asset('storage/' . $row->icon) . '" alt="Icon" style="max-width: 50px;" />';
                }
                return __('no_icon');
            })
            ->editColumn('display', function ($row) {
                return $row->display ? __('yes') : __('no');
            })
            ->editColumn('required', function ($row) {
                return $row->required ? __('yes') : __('no');
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['icon', 'actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\ServiceCategoryField $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(ServiceCategoryField $model)
    {
        return $model->with(['options'])->newQuery();
    }

    /**
     * Get language file for datatable.
     *
     * @return string
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('service-categories-fields-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(6, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('all')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('excel')),
                Button::make('print')->text(__('print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('title_' . __('lang'))->addClass('text-center')->title(__('title')),
            Column::make('type')->addClass('text-center')->title(__('type')),
            Column::make('icon')->addClass('text-center')->title(__('icon')),
            Column::make('display')->addClass('text-center')->title(__('display')),
            Column::make('required')->addClass('text-center')->title(__('required')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'ServiceCategoryFields_' . date('YmdHis');
    }
}