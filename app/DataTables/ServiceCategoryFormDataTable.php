<?php
namespace App\DataTables;

use App\Models\Admin\ServiceCategoryForm;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServiceCategoryFormDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->editColumn('type', function ($row) {
                $types = [
                    1 => __('registration_form'),
                    2 => __('add_form'),
                    3 => __('reservation_form'),
                ];
                return $types[$row->type] ?? __('unknown');
            })
            ->addColumn('actions', function ($row) {
                return view('content.service_categories_forms.actions', [
                    'item' => $row,
                    'service_category_id' => $row->service_category_id, // Pass service_category_id
                ]);
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\ServiceCategoryForm $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(ServiceCategoryForm $model)
    {
        // Get the service_category_id
        $serviceCategoryId = $this->id;
        // Filter forms by service_category_id
        return $model->with(['serviceCategory'])
            ->where('service_category_id', $serviceCategoryId)
            ->newQuery();
    }

    /**
     * Get language file for datatable.
     *
     * @return string
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('service-category-forms-table') // Updated table ID
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(3, 'desc') // Updated order column index
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('service_category.title_' . __('lang'))->defaultContent('')->addClass('text-center')->title(__('service_category')),
            Column::make('type')->addClass('text-center')->title(__('form_type')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'ServiceCategoryForms_' . date('YmdHis'); // Updated filename
    }
}
