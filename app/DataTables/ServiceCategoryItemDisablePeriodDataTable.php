<?php
namespace App\DataTables;

use App\Models\Admin\ServiceCategoryItemDisablePeriod;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServiceCategoryItemDisablePeriodDataTable extends DataTable
{
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->editColumn('disable_start', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->disable_start));
            })
            ->editColumn('disable_end', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->disable_end));
            })
            ->addColumn('actions', function ($row) {
                return view('content.service_category_item_disable_periods.actions', [
                    'item'                     => $row,
                    'service_category_item_id' => $row->service_category_item_id,
                ]);
            })
            ->rawColumns(['actions']);
    }

    public function query(ServiceCategoryItemDisablePeriod $model)
    {
        $serviceCategoryItemId = $this->id;
        return $model->with(['serviceCategoryItem'])
            ->where('service_category_item_id', $serviceCategoryItemId)
            ->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    public function html()
    {
        return $this->builder()
            ->setTableId('service-category-item-disable-periods-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(3, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('disable_start')->addClass('text-center')->title(__('disable_start')),
            Column::make('disable_end')->addClass('text-center')->title(__('disable_end')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    protected function filename(): string
    {
        return 'ServiceCategoryItemDisablePeriods_' . date('YmdHis');
    }
}
