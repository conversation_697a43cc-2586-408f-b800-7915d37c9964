<?php
namespace App\DataTables;

use App\Models\Admin\ServiceCategoryItemOffer;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServiceCategoryItemOfferDataTable extends DataTable
{
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->editColumn('type', function ($row) {
                $types = [
                    1 => __('percentage'),
                    2 => __('fixed'),
                ];
                return $types[$row->type] ?? __('unknown');
            })
            ->editColumn('apply_to', function ($row) {
                $apply_to = [
                    1 => __('price'),
                    2 => __('weekend_price'),
                ];
                return $apply_to[$row->apply_to] ?? __('all');
            })
            ->editColumn('offer_start', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->offer_start));
            })
            ->editColumn('offer_end', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->offer_end));
            })
            ->addColumn('actions', function ($row) {
                return view('content.service_category_item_offers.actions', [
                    'item'                     => $row,
                    'service_category_item_id' => $row->service_category_item_id,
                ]);
            })
            ->rawColumns(['actions']);
    }

    public function query(ServiceCategoryItemOffer $model)
    {
        $serviceCategoryItemId = $this->id;
        return $model->with(['serviceCategoryItem'])
            ->where('service_category_item_id', $serviceCategoryItemId)
            ->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    public function html()
    {
        return $this->builder()
            ->setTableId('service-category-item-offers-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(3, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('title_' . app()->getLocale())->addClass('text-center')->title(__('title')),
            Column::make('value')->addClass('text-center')->title(__('value')),
            Column::make('type')->addClass('text-center')->title(__('type')),
            Column::make('apply_to')->addClass('text-center')->title(__('apply_to')),
            Column::make('offer_start')->addClass('text-center')->title(__('offer_start')),
            Column::make('offer_end')->addClass('text-center')->title(__('offer_end')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    protected function filename(): string
    {
        return 'ServiceCategoryItemOffers_' . date('YmdHis');
    }
}
