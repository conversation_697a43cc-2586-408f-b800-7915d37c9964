<?php
namespace App\DataTables;

use App\Models\Admin\ServiceCategoryItemReservation;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class TransactionsDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->addColumn('actions', content: 'content.transactions.actions')
            ->editColumn('confirmed', function ($row) {
                return $row->confirmed ? __('yes') : __('no');
            })
            ->editColumn('reservation_date', function ($row) {
                return date('Y-m-d', strtotime($row->reservation_date));
            })
            ->editColumn('reservation_from', function ($row) {
                return $row->reservation_from ? $row->reservation_from->format('Y-m-d H:i') : '';
            })
            ->editColumn('reservation_to', function ($row) {
                return $row->reservation_to ? $row->reservation_to->format('Y-m-d H:i') : '';
            })
            ->rawColumns(['actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\ServiceCategoryItemReservation $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(ServiceCategoryItemReservation $model)
    {
        return $model->with(['serviceCategoryItem', 'user'])
            ->newQuery();
    }

    /**
     * Get language file for datatable.
     *
     * @return string
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('service-category-item-reservations-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(5, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('service_category_item.title')->defaultContent('')->addClass('text-center')->title(__('service_category_item')),
            Column::make('user.name')->defaultContent('')->addClass('text-center')->title(__('user')),
            Column::make('reservation_date')->addClass('text-center')->title(__('reservation_date')),
            Column::make('reservation_from')->addClass('text-center')->title(__('reservation_from')),
            Column::make('reservation_to')->addClass('text-center')->title(__('reservation_to')),
            Column::make('confirmed')->addClass('text-center')->title(__('confirmed')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Transactions_' . date('YmdHis');
    }
}
