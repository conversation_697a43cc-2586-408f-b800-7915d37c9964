<?php

namespace App\DataTables;

use App\Models\Admin\Transactions;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class TransactionsDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return !empty($row->invoice->status) && $row->invoice->status == 3 ? (config('custom.custom.myStyle') == 'dark' ? 'bg-warning text-black' : 'bg-primary text-black') : (config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '');
            })
            ->addColumn('actions', 'content.transactions.actions')
            ->editColumn('status', function ($row) {
                return __('status_' . $row->status);
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\FinancialCategory $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Transactions $model)
    {
        $fetch = $model->with(['user', 'invoice'])->select('transactions.*');
        $user = auth()->user();
        $allData = $user ? $user->can('read all_data') : false;
        if (!$allData):
            if (in_array($user->user_type_id, [3, 4, 5])) {
                if ($user->user_type_id == 5) {
                    $fetch->where('status', 2);
                } else {
                    $fetch->whereHas('user', function ($q) use ($user) {
                        $q->where($user->main_account_id ? 'user_id' : 'main_account_id', $user->id);
                    });
                }
            } else {
                $fetch->where('user_id', $user->id);
            }
        endif;
        if ($this->from_date):
            $fetch->where('created_at', '>=', $this->from_date);
        endif;
        if ($this->to_date):
            $fetch->where('created_at', '<=', $this->to_date);
        endif;
        if ($this->user_id):
            $fetch->where('user_id', $this->user_id);
        endif;
        if ($this->type):
            $fetch->where('type', $this->type);
        endif;
        return $fetch->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    // end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('transactions-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(4, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('number')->addClass('text-center')->title(__('number')),
            Column::make('total')->addClass('text-center')->title(__('total')),
            Column::make('status')->addClass('text-center')->title(__('status')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::make('user.name')->defaultContent('')->addClass('text-center')->title(__('user')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Transactions_' . date('YmdHis');
    }

}
