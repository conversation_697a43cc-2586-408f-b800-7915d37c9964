<?php

namespace App\DataTables;

use App\Models\User;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class UsersDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->addColumn('actions', 'content.users.actions')
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->editColumn('roles', function ($row) {
                $roles = $row->roles;
                $rolesContainer = "";
                if ($roles) {
                    foreach ($roles as $role) {
                        $rolesContainer .= ucwords($role->name) . '<br>';
                    }
                }
                return $rolesContainer ?? [];
            })
            ->rawColumns(['actions', 'roles']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\User $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(User $model)
    {
        $user = auth()->user();
        $fetch = $model->with(['roles', 'type']);
        //$fetch->where('id', '!=', $user->id);
        // Apply user type filter if provided
        if (request()->has('user_type') && request('user_type')) {
            $fetch->where('user_type_id', request('user_type'));
        }
        return $fetch->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    // end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('users-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(false)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $cols = [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('name')->addClass('text-center')->title(__('name')),
            Column::make('email')->addClass('text-center')->title(__('email')),
            Column::make('phone')->addClass('text-center')->title(__('phone')),
            Column::make('type.title_' . __('lang'))->defaultContent('')->addClass('text-center')->title(__('user_type')),
            Column::make('roles')
                ->sortable(false)
                ->searchable(false)->addClass('text-center')->title(__('role')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
        return $cols;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Users_' . date('YmdHis');
    }

}
