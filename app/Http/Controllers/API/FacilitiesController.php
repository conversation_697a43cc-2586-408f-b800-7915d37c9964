<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\FacilitiesResource;
use App\Models\Admin\Facilities;
use Illuminate\Http\Request;

class FacilitiesController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get facilities list.
     *
     * @return \Illuminate\Http\Response
     */
    public function get_list(Request $request)
    {
        $list = Facilities::orderBy('order')->get();
        // Return json response
        return $this->ApiResponse(
            ! empty($list) ? true : false,                                                 // Response status (Boolean)
            trans('api.' . (! empty($list) ? 'data_retrieved_success' : 'no_data_found')), // Response message (String)
            FacilitiesResource::collection($list),                                   // Response data (Array)
            ! empty($list) ? 200 : 404,                                                    // HTTP response status code (Integer)
        );
    }
}
