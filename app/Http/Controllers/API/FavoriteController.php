<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\FavoriteResource;
use App\Models\Admin\ServiceCategoryItemFavorite;
use Illuminate\Http\Request;

class FavoriteController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get favorite list.
     *
     * @return \Illuminate\Http\Response
     */
    public function list(Request $request)
    {
        $user = auth('api')->user();
        $list = ServiceCategoryItemFavorite::where('user_id', $user->id)->get();
        // Return json response
        return $this->ApiResponse(
            ! empty($list) ? true : false,                                                 // Response status (Boolean)
            trans('api.' . (! empty($list) ? 'data_retrieved_success' : 'no_data_found')), // Response message (String)
            FavoriteResource::collection($list),                                          // Response data (Array)
            ! empty($list) ? 200 : 404,                                                    // HTTP response status code (Integer)
        );
    }

    /**
     * Set item favorite.
     *
     * @return \Illuminate\Http\Response
     */
    public function set(Request $request)
    {
        $request->validate([
            'service_category_item_id' => 'required|integer|exists:service_category_items,id',
        ]);
        $user  = auth('api')->user();
        $fav = ServiceCategoryItemFavorite::where(['user_id' => $user->id, 'service_category_item_id' => $request->service_category_item_id])->first();
        if ($fav) {
            $fav->delete();
        } else {
            $fav = ServiceCategoryItemFavorite::create(['user_id' => $user->id, 'service_category_item_id' => $request->service_category_item_id]);
        }
        // Return json response
        return $this->ApiResponse(
            ! empty($fav) ? true : false,                                                   // Response status (Boolean)
            trans('api.' . (! empty($fav) ? 'data_submitted_success' : 'something_wrong')), // Response message (String)
            [],                                                                            // Response data (Array)
            ! empty($fav) ? 200 : 422,                                                      // HTTP response status code (Integer)
        );
    }

}
