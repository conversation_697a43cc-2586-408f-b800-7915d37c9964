<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\CityResource;
use App\Http\Resources\ServiceCategoryItemResource;
use App\Models\Admin\City;
use App\Models\Admin\ServiceCategoryItem;
use Illuminate\Http\Request;

class GeneralController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get cities list based on geolocation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function cities_geo(Request $request)
    {
        // Validate request parameters
        $request->validate([
            'lat' => ['required',
                'numeric',
                'between:-90,90',
                'regex:/^[-]?((([0-8]?[0-9])(\.(\d{1,8}))?)|(90(\.0{1,8})?))$/'],
            'lng' => ['required',
                'numeric',
                'between:-180,180',
                'regex:/^[-]?((((1[0-7][0-9])|([0-9]?[0-9]))(\.(\d{1,8}))?)|180(\.0{1,8})?)$/',
            ],
        ]);

        $lat = $request->input('lat');
        $lng = $request->input('lng');

        $radiusKm = 50;

        // Get all cities with distance calculation
        $cities = City::selectRaw('*,
            (6371 * acos(cos(radians(?))
            * cos(radians(lat))
            * cos(radians(lon) - radians(?))
            + sin(radians(?))
            * sin(radians(lat)))) AS distance',
            [$lat, $lng, $lat]
        )->orderBy('distance')->get();

        // Determine current city (closest one)
        $currentCity = $cities->firstWhere('distance', '<=', value: $radiusKm);

        // Filter nearby cities within 100km radius (optional)
        //$nearbyCities = $cities->where('distance', '<=', 100);

        return $this->ApiResponse(
            $cities->isNotEmpty(),
            trans('api.' . ($cities->isNotEmpty() ? 'data_retrieved_success' : 'no_data_found')),
            [
                'cities'       => CityResource::collection($cities),
                //'nearby_cities' => CityResource::collection($nearbyCities),
                'current_city' => $currentCity ? new CityResource($currentCity) : null,
            ],
            $cities->isNotEmpty() ? 200 : 404
        );
    }
}
