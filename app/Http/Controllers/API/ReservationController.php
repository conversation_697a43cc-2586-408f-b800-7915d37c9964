<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\ReservationResource;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\Admin\ServiceCategoryItemReservation;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ReservationController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Check for reservation and get information.
     *
     * @return \Illuminate\Http\Response
     */
    public function check(Request $request)
    {
        // Validate incoming request
        $request->validate([
            'service_category_item_id' => 'required|integer|exists:service_category_items,id',
            'reservation_from'         => 'required|date_format:Y-m-d H:i:s',
            'reservation_to'           => 'required|date_format:Y-m-d H:i:s|after:reservation_from',
        ]);

        // التأكد من توفر الفترة للحجز أولًا
        $isAvailable = ServiceCategoryItemReservation::isPeriodAvailable(
            $request->service_category_item_id,
            $request->reservation_from,
            $request->reservation_to
        );

        if (! $isAvailable) {
            return response()->json([
                'success' => false,
                'message' => 'The selected period is already booked.',
            ], 409); // 409 Conflict إذا كانت الفترة محجوزة
        }

        // احضار بيانات العنصر بعد التأكد من توفر الفترة
        $item = ServiceCategoryItem::with('serviceCategory')->findOrFail($request->service_category_item_id);

        // حساب عدد الأيام
        $from = Carbon::parse($request->reservation_from);
        $to   = Carbon::parse($request->reservation_to);
        $days = $from->diffInDays($to) ?: 1; // إذا كان نفس اليوم، نعتبرها يوم واحد

                            // اختيار السعر المناسب
        $priceDetails = []; // لتخزين تفاصيل الأسعار المستخدمة
        if ($days >= 30 && $item->month_price) {
            $price                          = $item->month_price * ($days / 30);
            $priceDetails['price_type']     = 'monthly';
            $priceDetails['price_per_unit'] = $item->month_price;
        } elseif ($days >= 7 && $item->week_price) {
            $price                          = $item->week_price * ($days / 7);
            $priceDetails['price_type']     = 'weekly';
            $priceDetails['price_per_unit'] = $item->week_price;
        } else {
            // حساب السعر اليومي مع مراعاة عطلة نهاية الأسبوع
            $price       = 0;
            $weekendDays = 0;
            $normalDays  = 0;

            for ($date = $from->copy(); $date < $to; $date->addDay()) {
                if ($date->isWeekend() && $item->weekend_price) {
                    $price += $item->weekend_price;
                    $weekendDays++;
                } else {
                    $price += $item->price;
                    $normalDays++;
                }
            }

            $priceDetails['price_type']    = 'daily';
            $priceDetails['daily_price']   = $item->price;
            $priceDetails['weekend_price'] = $item->weekend_price;
            $priceDetails['normal_days']   = $normalDays;
            $priceDetails['weekend_days']  = $weekendDays;
        }

        // حساب العمولة
        $commissionRate = $item->serviceCategory->commission ?? 0;
        $commission     = ($price * $commissionRate) / 100;

        // تحضير الاستجابة بعد التحقق من التوفر وحساب السعر
        return response()->json([
            'success' => true,
            'message' => 'The selected period is available for reservation.',
            'data'    => [
                'unit_title'    => $item->title,
                'total_days'    => $days,
                'price'         => round($price, 2),
                'commission'    => round($commission, 2),
                'final_price'   => round($price + $commission, 2),
                'price_details' => $priceDetails,
            ],
        ], 200);
    }

    /**
     * Create reservation
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        // Authenticate user
        $user = auth('api')->user();

        // Validate incoming request
        $request->validate([
            'service_category_item_id' => 'required|integer|exists:service_category_items,id',
            'reservation_from'         => 'required|date_format:Y-m-d H:i:s',
            'reservation_to'           => 'required|date_format:Y-m-d H:i:s|after:reservation_from',
        ]);

        // Check availability
        $isAvailable = ServiceCategoryItemReservation::isPeriodAvailable(
            $request->service_category_item_id,
            $request->reservation_from,
            $request->reservation_to
        );

        if (! $isAvailable) {
            return response()->json([
                'success' => false,
                'message' => trans('api.reservation_unavailable'),
            ], 409);
        }

        // Create reservation
        $reservation = ServiceCategoryItemReservation::create([
            'service_category_item_id' => $request->service_category_item_id,
            'reservation_date'         => Carbon::now(),
            'reservation_from'         => $request->reservation_from,
            'reservation_to'           => $request->reservation_to,
            'confirmed'                => false, // Default as unconfirmed until user confirms
            'user_id'                  => $user->id,
        ]);

        return response()->json([
            'success' => true,
            'message' => trans('api.reservation_created_successfully'),
            'data'    => $reservation,
        ], 201);
    }

    /**
     * Get reservations list.
     *
     * @return \Illuminate\Http\Response
     */
    public function list(Request $request)
    {
        $user = auth('api')->user();
        $list = ServiceCategoryItemReservation::with(['serviceCategoryItem', 'documents', 'fields'])->where('user_id', $user->id)->get();
        // Return json response
        return $this->ApiResponse(
            ! empty($list) ? true : false,                                                 // Response status (Boolean)
            trans('api.' . (! empty($list) ? 'data_retrieved_success' : 'no_data_found')), // Response message (String)
            ReservationResource::collection($list),                                       // Response data (Array)
            ! empty($list) ? 200 : 404,                                                    // HTTP response status code (Integer)
        );
    }

}
