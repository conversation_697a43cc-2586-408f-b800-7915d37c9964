<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\ServiceCategoryResource;
use App\Models\Admin\ServiceCategory;
use Illuminate\Http\Request;

class ServiceCategoryController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get items list.
     *
     * @return \Illuminate\Http\Response
     */
    public function get_list(Request $request)
    {
        $request->validate([
            'service_category_id' => 'nullable|integer|exists:service_categories,id',
        ]);
        $parent = $request->service_category_id ? $request->service_category_id : null;
        $list = ServiceCategory::where('parent', $parent)->get();
        // Return json response
        return $this->ApiResponse(
            ! empty($list) ? true : false,                                                 // Response status (Boolean)
            trans('api.' . (! empty($list) ? 'data_retrieved_success' : 'no_data_found')), // Response message (String)
            ServiceCategoryResource::collection($list),                                   // Response data (Array)
            ! empty($list) ? 200 : 404,                                                    // HTTP response status code (Integer)
        );
    }
}
