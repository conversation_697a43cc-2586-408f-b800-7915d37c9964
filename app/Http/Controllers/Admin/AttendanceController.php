<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin\Attendance;
use App\Models\User;
use Illuminate\Http\Request;
use App\DataTables\AttendanceDataTable;
use App\Models\AuditLog;
use Illuminate\Support\Carbon;

class AttendanceController extends Controller
{
    private const NAME = 'attendances';
    private const NAME_S = 'attendance';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(AttendanceDataTable $dataTable)
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        $users = User::all();
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'users'));
    }

    /**
       * Store a newly created resource in storage.
       *
       * @param  \Illuminate\Http\Request  $request
       * @return \Illuminate\Http\Response
       */
    public function store(Request $request)
    {
        $data = $request->validate([
            'user_id' => 'required|integer',
            'type' => 'required|in:1,2',
            'updated_at' => 'nullable|date_format:Y-m-d H:i'
        ]);
        $data['date'] = $data['updated_at'] ? Carbon::parse($data['updated_at'])->toDateTimeString() : Carbon::now()->toDateTimeString();
        $data['updated_at'] = $data['date'];
        $item = Attendance::updateOrCreate(['user_id' => $data['user_id'] , 'date' => Carbon::parse($data['date'])->toDateString() , 'type' => $data['type']], $data);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item)]);
        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $item = Attendance::find($id);
        if ($item) {
            $item->delete();
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_deleted'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
