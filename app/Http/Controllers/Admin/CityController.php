<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\CityDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\City;
use App\Models\Admin\Country;
use App\Models\AuditLog;
use Illuminate\Http\Request;

class CityController extends Controller
{
    private const NAME   = 'cities';
    private const NAME_S = 'city';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(CityDataTable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    public function create()
    {
        $name      = self::NAME;
        $nameS     = self::NAME_S;
        $countries = Country::all();
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'countries'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'country_id' => 'required|exists:countries,id',
            'title_en'   => 'required|string',
            'title_ar'   => 'required|string',
            'lat'        => 'nullable|numeric',
            'lon'        => 'nullable|numeric',
            'radius'     => 'nullable|numeric',
            'active'     => 'nullable|boolean',
        ]);
        $item = City::create($data);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item)]);
        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    public function edit($id)
    {
        $item = City::find($id);
        if ($item) {
            $name      = self::NAME;
            $nameS     = self::NAME_S;
            $countries = Country::all();
            return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'countries'));
        }
        abort(404);
    }

    public function update(Request $request, $id)
    {
        $item = City::find($id);
        if ($item) {
            $data = $request->validate([
                'country_id' => 'required|exists:countries,id',
                'title_en'   => 'required|string',
                'title_ar'   => 'required|string',
                'lat'        => 'nullable|numeric',
                'lon'        => 'nullable|numeric',
                'radius'     => 'nullable|numeric',
                'active'     => 'nullable|boolean',
            ]);
            $item->update($data);
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_updated'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    public function destroy(Request $request, $id)
    {
        $item = City::find($id);
        if ($item) {
            $item->delete();
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_deleted'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
