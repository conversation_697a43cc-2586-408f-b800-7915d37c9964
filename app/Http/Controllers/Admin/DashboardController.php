<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\Admin\ServiceCategoryItemReservation;
use App\Models\Admin\ServiceCategoryItemSearch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index()
    {
        // Statistics
        $s = [
            'total_users' => User::where(['user_type_id' => 2, 'is_guest' => false])->count(),
            'total_views' => ServiceCategoryItem::sum('views'),
            'total_search' => ServiceCategoryItemSearch::count(),
            'total_reservations' => ServiceCategoryItemReservation::count(),
        ];
        return view('content.dashboard', compact('s'));
    }

    /* Process the logout request */
    public function logout(Request $request)
    {
        $user = auth()->user();
        Auth::logout();
        return redirect(route('login'));
    }
}
