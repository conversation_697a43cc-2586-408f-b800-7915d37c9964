<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin\FinancialCategory;
use Illuminate\Http\Request;
use App\DataTables\FinancialCategoryDataTable;
use App\Models\AuditLog;

class FinancialCategoryController extends Controller
{
    private const NAME = 'financial_categories';
    private const NAME_S = 'financial_category';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(FinancialCategoryDataTable $dataTable)
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS'));
    }

    /**
       * Store a newly created resource in storage.
       *
       * @param  \Illuminate\Http\Request  $request
       * @return \Illuminate\Http\Response
       */
    public function store(Request $request)
    {
        $data = $request->validate([
            'title_en' => 'required|string',
            'title_ar' => 'required|string',
            'deduct' => 'required|integer',
        ]);
        $item = FinancialCategory::create($data);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item)]);
        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = FinancialCategory::find($id);
        if($item):
            $name = self::NAME;
            $nameS = self::NAME_S;
            return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item'));
        endif;
        abort(404);
    }

    /**
       * Update the specified resource in storage.
       *
       * @param  \Illuminate\Http\Request  $request
       * @param  Integer  $id
       * @return \Illuminate\Http\Response
       */
    public function update(Request $request, $id)
    {
        $item = FinancialCategory::find($id);
        if($item):
            $data = $request->validate([
                'title_en' => 'required|string',
                'title_ar' => 'required|string',
                'deduct' => 'required|integer',
            ]);
            $item->update($data);
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_updated'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        endif;
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $item = FinancialCategory::find($id);
        if ($item) {
            $item->delete();
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_deleted'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
