<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin\FinancialCategory;
use App\Models\Admin\Invoice;
use Illuminate\Http\Request;
use App\DataTables\InvoiceDataTable;
use App\Models\AuditLog;

class InvoiceController extends Controller
{
    private const NAME = 'invoices';
    private const NAME_S = 'invoice';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:create ' . self::NAME])->only('pay');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(InvoiceDataTable $dataTable)
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        $user = auth()->user();
        $allData = $user ? $user->can('read all_data') : false;
        $fetch_in = Invoice::where('financial_category_id', '!=', 3);
        $fetch_out = Invoice::query();
        $add = $fetch_in->whereHas('category', function ($query) {
            $query->where('deduct', 0);
        })->sum('paid');
        $sub = $fetch_out->whereHas('category', function ($query) {
            $query->where('deduct', 1);
        })->sum('paid');
        $total_cash = ['add' => $add,'sub' => $sub, 'before' => 0];
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS', 'total_cash'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        $user = auth()->user();
        $categories = FinancialCategory::whereNotIn('id', [1,2])->get();
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'categories'));
    }

    /**
       * Store a newly created resource in storage.
       *
       * @param  \Illuminate\Http\Request  $request
       * @return \Illuminate\Http\Response
       */
    public function store(Request $request)
    {
        $user = auth()->user();
        $data = $request->validate([
            'financial_category_id' => 'required|integer',
            'amount' => 'required|numeric',
            'paid' => ['nullable','numeric',
                request()->filled('amount') ? 'lte:amount' : '',
            ],
            'comment' => 'nullable|string',
        ]);
        if(!$data['paid']):
            $data['paid'] = $data['amount'];
        endif;
        // Create invoice
        $countInvoices = Invoice::count();
        $data['number'] = 'INV' . ($countInvoices + 1);
        $data['user_id'] = $user->id;
        $data['remaining'] = $data['amount'] - $data['paid'];
        $data['status'] = ($data['paid'] < $data['amount'] ? 0 : 1);
        $invoice = Invoice::create($data);
        $invoice->payments()->create(['amount' => $data['paid']]);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($invoice), 'relationmodel_id' => $invoice->id]);
        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    /**
     * Pay Invoice remaining.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function pay(Request $request, $id)
    {
        $item = Invoice::find($id);
        if($item):
            $data = $request->validate([
                'paid' => 'required|numeric|lte:' . $item->remaining,
            ]);
            $item->payments()->create(['amount' => $data['paid']]);
            $remaining = $item->amount - ($item->paid +  $data['paid']);
            $item->update(['paid' => $item->paid +  $data['paid'],'remaining' => $remaining,'status' => $remaining ? 0 : 1]);
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_paid'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_paid'));
            return redirect(route(self::NAME . '.index'));
        endif;
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $item = Invoice::find($id);
        if ($item) {
            if(!empty($item->relationmodel)) {
                switch (get_class($item->relationmodel)):
                    case 'App\Models\Admin\Transactions':
                        $transaction = $item->relationmodel;
                        if($transaction->wallet):
                            $transaction->wallet->update(['balance' => $transaction->type == 1 ? $transaction->wallet->balance - $transaction->amount : $transaction->wallet->balance + ($transaction->amount + $transaction->fees)]);
                        endif;
                        $transaction->delete();
                        break;
                endswitch;
            }
            $item->payments()->delete();
            $item->delete();
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_deleted'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
