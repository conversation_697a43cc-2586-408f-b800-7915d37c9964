<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Auth;

class LanguageController extends Controller {

    public function swap($locale) {
        // available language in template array
        $availLocale = ['en' => 'en', 'ar' => 'ar'];
        // check for existing language
        if (array_key_exists($locale, $availLocale)) {
            if (Auth::user()) {
                Auth::user()->lang = $locale;
                Auth::user()->save();
                App::setLocale(session()->put('locale', Auth::user()->lang));
            } else {
                App::setLocale(session()->put('locale', $locale));
            }
        }

        return redirect()->back();
    }

}
