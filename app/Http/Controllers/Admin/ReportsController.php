<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Reports\CashierReportDataTable;
use App\DataTables\Reports\CashierInOutReportDataTable;
use App\DataTables\Reports\AuditLogDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\FinancialCategory;
use App\Models\Admin\Invoice;
use App\Models\Admin\Transactions;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ReportsController extends Controller
{
    private const NAME = 'reports';
    private const NAME_S = 'report';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME_S . '_cashier'])->only('cashier');
        $this->middleware(['permission:read ' . self::NAME_S . '_cashier_in_out'])->only('cashier_in_out');
        $this->middleware(['permission:read audit_log'])->only('audit_log');
    }

    public function cashier(Request $request, CashierReportDataTable $dataTable)
    {
        $data = $request->validate([
            'from_date' => 'nullable|date_format:Y-m-d H:i',
            'to_date' => 'nullable|date_format:Y-m-d H:i|after:from_date',
            'user_id' => 'nullable|integer',
            'financial_category_id' => 'nullable|integer',
            'status' => 'nullable|integer',
            'type' => 'nullable|integer'
        ]);

        $dayStart = Carbon::now()->startOfDay();
        $now = Carbon::now();
        if(empty($data['from_date'])):
            $data['from_date'] = $dayStart;
        endif;
        if(empty($data['to_date'])):
            $data['to_date'] = $now;
        endif;
        $name = self::NAME;
        $nameS = self::NAME_S;
        $users  = User::all();
        $categories = FinancialCategory::orderByRaw("id = 3 DESC")->orderBy('deduct')->orderBy('id')->get();
        $fetch_cash = Invoice::with(['category', 'relationmodel']);
        $fetch_cash->where('financial_category_id', 3);
        $fetch_total = Invoice::with(['category', 'relationmodel']);
        $fetch_in = Invoice::with(['category', 'relationmodel']);
        $fetch_in->where('financial_category_id', '!=', 3);
        $fetch_out = Invoice::with(['category', 'relationmodel']);

        if(!empty($data['from_date'])):
            $fetch_cash->where('created_at', '>=', $data['from_date']);
            $fetch_total->where('created_at', '>=', $data['from_date']);
            $fetch_in->where('created_at', '>=', $data['from_date']);
            $fetch_out->where('created_at', '>=', $data['from_date']);
        endif;
        if(!empty($data['to_date'])):
            $fetch_cash->where('created_at', '<=', $data['to_date']);
            $fetch_total->where('created_at', '<=', $data['to_date']);
            $fetch_in->where('created_at', '<=', $data['to_date']);
            $fetch_out->where('created_at', '<=', $data['to_date']);
        endif;
        if(!empty($data['user_id'])):
            $fetch_cash->where('user_id', $data['user_id']);
            $fetch_total->where('user_id', $data['user_id']);
            $fetch_in->where('user_id', $data['user_id']);
            $fetch_out->where('user_id', $data['user_id']);
        endif;
        if(!empty($data['financial_category_id'])):
            if($data['financial_category_id'] != 3) {
                $fetch_cash->where('id', 0);
            }
            $fetch_total->where('financial_category_id', $data['financial_category_id']);
            $fetch_in->where('financial_category_id', $data['financial_category_id']);
            $fetch_out->where('financial_category_id', $data['financial_category_id']);
        endif;
        if(isset($data['status']) && in_array($data['status'], [0, 1])):
            $fetch_cash->where('status', $data['status']);
            $fetch_total->where('status', $data['status']);
            $fetch_in->where('status', $data['status']);
            $fetch_out->where('status', $data['status']);
        endif;
        $cash_total = $fetch_cash->sum('paid');
        $in_paid = $fetch_in->whereHas('category', function ($query) {
            $query->where('deduct', 0);
        })->sum('paid');
        $in_remaining = $fetch_in->whereHas('category', function ($query) {
            $query->where('deduct', 0);
        })->sum('remaining');
        $out_paid = $fetch_out->whereHas('category', function ($query) {
            $query->where('deduct', 1);
        })->sum('paid');
        $out_remaining = $fetch_out->whereHas('category', function ($query) {
            $query->where('deduct', 1);
        })->sum('remaining');
        $results = $fetch_total->get();
        $statistics = [
            'cash' => [
                'total' => $cash_total,
                'required' =>  ($in_paid + $cash_total) - $out_paid
            ],
            'in' => [
                'paid' => $in_paid,
                'remaining' => $in_remaining,
                'total' => $in_paid + $in_remaining,
            ],
            'out' => [
                'paid' => $out_paid,
                'remaining' => $out_remaining,
                'total' => $out_paid + $out_remaining,
            ]

        ];
        $total = $this->get_total_profit($data);
        return $dataTable->with($data)->render('content.' . self::NAME . '.cashier.index', compact('name', 'nameS', 'dayStart', 'now', 'users', 'categories', 'results', 'statistics', 'total'));
    }

    public function cashier_in_out(Request $request, CashierInOutReportDataTable $dataTable)
    {
        $data = $request->validate([
            'from_date' => 'nullable|date_format:Y-m-d H:i',
            'to_date' => 'nullable|date_format:Y-m-d H:i|after:from_date',
            'user_id' => 'nullable|integer',
            'financial_category_id' => 'nullable|integer',
            'status' => 'nullable|integer',
        ]);

        $dayStart = Carbon::now()->startOfDay();
        $now = Carbon::now();
        if(empty($data['from_date'])):
            $data['from_date'] = $dayStart;
        endif;
        if(empty($data['to_date'])):
            $data['to_date'] = $now;
        endif;
        $name = self::NAME;
        $nameS = self::NAME_S;
        $users  = User::all();
        $categories = FinancialCategory::whereIn('id', [5,4])->orderByRaw("id = 3 DESC")->orderBy('deduct')->orderBy('id')->get();
        $fetch_total = Invoice::with(['category', 'relationmodel']);

        if(!empty($data['from_date'])):
            $fetch_total->where('created_at', '>=', $data['from_date']);
        endif;
        if(!empty($data['to_date'])):
            $fetch_total->where('created_at', '<=', $data['to_date']);
        endif;
        if(!empty($data['user_id'])):
            $fetch_total->where('user_id', $data['user_id']);
        endif;
        if(!empty($data['financial_category_id'])):
            $fetch_total->where('financial_category_id', $data['financial_category_id']);
        endif;
        if(isset($data['status']) && in_array($data['status'], [0, 1])):
            $fetch_total->where('status', $data['status']);
        endif;
        $results = $fetch_total->get();
        return $dataTable->with($data)->render('content.' . self::NAME . '.cashier_in_out.index', compact('name', 'nameS', 'dayStart', 'now', 'users', 'categories', 'results'));
    }

    public function audit_log(Request $request, AuditLogDataTable $dataTable)
    {
        $data = $request->validate([
            'type' => 'nullable|integer',
            'user_id' => 'nullable|integer',
            'from_date' => 'nullable|date_format:Y-m-d H:i',
            'to_date' => 'nullable|date_format:Y-m-d H:i|after:from_date',
        ]);

        $dayStart = Carbon::now()->startOfDay();
        $now = Carbon::now();
        if(empty($data['from_date'])):
            $data['from_date'] = $dayStart;
        endif;
        if(empty($data['to_date'])):
            $data['to_date'] = $now;
        endif;
        $name = self::NAME;
        $nameS = self::NAME_S;
        $users  = User::all();
        return $dataTable->with($data)->render('content.' . self::NAME . '.audit_log.index', compact('name', 'nameS', 'dayStart', 'now', 'users'));
    }

    protected function get_total_profit($data)
    {
        $fetch_atm = Transactions::query();
        $fetch = Transactions::query();
        $fetch_atm->where('operator', 5);
        $fetch->where(function ($query) {
            $query->where('operator', '!=', 5);
            $query->orWhere('operator', null);
        });
        if(!empty($data['from_date'])):
            $fetch->where('created_at', '>=', $data['from_date']);
            $fetch_atm->where('created_at', '>=', $data['from_date']);
        endif;
        if(!empty($data['to_date'])):
            $fetch->where('created_at', '<=', $data['to_date']);
            $fetch_atm->where('created_at', '<=', $data['to_date']);
        endif;
        if(!empty($data['user_id'])):
            $fetch->where('user_id', $data['user_id']);
            $fetch_atm->where('user_id', $data['user_id']);
        endif;
        if(!empty($data['financial_category_id'])):
            $fetch->whereHas('invoice', function ($query) use ($data) {
                $query->where('financial_category_id', $data['financial_category_id']);
            });
            $fetch_atm->whereHas('invoice', function ($query) use ($data) {
                $query->where('financial_category_id', $data['financial_category_id']);
            });
        endif;
        if(isset($data['status']) && in_array($data['status'], [0, 1])):
            $fetch->whereHas('invoice', function ($query) use ($data) {
                $query->where('status', $data['status']);
            });
            $fetch_atm->whereHas('invoice', function ($query) use ($data) {
                $query->where('status', $data['status']);
            });
        endif;
        if(!empty($data['type'])):
            $fetch->where('type', $data['type']);
            $fetch_atm->where('type', $data['type']);
        endif;
        $total_commission = 0;
        $total_fees =  0;
        $total_atm_fees =  0;
        return  [
             'commission' => $total_commission,
             'fees' => $total_fees,
             'total' => $total_commission - $total_fees,
             'atm_fees' => $total_atm_fees
         ];
    }
}
