<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use App\DataTables\RolesDataTable;
use App\Models\Level;
use App\Models\AuditLog;

class RoleController extends Controller {

    private const INDEX = 'roles.index';

    public function __construct() {
        $this->middleware(['permission:read roles'])->only('index');
        $this->middleware(['permission:create roles'])->only('create');
        $this->middleware(['permission:update roles'])->only('edit');
        $this->middleware(['permission:delete roles'])->only('destroy');
    }

    public function index(RolesDataTable $dataTable) {
        $levels = Level::select('name', 'actions')->get();
        return $dataTable->render('content.access_rights.index', compact('levels'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request) {
        $levels = Level::select('name', 'actions')->get();
        return view('content.access_rights.create', compact(['levels']));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {
        $data = $request->validate([
            'name' => 'required|string|unique:roles|max:60',
        ]);
        $permissions = $request->validate([
            'permissions' => 'required',
        ]);
        $role = Role::create($data);
        $role->syncPermissions($permissions);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('role_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($role)]);
        session()->flash('status', __('role_created'));
        return redirect(route(self::INDEX));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Role  $role
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {
        $role = Role::find($id);
        if ($role) {
            $levels = Level::all();
            return view('content.access_rights.edit', compact(['role', 'levels']));
        }
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Role  $role
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id) {
        $role = Role::find($id);
        if ($role) {
            $data = $request->validate([
                'name' => 'required|string|max:60',
                'permissions' => 'required'
            ]);

            $role->update($data);
            $role->syncPermissions($request->permissions);
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('role_updated'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($role)]);
            session()->flash('status', __('role_updated'));
            return redirect(route(self::INDEX));
        }
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Role  $role
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id) {
        $role = Role::find($id);
        if ($role) {
            $exist = DB::table('model_has_roles')->select("role_id")->where("role_id", $role->id)->first();
            if (!$exist) {
                $role->delete();
                AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('role_deleted'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($role)]);
                session()->flash('status', __('role_deleted'));
            } else {
                session()->flash('error', __('role_not_deleted'));
            }
            return redirect(route(self::INDEX));
        }
        abort(404);
    }

}
