<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategory;
use App\Models\AuditLog;
use App\Models\UserTypes;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Laravel\Facades\Image;

class ServiceCategoryController extends Controller
{
    private const NAME   = 'service_categories';
    private const NAME_S = 'service_category';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param ServiceCategoryDataTable $dataTable
     * @return \Illuminate\Http\Response
     */
    public function index(ServiceCategoryDataTable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name             = self::NAME;
        $nameS            = self::NAME_S;
        $userTypes        = UserTypes::all();
        $parentCategories = ServiceCategory::whereNull('parent')->get();
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'userTypes', 'parentCategories'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'title_en'                 => 'required|string',
            'title_ar'                 => 'required|string',
            'icon'                     => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048', // Validate image file
            'image'                    => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',     // Image validation
            'order'                    => 'nullable|integer',
            'reservation_type'         => 'nullable|integer',
            'reservation_confirmation' => 'nullable|boolean',
            'enable_register'          => 'nullable|boolean',
            'can_add_item'             => 'nullable|string',
            'commission'               => 'nullable|numeric',
            'user_type_id'             => 'required|integer',
            'parent'                   => 'nullable|integer|exists:service_categories,id',
        ]);

        // Set default values if not provided
        $data['order']        = $data['order'] ?? 0;
        $data['can_add_item'] = $data['can_add_item'] ?? 0;

        // Handle file upload and resize the icon
        if ($request->hasFile('icon')) {
            $image        = $request->file('icon');
            $resizedImage = Image::read($image)->resize(100, 100); // Resize to 100x100 pixels
            $iconPath     = 'icons/' . uniqid() . '.' . $image->getClientOriginalExtension();
            Storage::disk('public')->put($iconPath, $resizedImage->encode());
            $data['icon'] = $iconPath;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('service_categories', 'public');
        }

        $item = ServiceCategory::create($data);

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($item),
            'relationmodel_id'   => $item->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = ServiceCategory::find($id);
        if ($item) {
            $name             = self::NAME;
            $nameS            = self::NAME_S;
            $userTypes        = UserTypes::all();
            $parentCategories = ServiceCategory::whereNull('parent')->get();
            return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'userTypes', 'parentCategories'));
        }
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $item = ServiceCategory::find($id);
        if ($item) {
            $data = $request->validate([
                'title_en'                 => 'required|string',
                'title_ar'                 => 'required|string',
                'icon'                     => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048', // Validate image file
                'image'                    => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'order'                    => 'nullable|integer',
                'reservation_type'         => 'nullable|integer',
                'reservation_confirmation' => 'nullable|boolean',
                'enable_register'          => 'nullable|boolean',
                'can_add_item'             => 'nullable|string',
                'commission'               => 'nullable|numeric',
                'user_type_id'             => 'required|integer',
                'parent'                   => 'nullable|integer|exists:service_categories,id',
            ]);

            // Set default values if not provided
            $data['order']        = $data['order'] ?? 0;
            $data['can_add_item'] = $data['can_add_item'] ?? 0;

            // Handle file upload and resize the icon
            if ($request->hasFile('icon')) {
                // Delete old icon if it exists
                if ($item->icon && Storage::disk('public')->exists($item->icon)) {
                    Storage::disk('public')->delete($item->icon);
                }

                // Resize and save the new icon
                $image        = $request->file('icon');
                $resizedImage = Image::read($image)->resize(100, 100); // Resize to 100x100 pixels
                $iconPath     = 'icons/' . uniqid() . '.' . $image->getClientOriginalExtension();
                Storage::disk('public')->put($iconPath, $resizedImage->encode());
                $data['icon'] = $iconPath;
            }

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($item->image) {
                    Storage::disk('public')->delete($item->image);
                }
                $data['image'] = $request->file('image')->store('service_categories', 'public');
            }

            $item->update($data);

            // Log the update
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_updated'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $item = ServiceCategory::find($id);
        if ($item) {

            // Delete associated files
            if ($item->image) {
                Storage::disk('public')->delete($item->image);
            }
            if ($item->icon) {
                Storage::disk('public')->delete($item->icon);
            }

            $item->delete();

            // Log the deletion
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_deleted'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
