<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryDocumentsDatatable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategoryDocument;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ServiceCategoryDocumentsController extends Controller
{
    private const NAME   = 'service_categories_documents';
    private const NAME_S = 'service_category_document';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(ServiceCategoryDocumentsDatatable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    public function create()
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'title_en'  => 'required|string|max:255',
            'title_ar'  => 'required|string|max:255',
            'icon'     => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'example'  => 'nullable|file|mimes:pdf,doc,docx|max:2048',
            'display'  => 'nullable|boolean',
            'required' => 'nullable|boolean',
            'multiple' => 'nullable|boolean',
        ]);

        // Handle file uploads
        if ($request->hasFile('icon')) {
            $data['icon'] = $request->file('icon')->store('icons', 'public');
        }
        if ($request->hasFile('example')) {
            $data['example'] = $request->file('example')->store('examples', 'public');
        }

        // Set default values if not provided
        $data['display']  = $data['display'] ?? false;
        $data['required'] = $data['required'] ?? false;
        $data['multiple'] = $data['multiple'] ?? false;

        // Create the document
        $document = ServiceCategoryDocument::create($data);

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($document),
            'relationmodel_id'   => $document->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    public function edit($id)
    {
        $item = ServiceCategoryDocument::find($id);
        if ($item) {
            $name  = self::NAME;
            $nameS = self::NAME_S;
            return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item'));
        }
        abort(404);
    }

    public function update(Request $request, $id)
    {
        $document = ServiceCategoryDocument::find($id);
        if ($document) {
            $data = $request->validate([
                'title_en'  => 'required|string|max:255',
                'title_ar'  => 'required|string|max:255',
                'icon'     => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
                'example'  => 'nullable|file|mimes:pdf,doc,docx|max:2048',
                'display'  => 'nullable|boolean',
                'required' => 'nullable|boolean',
                'multiple' => 'nullable|boolean',
            ]);

            // Handle file uploads
            if ($request->hasFile('icon')) {
                if ($document->icon && Storage::disk('public')->exists($document->icon)) {
                    Storage::disk('public')->delete($document->icon);
                }
                $data['icon'] = $request->file('icon')->store('icons', 'public');
            }
            if ($request->hasFile('example')) {
                if ($document->example && Storage::disk('public')->exists($document->example)) {
                    Storage::disk('public')->delete($document->example);
                }
                $data['example'] = $request->file('example')->store('examples', 'public');
            }

            // Set default values if not provided
            $data['display']  = $data['display'] ?? false;
            $data['required'] = $data['required'] ?? false;
            $data['multiple'] = $data['multiple'] ?? false;

            // Update the document
            $document->update($data);

            // Log the update
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_updated'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($document),
                'relationmodel_id'   => $document->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    public function destroy(Request $request, $id)
    {
        $document = ServiceCategoryDocument::find($id);
        if ($document) {
            // Delete associated files
            if ($document->icon && Storage::disk('public')->exists($document->icon)) {
                Storage::disk('public')->delete($document->icon);
            }
            if ($document->example && Storage::disk('public')->exists($document->example)) {
                Storage::disk('public')->delete($document->example);
            }

            // Delete the document
            $document->delete();

            // Log the deletion
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_deleted'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($document),
                'relationmodel_id'   => $document->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}