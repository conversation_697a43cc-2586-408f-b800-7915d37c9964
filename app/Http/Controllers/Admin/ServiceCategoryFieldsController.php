<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryFieldsDatatable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategoryField;
use App\Models\Admin\ServiceCategoryFieldOption;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Laravel\Facades\Image;

class ServiceCategoryFieldsController extends Controller
{
    private const NAME   = 'service_categories_fields';
    private const NAME_S = 'service_category_field';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param ServiceCategoryFieldsDatatable $dataTable
     * @return \Illuminate\Http\Response
     */
    public function index(ServiceCategoryFieldsDatatable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'title_en'           => 'nullable|string|max:255',
            'title_ar'           => 'nullable|string|max:255',
            'type'               => 'required|integer|between:1,14',
            'icon'               => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048', // Validate image file
            'display'            => 'nullable|boolean',
            'required'           => 'nullable|boolean',
            'options'            => 'nullable|array',
            'options.*.title_en' => 'required_with:options|string|max:255',
            'options.*.title_ar' => 'required_with:options|string|max:255',
        ]);

        // Handle file upload and resize the icon
        if ($request->hasFile('icon')) {
            $image        = $request->file('icon');
            $resizedImage = Image::read($image)->resize(100, 100); // Resize to 100x100 pixels
            $iconPath     = 'icons/' . uniqid() . '.' . $image->getClientOriginalExtension();
            Storage::disk('public')->put($iconPath, $resizedImage->encode());
            $data['icon'] = $iconPath;
        }

        // Set default values if not provided
        $data['display']  = $data['display'] ?? false;
        $data['required'] = $data['required'] ?? false;

        // Create the field
        $field = ServiceCategoryField::create($data);

        // If type is 7 (Checkbox) or 8 (Radio), save options
        if (in_array($data['type'], [7, 8])) {
            foreach ($request->input('options', []) as $option) {
                ServiceCategoryFieldOption::create([
                    'title_en'                  => $option['title_en'],
                    'title_ar'                  => $option['title_ar'],
                    'service_category_field_id' => $field->id,
                ]);
            }
        }

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($field),
            'relationmodel_id'   => $field->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = ServiceCategoryField::with('options')->find($id);
        if ($item) {
            $name  = self::NAME;
            $nameS = self::NAME_S;
            return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item'));
        }
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $field = ServiceCategoryField::find($id);
        if ($field) {
            $data = $request->validate([
                'title_en'           => 'nullable|string|max:255',
                'title_ar'           => 'nullable|string|max:255',
                'type'               => 'required|integer|between:1,14',
                'icon'               => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048', // Validate image file
                'display'            => 'nullable|boolean',
                'required'           => 'nullable|boolean',
                'options'            => 'nullable|array',
                'options.*.title_en' => 'required_with:options|string|max:255',
                'options.*.title_ar' => 'required_with:options|string|max:255',
            ]);

            // Handle file upload and resize the icon
            if ($request->hasFile('icon')) {
                // Delete old icon if it exists
                if ($field->icon && Storage::disk('public')->exists($field->icon)) {
                    Storage::disk('public')->delete($field->icon);
                }

                // Resize and save the new icon
                $image        = $request->file('icon');
                $resizedImage = Image::read($image)->resize(100, 100); // Resize to 100x100 pixels
                $iconPath     = 'icons/' . uniqid() . '.' . $image->getClientOriginalExtension();
                Storage::disk('public')->put($iconPath, $resizedImage->encode());
                $data['icon'] = $iconPath;
            }

            // Set default values if not provided
            $data['display']  = $data['display'] ?? false;
            $data['required'] = $data['required'] ?? false;

            // Update the field
            $field->update($data);

            // Handle options for type 7 or 8
            if (in_array($data['type'], [7, 8])) {
                $field->options()->delete();
                // Add new options
                foreach ($request->input('options', []) as $option) {
                    ServiceCategoryFieldOption::create([
                        'title_en'                  => $option['title_en'],
                        'title_ar'                  => $option['title_ar'],
                        'service_category_field_id' => $field->id,
                    ]);
                }
            } else {
                // If type is not 7 or 8, delete all options only if the type is changed from 7 or 8 to something else
                if ($field->type != $data['type'] && in_array($field->type, [7, 8])) {
                    $field->options()->delete();
                }
            }

            // Log the update
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_updated'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($field),
                'relationmodel_id'   => $field->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $field = ServiceCategoryField::find($id);
        if ($field) {
            // Delete associated options
            $field->options()->delete();

            // Delete the icon file if it exists
            if ($field->icon && Storage::disk('public')->exists($field->icon)) {
                Storage::disk('public')->delete($field->icon);
            }

            // Delete the field
            $field->delete();

            // Log the deletion
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_deleted'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($field),
                'relationmodel_id'   => $field->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
