<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryFormDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategory;
use App\Models\Admin\ServiceCategoryDocument;
use App\Models\Admin\ServiceCategoryField;
use Illuminate\Http\Request;

class ServiceCategoryFormController extends Controller
{
    private const NAME   = 'service_categories_forms';
    private const NAME_S = 'service_category_form';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create', 'store');
        $this->middleware(['permission:update ' . self::NAME])->only('edit', 'update');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(ServiceCategoryFormDataTable $dataTable, $serviceCategoryId)
    {
        $item  = ServiceCategory::findOrFail($serviceCategoryId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->with(['id' => $serviceCategoryId])->render('content.' . self::NAME . '.index', compact('name', 'nameS', 'item'));
    }

    public function create($serviceCategoryId)
    {
        $item      = ServiceCategory::findOrFail($serviceCategoryId);
        $name      = self::NAME;
        $nameS     = self::NAME_S;
        $fields    = ServiceCategoryField::all();    // Fetch all fields
        $documents = ServiceCategoryDocument::all(); // Fetch all documents
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'item', 'fields', 'documents'));
    }

    public function store(Request $request, $serviceCategoryId)
    {
        $serviceCategory = ServiceCategory::findOrFail($serviceCategoryId);

        $data = $request->validate([
            'type'                     => 'required|string|in:1,2,3',
            'form_items'               => 'required|array',
            'form_items.*.field_id'    => 'required_without:form_items.*.document_id|exists:service_category_fields,id',
            'form_items.*.document_id' => 'required_without:form_items.*.field_id|exists:service_category_documents,id',
            'form_items.*.order'       => 'required|integer|min:1',
        ]);

        $form = $serviceCategory->forms()->create(['type' => $data['type']]);

        if (! empty($data['form_items'])) {
            foreach ($data['form_items'] as $item) {
                $form->formItems()->create([
                    'service_category_field_id'    => $item['field_id'] ?? null,
                    'service_category_document_id' => $item['document_id'] ?? null,
                    'order'                        => $item['order'],
                ]);
            }
        }

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index', $serviceCategory->id));
    }

    public function edit($serviceCategoryId, $formId)
    {
        $item      = ServiceCategory::findOrFail($serviceCategoryId);
        $form      = $item->forms()->findOrFail($formId);
        $fields    = ServiceCategoryField::all();    // Fetch all fields
        $documents = ServiceCategoryDocument::all(); // Fetch all documents
        $name      = self::NAME;
        $nameS     = self::NAME_S;
        return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'form', 'fields', 'documents'));
    }

    public function update(Request $request, $serviceCategoryId, $formId)
    {
        $serviceCategory = ServiceCategory::findOrFail($serviceCategoryId);
        $form            = $serviceCategory->forms()->findOrFail($formId);

        $data = $request->validate([
            'type'                     => 'required|string|in:1,2,3', // Update based on your form types
            'form_items'               => 'nullable|array',
            'form_items.*.field_id'    => 'required_without:form_items.*.document_id|exists:service_category_fields,id',
            'form_items.*.document_id' => 'required_without:form_items.*.field_id|exists:service_category_documents,id',
            'form_items.*.order'       => 'required|integer|min:1',
        ]);

        // Update the form type
        $form->update(['type' => $data['type']]);

        // Delete existing form items
        $form->formItems()->delete();

        // Add updated form items
        if (! empty($data['form_items'])) {
            foreach ($data['form_items'] as $item) {
                $form->formItems()->create([
                    'service_category_field_id'    => $item['field_id'] ?? null,
                    'service_category_document_id' => $item['document_id'] ?? null,
                    'order'                        => $item['order'],
                ]);
            }
        }

        session()->flash('status', __(self::NAME_S . '_updated'));
        return redirect(route(self::NAME . '.index', $serviceCategory->id));
    }

    public function destroy($serviceCategoryId, $formId)
    {
        $serviceCategory = ServiceCategory::findOrFail($serviceCategoryId);
        $form            = $serviceCategory->forms()->findOrFail($formId);
        $form->delete();

        session()->flash('status', __(self::NAME_S . '_deleted'));
        return redirect(route(self::NAME . '.index', $serviceCategory->id));
    }

    public function getFieldsAndDocuments($service_category_id, $type)
    {
        $html = view('content.service_categories_forms.display', ['service_category_id' => $service_category_id,'type' => $type])->render();
        return response()->json(['html' => $html]);
    }
}
