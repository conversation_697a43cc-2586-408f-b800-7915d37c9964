<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryItemDisablePeriodDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\AuditLog;
use Illuminate\Http\Request;

class ServiceCategoryItemDisablePeriodController extends Controller
{
    private const NAME   = 'service_category_item_disable_periods';
    private const NAME_S = 'service_category_item_disable_period';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create', 'store');
        $this->middleware(['permission:update ' . self::NAME])->only('edit', 'update');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(ServiceCategoryItemDisablePeriodDataTable $dataTable, $serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->with(['id' => $serviceCategoryItemId])->render('content.' . self::NAME . '.index', compact('name', 'nameS', 'item'));
    }

    public function create($serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'item'));
    }

    public function store(Request $request, $serviceCategoryItemId)
    {
        $request->validate([
            'disable_start' => 'required|date',
            'disable_end'   => 'required|date|after:disable_start',
        ]);

        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $disablePeriod       = $serviceCategoryItem->disablePeriods()->create($request->only('disable_start', 'disable_end'));

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($serviceCategoryItem),
            'relationmodel_id'   => $serviceCategoryItem->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }

    public function edit($serviceCategoryItemId, $disablePeriodId)
    {
        $item          = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $disablePeriod = $item->disablePeriods()->findOrFail($disablePeriodId);
        $name          = self::NAME;
        $nameS         = self::NAME_S;
        return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'disablePeriod'));
    }

    public function update(Request $request, $serviceCategoryItemId, $disablePeriodId)
    {
        $request->validate([
            'disable_start' => 'required|date',
            'disable_end'   => 'required|date|after:disable_start',
        ]);

        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $disablePeriod       = $serviceCategoryItem->disablePeriods()->findOrFail($disablePeriodId);
        $disablePeriod->update($request->only('disable_start', 'disable_end'));

        // Log the update
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_updated'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($disablePeriod),
            'relationmodel_id'   => $disablePeriod->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_updated'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }

    public function destroy($serviceCategoryItemId, $disablePeriodId)
    {
        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $disablePeriod       = $serviceCategoryItem->disablePeriods()->findOrFail($disablePeriodId);
        $disablePeriod->delete();

        // Log the deletion
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_deleted'),
            'ip_address'         => request()->getClientIp(),
            'relationmodel_type' => get_class($disablePeriod),
            'relationmodel_id'   => $disablePeriod->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_deleted'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }
}
