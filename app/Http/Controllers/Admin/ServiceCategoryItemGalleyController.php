<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryItemGalleryDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ServiceCategoryItemGalleyController extends Controller
{
    private const NAME   = 'service_category_item_galleries';
    private const NAME_S = 'service_category_item_gallery';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create', 'store');
        $this->middleware(['permission:update ' . self::NAME])->only('edit', 'update');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(ServiceCategoryItemGalleryDataTable $dataTable, $serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->with(['id' => $serviceCategoryItemId])->render('content.' . self::NAME . '.index', compact('name', 'nameS', 'item'));
    }

    public function create($serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'item'));
    }

    public function store(Request $request, $serviceCategoryItemId)
    {
        $data = $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048', // Image validation
            'cover' => 'nullable|boolean',
        ]);

        // Set default values if not provided
        $data['cover'] = $data['cover'] ?? 0;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('galleries', 'public');
        }

        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $serviceCategoryItem->gallery()->create($data);

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($serviceCategoryItem),
            'relationmodel_id'   => $serviceCategoryItem->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }

    public function edit($serviceCategoryItemId, $galleryImageId)
    {
        $item    = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $gallery = $item->gallery()->findOrFail($galleryImageId);
        $name    = self::NAME;
        $nameS   = self::NAME_S;
        return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'gallery'));
    }

    public function update(Request $request, $serviceCategoryItemId, $galleryImageId)
    {
        $data = $request->validate([
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // Image validation
            'cover' => 'nullable|boolean',
        ]);

        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $gallery             = $serviceCategoryItem->gallery()->findOrFail($galleryImageId);

        // Set default values if not provided
        $data['cover'] = $data['cover'] ?? 0;

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($gallery->image) {
                Storage::disk('public')->delete($gallery->image);
            }
            $data['image'] = $request->file('image')->store('galleries', 'public');
        }

        $gallery->update($data);

        // Log the update
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_updated'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($gallery),
            'relationmodel_id'   => $gallery->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_updated'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }

    public function destroy($serviceCategoryItemId, $galleryImageId)
    {
        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $gallery             = $serviceCategoryItem->gallery()->findOrFail($galleryImageId);

        // Delete associated files
        if ($gallery->image) {
            Storage::disk('public')->delete($gallery->image);
        }

        $gallery->delete();

        // Log the deletion
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_deleted'),
            'ip_address'         => request()->getClientIp(),
            'relationmodel_type' => get_class($gallery),
            'relationmodel_id'   => $gallery->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_deleted'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }
}
