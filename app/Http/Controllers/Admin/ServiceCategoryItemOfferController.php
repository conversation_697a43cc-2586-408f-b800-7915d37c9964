<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryItemOfferDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategoryItem;
use Illuminate\Http\Request;

class ServiceCategoryItemOfferController extends Controller
{
    private const NAME   = 'service_category_item_offers';
    private const NAME_S = 'service_category_item_offer';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create', 'store');
        $this->middleware(['permission:update ' . self::NAME])->only('edit', 'update');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(ServiceCategoryItemOfferDataTable $dataTable, $serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->with(['id' => $serviceCategoryItemId])->render('content.' . self::NAME . '.index', compact('name', 'nameS', 'item'));
    }

    public function create($serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'item'));
    }

    public function store(Request $request, $serviceCategoryItemId)
    {
        $request->validate([
            'title_en'    => 'required|string|max:255',
            'title_ar'    => 'required|string|max:255',
            'value'       => 'required|numeric',
            'type'        => 'required|in:1,2',
            'apply_to'    => 'required|in:0,1,2',
            'offer_start' => 'required|date',
            'offer_end'   => 'required|date|after:offer_start',
        ]);

        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $serviceCategoryItem->offers()->create($request->only([
            'title_en', 'title_ar', 'value', 'type', 'apply_to', 'offer_start', 'offer_end',
        ]));

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }

    public function edit($serviceCategoryItemId, $offerId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $offer = $item->offers()->findOrFail($offerId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'offer'));
    }

    public function update(Request $request, $serviceCategoryItemId, $offerId)
    {
        $request->validate([
            'title_en'    => 'required|string|max:255',
            'title_ar'    => 'required|string|max:255',
            'value'       => 'required|numeric',
            'type'        => 'required|in:1,2',
            'apply_to'    => 'required|in:0,1,2',
            'offer_start' => 'required|date',
            'offer_end'   => 'required|date|after:offer_start',
        ]);

        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $offer               = $serviceCategoryItem->offers()->findOrFail($offerId);
        $offer->update($request->only([
            'title_en', 'title_ar', 'value', 'type', 'apply_to', 'offer_start', 'offer_end',
        ]));

        session()->flash('status', __(self::NAME_S . '_updated'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }

    public function destroy($serviceCategoryItemId, $offerId)
    {
        $serviceCategoryItem = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $offer               = $serviceCategoryItem->offers()->findOrFail($offerId);
        $offer->delete();

        session()->flash('status', __(self::NAME_S . '_deleted'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItem->id));
    }
}
