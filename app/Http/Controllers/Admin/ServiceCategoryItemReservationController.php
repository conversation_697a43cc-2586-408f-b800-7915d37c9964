<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryItemReservationDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\Admin\ServiceCategoryItemReservation;
use App\Models\AuditLog;
use App\Models\User;
use App\Rules\CheckReservationAvailability;
use Illuminate\Http\Request;

class ServiceCategoryItemReservationController extends Controller
{
    private const NAME   = 'service_category_item_reservations';
    private const NAME_S = 'service_category_item_reservation';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create', 'store');
        $this->middleware(['permission:update ' . self::NAME])->only('edit', 'update');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param ServiceCategoryItemReservationDataTable $dataTable
     * @return \Illuminate\Http\Response
     */
    public function index(ServiceCategoryItemReservationDataTable $dataTable, $serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->with(['id' => $serviceCategoryItemId])->render('content.' . self::NAME . '.index', compact('name', 'nameS', 'item'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($serviceCategoryItemId)
    {
        $item  = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $users = User::where('is_guest', false)->where('user_type_id', 2)->get();
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'item', 'users'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $serviceCategoryItemId)
    {
        $data = $request->validate([
            'reservation_date' => 'required|date_format:Y-m-d',
            'reservation_from' => 'required|date_format:Y-m-d\TH:i', // Example: 2023-10-15T14:30
            'reservation_to'   => 'required|date_format:Y-m-d\TH:i|after:reservation_from',
            'confirmed'        => 'nullable|boolean',
            'user_id'          => 'required|exists:users,id',
        ]);

        // Add custom rule for reservation availability
        $request->validate([
            'reservation_from' => [
                new CheckReservationAvailability(
                    $request->service_category_item_id,
                    $request->reservation_from,
                    $request->reservation_to
                ),
            ],
        ]);

        

        $data['service_category_item_id'] = $serviceCategoryItemId;

        $reservation = ServiceCategoryItemReservation::create($data);

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($reservation),
            'relationmodel_id'   => $reservation->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItemId));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($serviceCategoryItemId, $reservationId)
    {
        $item        = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $reservation = $item->reservations()->findOrFail($reservationId);
        $users = User::where('is_guest', false)->where('user_type_id', 2)->get();
        $name        = self::NAME;
        $nameS       = self::NAME_S;
        return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'reservation', 'users'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $serviceCategoryItemId, $reservationId)
    {
        $item        = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $reservation = $item->reservations()->findOrFail($reservationId);

        $data = $request->validate([
            'reservation_date' => 'required|date_format:Y-m-d',
            'reservation_from' => 'required|date_format:Y-m-d\TH:i',
            'reservation_to'   => 'required|date_format:Y-m-d\TH:i|after:reservation_from',
            'confirmed'        => 'nullable|boolean',
            'user_id'          => 'required|exists:users,id',
        ]);

        $reservation->update($data);

        // Log the update
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_updated'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($reservation),
            'relationmodel_id'   => $reservation->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_updated'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItemId));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($serviceCategoryItemId, $reservationId)
    {
        $item        = ServiceCategoryItem::findOrFail($serviceCategoryItemId);
        $reservation = $item->reservations()->findOrFail($reservationId);
        $reservation->delete();

        // Log the deletion
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_deleted'),
            'ip_address'         => request()->getClientIp(),
            'relationmodel_type' => get_class($reservation),
            'relationmodel_id'   => $reservation->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_deleted'));
        return redirect(route(self::NAME . '.index', $serviceCategoryItemId));
    }
}
