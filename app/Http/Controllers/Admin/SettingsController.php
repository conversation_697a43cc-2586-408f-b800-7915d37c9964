<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditLog;
use App\Models\CredentialSetting;
use App\Models\EmailSetting;
use App\Models\Setting;
use App\Models\SmsSettings;
use App\Models\PaymentGatewaySetting;
use Illuminate\Http\Request;
use stdClass;

class SettingsController extends Controller
{
    public function __construct()
    {
        // settings
        $this->middleware(['permission:read settings'])->only('getSettings');
        $this->middleware(['permission:update settings'])->only('updateSettings');
        // email_setting
        $this->middleware(['permission:read email_setting'])->only('getEmailSetting');
        $this->middleware(['permission:update email_setting'])->only('updateEmailSetting');
        // credential_setting
        $this->middleware(['permission:read credential_setting'])->only('editCredentialSetting');
        $this->middleware(['permission:update credential_setting'])->only('updateCredentialSetting');
        // sms_setting
        $this->middleware(['permission:read sms_setting'])->only('getSmsSetting');
        $this->middleware(['permission:update sms_setting'])->only('updateSmsSetting');
        // Payment Gateway Settings
        $this->middleware(['permission:read payment_gateway_setting'])->only('getPaymentGatewaySetting');
        $this->middleware(['permission:update payment_gateway_setting'])->only('updatePaymentGatewaySetting');
    }

    public function getSettings()
    {
        $settings     = new stdClass();
        $get_settings = Setting::whereIn('key', ['inventory_alert'])->where('category', 'general_settings')->get();
        foreach ($get_settings as $s):
            $settings->{$s->key} = $s->value;
        endforeach;
        return view('content.settings.edit', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $data = $request->validate([
            'inventory_alert' => 'required|integer|min:1',
        ]);
        foreach ($data as $k => $v):
            Setting::updateOrCreate(['key' => $k, 'category' => 'general_settings'], ['value' => $v]);
        endforeach;
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('update_settings_success'), 'ip_address' => $request->getClientIp()]);
        session()->flash('status', __('update_settings_success'));
        return redirect(route('settings.edit'));
    }

    public function getEmailSetting()
    {
        $emailSetting = EmailSetting::firstOrCreate(['id' => 1]);
        return view('content.settings.email-edit', compact('emailSetting'));
    }

    public function updateEmailSetting(Request $request)
    {
        $emailSetting = EmailSetting::firstOrCreate(['id' => 1]);

        $data = $request->validate([
            'mail_mailer'     => 'nullable|string|max:80',
            'mail_host'       => 'nullable|string|max:80',
            'mail_port'       => 'nullable|string|max:80',
            'mail_username'   => 'nullable|string|max:255',
            'mail_password'   => 'nullable|string|max:255',
            'mail_encryption' => 'nullable|string|max:255',
            'from_name'       => 'nullable|string|max:80',
            'from_address'    => 'nullable|string|max:80',
        ]);
        $emailSetting->update($data);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('update_email_setting_success'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($emailSetting)]);
        session()->flash('status', __('update_email_setting_success'));
        return redirect(route('settings.email.edit'));
    }

    public function editCredentialSetting()
    {
        $credentialSetting = CredentialSetting::firstOrCreate(['id' => 1]);
        return view('content.settings.credential-edit', compact('credentialSetting'));
    }

    public function updateCredentialSetting(Request $request)
    {
        $credentialSetting = CredentialSetting::firstOrCreate(['id' => 1]);
        $data              = $request->validate([
            'min'    => 'required|integer',
            'expiry' => 'nullable|integer',
        ]);

        $data['mixedCase'] = isset($request->mixedCase) ? 1 : 0;
        $data['letters']   = isset($request->letters) ? 1 : 0;
        $data['numbers']   = isset($request->numbers) ? 1 : 0;
        $data['symbols']   = isset($request->symbols) ? 1 : 0;

        $credentialSetting->update($data);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('update_credential_setting_success'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($credentialSetting)]);
        session()->flash('status', __('update_credential_setting_success'));
        return redirect(route('settings.credential.edit'));
    }

    /**
     * Display the SMS settings form.
     *
     * @return \Illuminate\View\View
     */
    public function getSmsSetting()
    {
        $smsSetting = SmsSettings::firstOrCreate(['id' => 1]);
        return view('content.settings.sms-edit', compact('smsSetting'));
    }

    /**
     * Update the SMS settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSmsSetting(Request $request)
    {
        $smsSetting = SmsSettings::firstOrCreate(['id' => 1]);

        $data = $request->validate([
            'baseUrl'    => 'required|url',
            'userName'   => 'required|string|max:255',
            'apiKey'     => 'required|string|max:255',
            'userSender' => 'required|string|max:255',
        ]);

        $smsSetting->update($data);

        // Log the update
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __('update_sms_setting_success'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($smsSetting),
        ]);

        session()->flash('status', __('update_sms_setting_success'));
        return redirect(route('settings.sms.edit'));
    }

    /**
     * Display the payment gateway settings form.
     *
     * @return \Illuminate\View\View
     */
    public function getPaymentGatewaySetting()
    {
        $paymentGatewaySetting = PaymentGatewaySetting::firstOrCreate(['gateway_name' => 'Urway']);
        return view('content.settings.payment-gateway-edit', compact('paymentGatewaySetting'));
    }

    /**
     * Update the payment gateway settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePaymentGatewaySetting(Request $request)
    {
        $paymentGatewaySetting = PaymentGatewaySetting::firstOrCreate(['gateway_name' => 'Urway']);

        $data = $request->validate([
            'api_key'      => 'required|string|max:255',
            'merchant_id'  => 'required|string|max:255',
            'terminal_id'  => 'required|string|max:255',
            'base_url'     => 'required|url',
            'currency'     => 'required|string|max:3',
            'callback_url' => 'required|url',
            'is_active'    => 'nullable|boolean',
        ]);

        $paymentGatewaySetting->update($data);

        // Log the update
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __('update_payment_gateway_setting_success'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($paymentGatewaySetting),
        ]);

        session()->flash('status', __('update_payment_gateway_setting_success'));
        return redirect(route('settings.payment-gateway.edit'));
    }

}
