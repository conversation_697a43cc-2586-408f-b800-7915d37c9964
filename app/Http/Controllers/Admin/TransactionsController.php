<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\TransactionsDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\FinancialCategory;
use App\Models\Admin\Country;
use App\Models\Admin\Invoice;
use App\Models\Admin\Product;
use App\Models\Admin\Transactions;
use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Http\Request;

class TransactionsController extends Controller
{
    private const NAME = 'transactions';
    private const NAME_S = 'transaction';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('store');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
        $this->middleware(['permission:create cash_in'])->only('store_cash_in');
        $this->middleware(['permission:create cash_out'])->only('store_cash_out');
    }

    public function index(Request $request, TransactionsDataTable $dataTable)
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        $financial_categories = FinancialCategory::whereNotIn('id', [1, 2])->get();
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS', 'financial_categories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $data = $request->validate([
            'searchclient' => 'required|string',
            'client_id' => 'required|integer',
            'phone' => 'required', 'numeric', 'regex:/^[0-9]{11}$/',
            'phone2' => 'nullable', 'numeric', 'regex:/^[0-9]{11}$/',
            'country_id' => 'required|integer',
            'address' => 'required|string',
            'product' => 'required|array',
            'product.*.id' => 'required|integer',
            'product.*.qty' => 'required|integer',
            'product.*.url' => 'nullable|string',
            'order_total' => 'required|numeric',
            'collected' => 'required|numeric',
            'notes' => 'nullable|string',
        ]);
        $data['user_id'] = $user->id;
        $client = User::where('id', $data['client_id'])->where('user_type_id', 2)->first();

        $cont = false;
        $shippingCountry = Country::find($data['country_id']);
        if ($client && $shippingCountry) {
            $data['shipping_fees'] = $shippingCountry->price;
            $data['for_user_id'] = $client->id;
            $productList = [];
            $total = 0;
            foreach ($data['product'] as $v) {
                $product = Product::find($v['id']);
                if ($product) {
                    $price = $user->user_type_id == 4 ? $product->price_bulk : $product->price;
                    $total += $price;
                    $productList[] = [
                        'product_id' => $v['id'],
                        'price' => $price,
                        'qty' => $v['qty'],
                        'design_url' => isset($v['url']) ? $v['url'] : null,
                    ];
                }
            }
            $cont = true;
            if ($cont) {
                $inv_prefix = 'ORD';
                $user_type = $user->user_type_id;
                if (in_array($user_type, [3, 4])) {
                    if ($user->main_account_id) {
                        $main_user = User::find($user->main_account_id);
                        if ($main_user) {
                            $inv_prefix = $main_user->invoice_prefix;
                        }
                    } else {
                        $inv_prefix = $user->invoice_prefix;
                    }
                }
                $countTransactions = Transactions::where('number', 'like', '%' . $inv_prefix . '%')->count();
                $data['number'] = $inv_prefix . ($countTransactions + 1);
                $data['sub_total'] = $total;
                $data['total'] = $data['shipping_fees'] + $total;
                $item = Transactions::create($data);
                if ($item) {
                    $item->products()->createMany($productList);
                    // Create transaction invoice
                    $countInvoices = Invoice::count();
                    $invoice_data = [
                        'number' => 'INV' . ($countInvoices + 1),
                        'user_id' => $user->id,
                        'for_user_id' => $client->id,
                        'financial_category_id' => 2,
                        'relationmodel_type' => get_class($item),
                        'relationmodel_id' => $item->id,
                        'amount' => $data['total'],
                        'paid' => $data['total'],
                        'remaining' => 0,
                        'status' => 1,
                    ];
                    $invoice = Invoice::create($invoice_data);
                    if ($invoice) {
                        $invoice->payments()->create(['amount' => $data['total']]);
                        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item)]);
                        session()->flash('status', __(self::NAME_S . '_created'));
                        return redirect(route(self::NAME . '.index'))->withInput();
                    }
                }
            }
        }
        session()->flash('error', __('something_went_wrong'));
        return redirect()->back()->withInput();
    }

    /**
     * Display the specified resource.
     *
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $item = Transactions::with(['user', 'client', 'products'])->find($id);
        if ($item) {
            $name = self::NAME;
            $nameS = self::NAME_S;
            return view('content.' . self::NAME . '.show', compact('name', 'nameS', 'item'));
        }
        abort(404);
    }

    /**
     * Change the status of specified resource in the storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function change_status(Request $request, $id)
    {
        $item = Transactions::find($id);
        if ($item) {
            $data = $request->validate([
                'status' => 'required|string',
            ]);
            $item->update(['status' => $data['status']]);
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_status_updated') . ' [ ' . (__('status_' . $data['status'])) . ' ]', 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id, 'type' => 2]);
            session()->flash('status', __(self::NAME_S . '_holded'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    /**
     * Store Cash In.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store_cash_in(Request $request)
    {
        $user = auth()->user();
        $data = $request->validate([
            'financial_category_id' => 'required|numeric',
            'amount' => 'required|numeric',
            'paid' => ['nullable', 'numeric',
                request()->filled('amount') ? 'lte:amount' : '',
            ],
            'comment' => 'nullable|string',
        ]);
        if (!$data['paid']):
            $data['paid'] = $data['amount'];
        endif;
        // Create invoice
        $countInvoices = Invoice::count();
        $data['number'] = 'INV' . ($countInvoices + 1);
        $data['user_id'] = $user->id;
        $data['remaining'] = $data['amount'] - $data['paid'];
        $data['status'] = ($data['paid'] < $data['amount'] ? 0 : 1);
        $invoice = Invoice::create($data);
        $invoice->payments()->create(['amount' => $data['paid']]);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($invoice), 'relationmodel_id' => $invoice->id]);
        session()->flash('status', __('cash_in_created'));
        return redirect(route(self::NAME . '.index'));
        abort(404);
    }
    /**
     * Store Cash Out.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store_cash_out(Request $request)
    {
        $user = auth()->user();
        $data = $request->validate([
            'amount' => 'required|numeric',
            'financial_category_id' => 'required|numeric',
            'paid' => ['nullable', 'numeric',
                request()->filled('amount') ? 'lte:amount' : '',
            ],
            'comment' => 'nullable|string',
        ]);
        if (!$data['paid']):
            $data['paid'] = $data['amount'];
        endif;
        // Create invoice
        $countInvoices = Invoice::count();
        $data['number'] = 'INV' . ($countInvoices + 1);
        $data['user_id'] = $user->id;
        $data['remaining'] = $data['amount'] - $data['paid'];
        $data['status'] = ($data['paid'] < $data['amount'] ? 0 : 1);
        $invoice = Invoice::create($data);
        $invoice->payments()->create(['amount' => $data['paid']]);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_created'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($invoice), 'relationmodel_id' => $invoice->id]);
        session()->flash('status', __('cash_out_created'));
        return redirect(route(self::NAME . '.index'));
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Integer  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $item = Transactions::find($id);
        if ($item) {
            $invoice = Invoice::whereHasMorph('relationmodel', [Transactions::class], function ($query) use ($item) {
                $query->where('relationmodel_id', $item->id);
            })->first();
            $invoice->payments()->delete();
            $invoice->delete();
            $item->delete();
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __(self::NAME_S . '_deleted'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($item), 'relationmodel_id' => $item->id]);
            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
