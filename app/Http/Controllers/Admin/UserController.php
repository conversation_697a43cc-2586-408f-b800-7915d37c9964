<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\UsersDataTable;
use App\Http\Controllers\Controller;
use App\Models\AuditLog;
use App\Models\User;
use App\Models\UserTypes;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    private const INDEX = 'users.index';

    public function __construct()
    {
        //permissions access rights
        $this->middleware(['permission:read users'])->only('index');
        $this->middleware(['permission:create users'])->only('create');
        $this->middleware(['permission:update users'])->only('edit');
        $this->middleware(['permission:delete users'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(UsersDataTable $dataTable)
    {
        $types = UserTypes::all();
        return $dataTable->render('content.users.index', compact('types'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $user = auth()->user();
        $roles = Role::all();
        $types = UserTypes::all();
        return view('content.users.create', compact('roles', 'types'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $data = $request->validate([
            'name' => 'required|string|max:80',
            'email' => 'required|email|unique:users,email,NULL,id,deleted_at,NULL',
            'password' => 'required|confirmed|min:6',
            'phone' => ['required', 'numeric', 'regex:/^[0-9]{11}$/', 'unique:users,phone,NULL,id,deleted_at,NULL'],
            'role' => $user->user_type_id == 1 && in_array($request->user_type_id, [3, 4, 5]) ? 'required|integer' : 'nullable',
            'user_type_id' => $user->user_type_id == 1 ? 'required|integer' : 'nullable',
            'store_name' => 'nullable|' . ($user->user_type_id == 1 ? 'required_if:user_type_id,3,4|string|max:80' : ''),
            'invoice_prefix' => 'nullable|' . ($user->user_type_id == 1 ? 'required_if:user_type_id,3,4|string|max:6' : ''),
        ]);
        $data['password'] = bcrypt($request->password);
        $data['added_by'] = $user->id;
        $role_id = $request->role;
        if (in_array($user->user_type_id, [3, 4])) {
            $roles = $user->roles;
            if ($roles) {
                foreach ($roles as $role) {
                    $role_id = $role->id;
                }
            }
            $data['role'] = $role_id;
            $data['user_type_id'] = $user->user_type_id;
            $data['main_account_id'] = $user->main_account_id ? $user->main_account_id : $user->id;
            $data['store_name'] = $user->store_name;
            $data['invoice_prefix'] = $user->invoice_prefix;
        }
        $user = User::create($data);
        $user->assignRole($role_id);
        AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('user_created_successfully'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($user)]);
        session()->flash('status', __('user_created_successfully'));
        return redirect(route(self::INDEX));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = User::find($id);
        if ($user) {
            if ($user->hasRole('super_admin')) {
                abort(401);
            }
            if (auth()->user()->id != $id) {
                $roles = Role::all();
                $types = UserTypes::all();
                return view('content.users.edit', compact('user', 'roles', 'types'));
            }
        }
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $user = User::find($id);
        if ($user) {
            $data = $request->validate([
                'name' => 'required|string|max:80',
                'email' => ['required', 'email', Rule::unique('users', 'email')->ignore($user->id, 'id')],
                'phone' => ['required', 'numeric', 'regex:/^[0-9]{11}$/', Rule::unique('users', 'phone')->ignore($user->id, 'id')],
                'role' => 'required|integer',
                'user_type_id' => 'required|integer',
                'password' => 'nullable|confirmed|min:6',
            ]);
            $data['password'] = isset($request->password) ? bcrypt($request->password) : $user->password;
            $user->update($data);
            $user->syncRoles($request->role);
            AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('user_updated_successfully'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($user)]);
            session()->flash('status', __('user_updated_successfully'));
            return redirect(route(self::INDEX));
        }
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $user = User::find($id);
        if ($user) {
            if (!$user->hasRole('super_admin') || auth()->user()->id != $id) {
                $user->delete();
                AuditLog::create(['user_id' => auth()->user()->id, 'description' => __('user_deleted_successfully'), 'ip_address' => $request->getClientIp(), 'relationmodel_type' => get_class($user)]);
                session()->flash('status', __('user_deleted_successfully'));
                return redirect(route(self::INDEX));
            }
        }
        abort(404);
    }

    /**
     * create user by Ajax request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function create_ajax(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:80',
            'phone' => ['required', 'numeric', 'regex:/^[0-9]{11}$/', 'unique:users,phone,NULL,id,deleted_at,NULL'],
            'address' => 'required|string',
            'country_id' => 'required|integer',
        ]);
        $data['email'] = $data['phone'] . '@tb3nalk.com';
        $data['password'] = bcrypt('TB@123456');
        $data['added_by'] = auth()->user()->id;
        $data['user_type_id'] = 2;
        $item = User::create($data);
        return response()->json(array('success' => true, 'data' => ['item' => $item]));
    }

    /**
     * return users list for Ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function get_list()
    {
        $list = User::where('user_type_id', 2)->get();
        return response()->json(array('success' => true, 'data' => ['list' => $list]));
    }

    /**
     * Search auto complete.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function autoComplete(Request $request)
    {
        $term = $request->term;
        $results = [];
        $results[] = ['id' => '', 'title' => $term, 'value' => __('add_client')];
        $queries = User::where('user_type_id', 2)->where(
            function ($query) use ($term) {
                $query->orWhere('name', 'like', '%' . $term . '%');
                $query->orWhere('phone', 'like', '%' . $term . '%');
            }
        )->get();
        foreach ($queries as $query):
            $results[] = ['id' => $query->id, 'value' => $query->name, 'phone' => $query->phone, 'address' => $query->address, 'country_id' => $query->country_id];
        endforeach;
        return response()->json($results);
    }

}
