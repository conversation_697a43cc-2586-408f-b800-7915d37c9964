<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Http\Response;

class Controller extends BaseController {

    use AuthorizesRequests,
        DispatchesJobs,
        ValidatesRequests;

    public function ApiResponse($status, $message, $data = [], $code = NULL): Response {
        // 'server' => config('app.server'), // Response server ip
        $response = [
            'code' => $code,
            'status' => $status,
            'message' => $message,
            'data' => !empty($data) ? $data : null
        ];

        return response($response, $code);
    }

}
