<?php
namespace App\Http\Middleware;

use App\Models\Admin\ServiceCategoryItem;
use Closure;
use Illuminate\Http\Request;

class EnsureItemIsOwned
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth('api')->user();

        if (! $user) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $itemId = $request->route('id');

        $item = ServiceCategoryItem::find($itemId);

        if (! $item) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        if ($item->user_id !== $user->id) {
            return response()->json(['message' => 'You do not own this item'], 403);
        }

        return $next($request);
    }
}
