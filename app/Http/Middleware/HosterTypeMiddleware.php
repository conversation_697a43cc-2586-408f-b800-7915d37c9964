<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class HosterTypeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth('api')->user();

        if (! $user || $user->user_type_id != 2) {
            // Optionally redirect or return 403
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return $next($request);
    }
}
