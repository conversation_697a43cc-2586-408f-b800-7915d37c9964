<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Auth;

class LocaleMiddleware {

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next) {
        // available language in template array
        $availLocale = ['en' => 'en', 'ar' => 'ar'];

        // Locale is enabled and allowed to be change
        if (Auth::user()) {
            App::setLocale(session()->get('locale', Auth::user()->lang));
        } elseif (session()->has('locale') && array_key_exists(session()->get('locale'), $availLocale)) {
            // Set the Laravel locale
            App::setLocale(session()->get('locale'));
        }

        return $next($request);
    }

}
