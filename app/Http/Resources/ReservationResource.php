<?php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ReservationResource extends JsonResource
{
    /**
     * User resource construct.
     *
     * @param  mixed  $collection
     * @param  \Laravel\Passport\Token|null  $token
     * @param  String|null  $accessToken
     */
    public function __construct($collection)
    {
        parent::__construct($collection);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $resource = [
            'id'         => (int) $this->id,
            'item'       => new ServiceCategoryItemResource($this->serviceCategoryItem),
            'created_at' => $this->created_at,
        ];
        return $resource;
    }

}
