<?php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ServiceCategoryItemGalleryResource extends JsonResource
{
    /**
     * User resource construct.
     *
     * @param  mixed  $collection
     * @param  \Laravel\Passport\Token|null  $token
     * @param  String|null  $accessToken
     */
    public function __construct($collection)
    {
        parent::__construct($collection);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $resource = [
            'id'         => (int) $this->id,
            'image'      => $this->image ? asset(path : 'storage/' . $this->image): null,
            'cover'      => $this->cover,
            'created_at' => $this->created_at,
        ];
        return $resource;
    }

}
