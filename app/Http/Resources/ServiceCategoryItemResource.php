<?php
namespace App\Http\Resources;

use App\Models\Admin\ServiceCategoryItemFavorite;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceCategoryItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $lang       = $request->hasHeader('X-localization') && in_array($request->Header('X-localization'), ['ar', 'en']) ? $request->Header('X-localization') : 'ar';
        $user       = auth('api')->user();
        $isFavorite = false;

        if ($user) {
            $fav = ServiceCategoryItemFavorite::where([
                'user_id'                  => $user->id,
                'service_category_item_id' => $this->id,
            ])->first();

            $isFavorite = ! is_null($fav);
        }

        return array_merge(parent::toArray($request), [
            'favorite'          => $isFavorite,
            'image'             => ($this->gallery && $this->gallery->isNotEmpty() && $this->gallery->first()->image)
                ? asset('storage/' . $this->gallery->first()->image)
                : asset('assets/default-image.jpg'),
            'gallery'           => $this->gallery ? ServiceCategoryItemGalleryResource::collection($this->gallery) : [],
            'facilities'        => $this->facilities ? ServiceCategoryItemFacilitiesResource::collection($this->facilities) : [],
            'country'           => $this->city->country->{'title_' . $lang} ?? null,
            'city'              => $this->city->{'title_' . $lang} ?? null,
            'lat'               => $this->lat,
            'lon'               => $this->lon,
            'price'             => $this->price,
            'rating'            => $this->rating,      // Assuming a rating attribute exists
            'no_of_rates'       => $this->no_of_rates, // Assuming a no_of_rates attribute exists
            'title'             => $this->title,
            'content'           => $this->content,
            'no_guests'         => $this->no_guests, // Assuming a no_guests attribute exists
            'beds'              => $this->beds,      // Assuming a beds attribute exists
            'baths'             => $this->baths,     // Assuming a baths attribute exists
            'hoster'            => [
                'rating'            => $this->user->rating ?? null, // Assuming user relationship has a rating attribute
                'name'              => $this->user->name ?? null,
                'registered_since'  => $this->user->created_at ?? null,
                'total_no_of_rates' => $this->user->total_no_of_rates ?? null, // Assuming user has total_no_of_rates
            ],
            'booking_rules'     => $this->booking_rules,     // Assuming a booking_rules attribute exists
            'cancelation_rules' => $this->cancelation_rules, // Assuming a cancelation_rules attribute exists
        ]);
    }
}
