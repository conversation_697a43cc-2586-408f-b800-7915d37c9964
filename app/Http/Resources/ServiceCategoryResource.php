<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ServiceCategoryResource extends JsonResource
{
    /**
     * User resource construct.
     *
     * @param  mixed  $collection
     * @param  \Laravel\Passport\Token|null  $token
     * @param  String|null  $accessToken
     */
    public function __construct($collection)
    {
        parent::__construct($collection);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $lang = $request->hasHeader('X-localization') && in_array($request->Header('X-localization'), ['ar', 'en']) ? $request->Header('X-localization') : 'ar';
        $resource = [
                'id' => (int) $this->id,
                'title' => $this->{'title_' . $lang},
                'icon' => $this->icon ? asset('storage/' . $this->icon) : null,
                'image' => $this->image ? asset('storage/' . $this->image) : null,
                'order' => $this->order,
                'reservation_type' => $this->reservation_type,
                'reservation_confirmation' => $this->reservation_confirmation,
                'enable_register' => $this->enable_register,
                'can_add_item' => $this->can_add_item,
                'commission' => $this->commission,
                'parent' => $this->parent,
                'created_at' => $this->created_at,
        ];
        return $resource;
    }

}
