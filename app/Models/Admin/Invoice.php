<?php

namespace App\Models\Admin;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    public function payments()
    {
        return $this->hasMany(InvoicePayments::class, 'invoice_id', 'id');
    }

    public function relationmodel()
    {
        return $this->morphTo();
    }

    public function relatedmodelfor()
    {
        return $this->morphTo();
    }

    public function category()
    {
        return $this->belongsTo(FinancialCategory::class, 'financial_category_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function for()
    {
        return $this->belongsTo(User::class, 'for_user_id', 'id');
    }
}
