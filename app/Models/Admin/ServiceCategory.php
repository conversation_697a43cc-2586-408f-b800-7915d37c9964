<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategoryDocument;
use App\Models\Admin\ServiceCategoryField;
use App\Models\Admin\ServiceCategoryForm;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\UserTypes;

class ServiceCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title_en',
        'title_ar',
        'icon',
        'image',
        'order',
        'reservation_type',
        'reservation_confirmation',
        'enable_register',
        'can_add_item',
        'commission',
        'user_type_id',
        'parent',
    ];

    public function userType()
    {
        return $this->belongsTo(UserTypes::class, 'user_type_id');
    }

    public function parentCategory()
    {
        return $this->belongsTo(ServiceCategory::class, 'parent');
    }

    public function childrenCategories()
    {
        return $this->hasMany(ServiceCategory::class, 'parent');
    }

    public function forms()
    {
        return $this->hasMany(ServiceCategoryForm::class);
    }

    public function items()
    {
        return $this->hasMany(ServiceCategoryItem::class);
    }
}