<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceCategoryDocument extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'service_category_documents';

    protected $fillable = [
        'title_en',
        'title_ar',
        'icon',
        'example',
        'display',
        'required',
        'multiple',
    ];

    protected $casts = [
        'display'  => 'boolean',
        'required' => 'boolean',
        'multiple' => 'boolean',
    ];
}