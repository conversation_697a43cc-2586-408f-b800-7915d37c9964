<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategory;
use App\Models\Admin\ServiceCategoryFieldOption;
class ServiceCategoryField extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title_en',
        'title_ar',
        'type',
        'icon',
        'display',
        'required',
    ];

    public function options()
    {
        return $this->hasMany(ServiceCategoryFieldOption::class);
    }
    
    // Helper method to get input type
    public function getInputType()
    {
        switch ($this->type) {
            case 1: return 'text';
            case 2: return 'number';
            case 3: return 'number';
            case 4: return 'tel';
            case 5: return 'password';
            case 6: return 'password';
            case 9: return 'textarea';
            case 10: return 'date';
            case 11: return 'datetime-local';
            case 12: return 'time';
            default: return 'text';
        }
    }
}