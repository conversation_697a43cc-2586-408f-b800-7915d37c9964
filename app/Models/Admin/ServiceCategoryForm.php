<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategory;
use App\Models\Admin\ServiceCategoryFormItem;

class ServiceCategoryForm extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'type',
        'service_category_id',
    ];

    public function serviceCategory()
    {
        return $this->belongsTo(ServiceCategory::class);
    }

    public function formItems()
    {
        return $this->hasMany(ServiceCategoryFormItem::class);
    }
}