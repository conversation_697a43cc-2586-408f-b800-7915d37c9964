<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategoryForm;
use App\Models\Admin\ServiceCategoryDocument;
use App\Models\Admin\ServiceCategoryField;

class ServiceCategoryFormItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'order',
        'service_category_form_id',
        'service_category_document_id',
        'service_category_field_id',
    ];

    public function form()
    {
        return $this->belongsTo(ServiceCategoryForm::class, 'service_category_form_id');
    }

    public function document()
    {
        return $this->belongsTo(ServiceCategoryDocument::class, 'service_category_document_id');
    }

    public function field()
    {
        return $this->belongsTo(ServiceCategoryField::class, 'service_category_field_id');
    }
}