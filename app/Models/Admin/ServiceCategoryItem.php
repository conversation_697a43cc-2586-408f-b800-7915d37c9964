<?php
namespace App\Models\Admin;

use App\Models\Admin\ServiceCategory;
use App\Models\Admin\ServiceCategoryItemDisablePeriod;
use App\Models\Admin\ServiceCategoryItemDocument;
use App\Models\Admin\ServiceCategoryItemField;
use App\Models\Admin\ServiceCategoryItemOffer;
use App\Models\Admin\ServiceCategoryItemReservation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceCategoryItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'city_id',
        'title',   // Single title field
        'content', // Single content field
        'image',   // Image field
        'video',   // Video field
        'price',
        'weekend_price',
        'week_price',
        'month_price',
        'lat',
        'lon',
        'active',
        'service_category_id',
        'service_category_form_id',
        'user_id',
        'views',
        'rating',
        'no_of_rates',
        'no_guests',
        'beds',
        'baths',
        'booking_rules',
        'cancelation_rules',
    ];

    public function incrementViews()
    {
        $this->increment('views');
    }

    public function serviceCategory()
    {
        return $this->belongsTo(ServiceCategory::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function documents()
    {
        return $this->hasMany(ServiceCategoryItemDocument::class);
    }

    public function fields()
    {
        return $this->hasMany(ServiceCategoryItemField::class);
    }

    public function reservations()
    {
        return $this->hasMany(ServiceCategoryItemReservation::class);
    }

    public function offers()
    {
        return $this->hasMany(ServiceCategoryItemOffer::class);
    }

    public function disablePeriods()
    {
        return $this->hasMany(ServiceCategoryItemDisablePeriod::class);
    }

    public function gallery()
    {
        return $this->hasMany(ServiceCategoryItemGallery::class);
    }

    public function facilities()
    {
        return $this->hasMany(ServiceCategoryItemFacilities::class);
    }

    public function getVideoAttribute($value)
    {
        return $value ? url('storage/' . $value) : null;
    }

    public function getImageAttribute($value)
    {
        return $value ? url('storage/' . $value) : null;
    }
}
