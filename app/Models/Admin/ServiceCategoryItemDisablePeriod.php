<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategoryItem;

class ServiceCategoryItemDisablePeriod extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'disable_start',
        'disable_end',
        'service_category_item_id',
    ];

    protected $table = 'service_category_item_disable_period';

    public function serviceCategoryItem()
    {
        return $this->belongsTo(ServiceCategoryItem::class);
    }
}