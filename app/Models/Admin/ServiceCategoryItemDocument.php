<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategoryItem;

class ServiceCategoryItemDocument extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'value',
        'icon',
        'example',
        'display',
        'required',
        'multiple',
        'service_category_item_id',
    ];

    public function serviceCategoryItem()
    {
        return $this->belongsTo(ServiceCategoryItem::class);
    }
}