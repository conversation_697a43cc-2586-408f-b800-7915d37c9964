<?php
namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceCategoryItemFacilities extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function serviceCategoryItem()
    {
        return $this->belongsTo(ServiceCategoryItem::class);
    }

    public function facility()
    {
        return $this->belongsTo(Facilities::class);
    }
}
