<?php
namespace App\Models\Admin;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceCategoryItemFavorite extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'service_category_item_id',
        'user_id',
    ];

    public function serviceCategoryItem()
    {
        return $this->belongsTo(ServiceCategoryItem::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
