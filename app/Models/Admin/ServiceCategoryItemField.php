<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategoryItem;

class ServiceCategoryItemField extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'value',
        'type',
        'icon',
        'display',
        'required',
        'service_category_item_id',
    ];

    public function serviceCategoryItem()
    {
        return $this->belongsTo(ServiceCategoryItem::class);
    }
}