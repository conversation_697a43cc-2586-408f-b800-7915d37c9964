<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Admin\ServiceCategoryItem;

class ServiceCategoryItemOffer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title_en',
        'title_ar',
        'value',
        'type',
        'apply_to',
        'offer_start',
        'offer_end',
        'service_category_item_id',
    ];

    public function serviceCategoryItem()
    {
        return $this->belongsTo(ServiceCategoryItem::class);
    }
}