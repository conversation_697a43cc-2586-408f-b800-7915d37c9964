<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TransactionProducts extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];
    public function transaction()
    {
        return $this->belongsTo(Transactions::class, 'transaction_id', 'id')->withTrashed();
    }
    public function item()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id')->withTrashed();
    }

}
