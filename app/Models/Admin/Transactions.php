<?php

namespace App\Models\Admin;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transactions extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    public function client()
    {
        return $this->belongsTo(User::class, 'for_user_id', 'id')->withTrashed();
    }
    
    public function shipping()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id')->withTrashed();
    }

    public function products()
    {
        return $this->hasMany(TransactionProducts::class, 'transaction_id', 'id')->withTrashed();
    }

    public function invoice()
    {
        return $this->morphOne(Invoice::class, 'relationmodel')->withTrashed();
    }
}
