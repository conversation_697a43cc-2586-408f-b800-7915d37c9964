<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AuditLog extends Model {

    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'description',
        'ip_address',
        'relationmodel_type',
        'relationmodel_id',
        'type',
    ];

    public function relationmodel() {
        return $this->morphTo();
    }
    
    public function user() {
        return $this->belongsTo(User::class);
    }

}
