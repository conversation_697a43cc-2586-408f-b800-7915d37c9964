<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentGatewaySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'gateway_name', // e.g., 'Urway'
        'api_key',
        'merchant_id',
        'terminal_id',
        'base_url', // Sandbox or Production URL
        'currency', // e.g., 'SAR'
        'callback_url',
        'is_active', // Enable/Disable the gateway
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}