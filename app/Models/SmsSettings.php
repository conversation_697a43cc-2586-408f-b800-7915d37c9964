<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class SmsSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'baseUrl',
        'userName',
        'apiKey',
        'userSender',
    ];

    protected $casts = [
        'baseUrl' => 'string',
        'userName' => 'string',
        'apiKey' => 'string',
        'userSender' => 'string',
    ];

    protected $attributes = [
        'userSender' => 'DefaultSender',
    ];

    public static function rules($id = null)
    {
        return [
            'baseUrl' => 'required|url',
            'userName' => 'required|string|max:255',
            'apiKey' => 'required|string|max:255',
            'userSender' => 'required|string|max:255',
        ];
    }

    public function setApiKeyAttribute($value)
    {
        $this->attributes['apiKey'] = Crypt::encryptString($value);
    }

    public function getApiKeyAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    
}