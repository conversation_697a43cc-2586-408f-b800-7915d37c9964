<?php
namespace App\Rules;

use App\Models\Admin\ServiceCategoryItemReservation;
use Illuminate\Contracts\Validation\Rule;

class CheckReservationAvailability implements Rule
{
    protected $serviceCategoryItemId;
    protected $reservationFrom;
    protected $reservationTo;

    public function __construct($serviceCategoryItemId, $reservationFrom, $reservationTo)
    {
        $this->serviceCategoryItemId = $serviceCategoryItemId;
        $this->reservationFrom       = $reservationFrom;
        $this->reservationTo         = $reservationTo;
    }

    public function passes($attribute, $value)
    {
        $overlapExists = ServiceCategoryItemReservation::where('service_category_item_id', $this->serviceCategoryItemId)
            ->where(function ($query) {
                $query->whereBetween('reservation_from', [$this->reservationFrom, $this->reservationTo])
                    ->orWhereBetween('reservation_to', [$this->reservationFrom, $this->reservationTo])
                    ->orWhere(function ($query) {
                        $query->where('reservation_from', '<', $this->reservationFrom)
                            ->where('reservation_to', '>', $this->reservationTo);
                    });
            })
            ->exists();

        // Debugging
        if ($overlapExists) {
            \Log::info('Overlap detected', [
                'reservation_from'         => $this->reservationFrom,
                'reservation_to'           => $this->reservationTo,
                'service_category_item_id' => $this->serviceCategoryItemId,
            ]);
        }

        return ! $overlapExists;
    }

    public function message()
    {
        return 'The selected reservation period is not available.';
    }
}
