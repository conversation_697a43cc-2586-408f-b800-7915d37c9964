<?php

namespace App\Services\Notifications;

use Notification;
use App\Notifications\SendFcmNotification;

/**
 * Class FcmService.
 */
class FcmService
{
    public static function send($title, $body, $fcmTokens)
    {
        try {
            Notification::send(null, new SendPushNotification($title, $body, $fcmTokens));
            return true;
        } catch(\Exception $e) {
            report($e);
            return false;
        }

    }
}
