{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "fruitcake/laravel-cors": "^2.0.5", "guzzlehttp/guzzle": "^7.2", "intervention/image": "*", "intervention/image-laravel": "^1.5", "laravel/framework": "^9.0", "laravel/jetstream": "2.8.5", "laravel/passport": "^12.4", "laravel/sanctum": "^2.14", "laravel/tinker": "^2.7", "livewire/livewire": "^2.5", "maatwebsite/excel": "^3.1", "matanyadaev/laravel-eloquent-spatial": "^2.10", "spatie/laravel-permission": "^5.9", "yajra/laravel-datatables": "^9.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "pixinvent/vuexy-laravel-bootstrap-jetstream": "dev-master", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}