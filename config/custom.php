<?php
// Set your timezone
date_default_timezone_set('Africa/Cairo'); // Change this to your time zone

// Get current time (Unix timestamp)
$current_time = time();

// Get sunrise and sunset times for today
$sunrise = strtotime(date('Y-m-d') . ' 06:00:00'); // Example sunrise at 6 AM
$sunset = strtotime(date('Y-m-d') . ' 18:00:00');  // Example sunset at 6 PM

// Determine if it's day or night
if ($current_time >= $sunset || $current_time <= $sunrise) {
    // It's nighttime, activate dark theme
    $dark = true;
} else {
    // It's daytime, use light theme
    $dark = false;
}
// Custom Config
// -------------------------------------------------------------------------------------
//! IMPORTANT: Make sure you clear the browser local storage In order to see the config changes in the template.
//! To clear local storage: (https://www.leadshook.com/help/how-to-clear-local-storage-in-google-chrome-browser/).

return [
  'custom' => [
    'myLayout' => 'vertical', // Options[String]: vertical(default), horizontal
    'myTheme' => 'theme-bordered', // Options[String]: theme-default(default), theme-bordered, theme-semi-dark
    'myStyle' => $dark ? 'dark' :'light', // Options[String]: light(default), dark
    'myRTLSupport' => true, // options[Boolean]: true(default), false // To provide RTLSupport or not
    'myRTLMode' => false, // options[Boolean]: false(default), true // To set layout to RTL layout  (myRTLSupport must be true for rtl mode)
    'hasCustomizer' => false, // options[Boolean]: true(default), false // Display customizer or not THIS WILL REMOVE INCLUDED JS FILE. SO LOCAL STORAGE WON'T WORK
    'displayCustomizer' => false, // options[Boolean]: true(default), false // Display customizer UI or not, THIS WON'T REMOVE INCLUDED JS FILE. SO LOCAL STORAGE WILL WORK
    'menuFixed' => true, // options[Boolean]: true(default), false // Layout(menu) Fixed
    'menuCollapsed' => true, // options[Boolean]: false(default), true // Show menu collapsed, Only for vertical Layout
    'navbarFixed' => true, // options[Boolean]: false(default), true // Navbar Fixed
    'footerFixed' => true, // options[Boolean]: false(default), true // Footer Fixed
    'menuFlipped' => false, // options[Boolean]: false(default), true // Show flipped menu, Only for vertical Layout
    // 'menuOffcanvas' => false, // options[Boolean]: false(default), true // Show Offcanvas menu, Only for vertical Layout
    'showDropdownOnHover' => true, // true, false (for horizontal layout only)
    'customizerControls' => [
      'rtl',
      'style',
      'layoutType',
      'showDropdownOnHover',
      'layoutNavbarFixed',
      'layoutFooterFixed',
      'themes',
    ], // To show/hide customizer options
  ],
];
