<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_types', function (Blueprint $table) {
            $table->id();
            $table->string('title_en', 45)->nullable();
            $table->string('title_ar', 45)->nullable();
            $table->text('icon')->nullable();
            $table->boolean('enable_register')->default(false);
            $table->tinyInteger('register_type')->default(1)->comment('1-> Client, 2-> Provider');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_types');
    }
};