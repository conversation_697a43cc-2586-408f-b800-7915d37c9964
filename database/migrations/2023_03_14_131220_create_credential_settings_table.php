<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('credential_settings', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('min')->nullable();
            $table->bigInteger('max')->nullable();
            $table->boolean('mixedCase')->default(0)->comment('Require at least one uppercase and one lowercase letter');
            $table->boolean('letters')->default(0)->comment('Require at least one letter');
            $table->boolean('numbers')->default(0)->comment('Require at least one number');
            $table->boolean('symbols')->default(0)->comment('Require at least one symbol');
            $table->bigInteger('expiry')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('credential_settings');
    }
};
