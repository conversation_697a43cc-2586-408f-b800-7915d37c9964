<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration
{
    public function up()
    {
        Schema::create('service_categories', function (Blueprint $table) {
            $table->id();
            $table->string('title_en', 255)->nullable();
            $table->string('title_ar', 255)->nullable();
            $table->text('icon')->nullable();
            $table->integer('order')->default(0);
            $table->tinyInteger('reservation_type')->nullable();
            $table->boolean('reservation_confirmation')->default(false)->comment('0->No, 1->Yes');
            $table->boolean('enable_register')->default(false);
            $table->string('can_add_item', 45)->default('0');
            $table->double('commission')->nullable();
            $table->foreignId('user_type_id')->constrained('user_types');
            $table->foreignId('parent')->nullable()->constrained('service_categories');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_categories');
    }
};