<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_documents', function (Blueprint $table) {
            $table->id();
            $table->string('title_en', 255)->nullable();
            $table->string('title_ar', 255)->nullable();
            $table->text('icon')->nullable();
            $table->text('example')->nullable()->default('0');
            $table->boolean('display')->default(false)->comment('0->No, 1->Yes');
            $table->boolean('required')->default(false);
            $table->boolean('multiple')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_documents');
    }
};