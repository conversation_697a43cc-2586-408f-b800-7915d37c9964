<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_fields', function (Blueprint $table) {
            $table->id();
            $table->string('title_en', 255)->nullable();
            $table->string('title_ar', 255)->nullable();
            $table->tinyInteger('type')->default(1)->comment('1-> Text, 2-> Numeric, 3-> Numeric Decimal, 4-> Phone, 5-> Password Text, 6-> Password Numeric, 7-> Checkbox, 8-> Radio, 9-> Text Area, 10-> Date, 11-> DateTime, 12-> Time, 13->Date From and To, 14->DateTime From and To');
            $table->text('icon')->nullable();
            $table->boolean('display')->default(false);
            $table->boolean('required')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_fields');
    }
};