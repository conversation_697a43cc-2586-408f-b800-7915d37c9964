<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_forms', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('type')->default(1)->comment('1-> Register Form, 2-> Add Form, 3-> Reservation Form');
            $table->foreignId('service_category_id')->constrained('service_categories');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_forms');
    }
};