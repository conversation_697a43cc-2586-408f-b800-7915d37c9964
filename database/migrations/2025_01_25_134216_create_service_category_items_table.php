<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_items', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // Single title field
            $table->text('content')->nullable(); // Single content field
            $table->text('image')->nullable(); // Image field
            $table->text('video')->nullable(); // Video field
            $table->double('price', 10, 2)->default(0);
            $table->double('weekend_price', 10, 2)->default(0);
            $table->double('lat')->nullable();
            $table->double('lon')->nullable();
            $table->boolean('confirmation')->default(false)->comment('0->No, 1->Yes');
            $table->boolean('active')->default(false)->comment('0->No, 1->Yes');
            $table->foreignId('service_category_id')->constrained('service_categories');
            $table->foreignId('service_category_form_id')->nullable()->constrained('service_category_forms');
            $table->foreignId('user_id')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_items');
    }
};