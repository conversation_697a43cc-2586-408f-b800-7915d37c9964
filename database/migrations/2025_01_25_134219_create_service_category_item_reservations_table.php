<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_item_reservations', function (Blueprint $table) {
            $table->id();
            $table->date('reservation_date')->nullable();
            $table->dateTime('reservation_from')->nullable();
            $table->dateTime('reservation_to')->nullable();
            $table->tinyInteger('confirmed')->nullable()->comment('0->Pending, 1->Confirmed, 2->Automated Confirmation');
            
            // Use a shorter foreign key constraint name
            $table->foreignId('service_category_item_id')
                  ->constrained('service_category_items')
                  ->onDelete('cascade')
                  ->name('fk_reservations_item_id'); // Custom constraint name

            $table->foreignId('service_category_form_id')
                  ->nullable()
                  ->constrained('service_category_forms')
                  ->onDelete('set null')
                  ->name('fk_reservations_form_id'); // Custom constraint name

            $table->foreignId('user_id')
                  ->nullable()
                  ->constrained('users')
                  ->onDelete('set null')
                  ->name('fk_reservations_user_id'); // Custom constraint name

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_item_reservations');
    }
};