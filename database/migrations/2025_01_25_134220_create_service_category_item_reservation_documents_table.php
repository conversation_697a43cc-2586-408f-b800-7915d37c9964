<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_item_reservation_documents', function (Blueprint $table) {
            $table->id();
            $table->text('value')->nullable();
            $table->text('icon')->nullable();
            $table->text('example')->nullable()->default('0');
            $table->boolean('display')->default(false)->comment('0->No, 1->Yes');
            $table->boolean('required')->default(false);
            $table->boolean('multiple')->default(false);

            // Use a shorter foreign key constraint name
            $table->foreignId('service_category_item_reservation_id')
                  ->constrained('service_category_item_reservations')
                  ->onDelete('cascade')
                  ->name('fk_reservation_docs_reservation_id'); // Custom constraint name

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_item_reservation_documents');
    }
};