<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_item_reservation_fields', function (Blueprint $table) {
            $table->id();
            $table->text('value')->nullable();
            $table->tinyInteger('type')->default(1)->comment('1-> Text, 2-> Numeric, 3-> Numeric Decimal, 4-> Phone, 5-> Password Text, 6-> Password Numeric, 7-> Checkbox, 8-> Radio, 9-> Text Area, 10-> Date, 11-> DateTime, 12-> Time, 13->Date From and To, 14->DateTime From and To');
            $table->text('icon')->nullable();
            $table->boolean('display')->default(false);
            $table->boolean('required')->default(false);

            // Use a shorter foreign key constraint name
            $table->foreignId('service_category_item_reservation_id')
                  ->constrained('service_category_item_reservations')
                  ->onDelete('cascade')
                  ->name('fk_reservation_fields_reservation_id'); // Custom constraint name

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_item_reservation_fields');
    }
};