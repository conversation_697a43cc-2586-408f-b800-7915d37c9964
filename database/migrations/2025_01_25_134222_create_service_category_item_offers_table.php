<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_item_offers', function (Blueprint $table) {
            $table->id();
            $table->string('title_en', 45)->nullable()->default('0');
            $table->string('title_ar', 45)->nullable()->default('0');
            $table->double('value')->nullable();
            $table->tinyInteger('type')->default(1)->comment('1->Percentage, 2->Fixed');
            $table->tinyInteger('apply_to')->default(0)->comment('0->None, 1->Price, 2->Weekend Price');
            $table->dateTime('offer_start')->nullable();
            $table->dateTime('offer_end')->nullable();
            $table->foreignId('service_category_item_id')->constrained('service_category_items');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_item_offers');
    }
};