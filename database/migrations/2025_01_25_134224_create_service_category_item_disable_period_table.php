<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_item_disable_period', function (Blueprint $table) {
            $table->id();
            $table->dateTime('disable_start')->nullable();
            $table->dateTime('disable_end')->nullable();

            // Use a shorter foreign key constraint name
            $table->foreignId('service_category_item_id')
                  ->constrained('service_category_items')
                  ->onDelete('cascade')
                  ->name('fk_disable_period_item_id'); // Custom constraint name

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_item_disable_period');
    }
};