<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('service_category_form_items', function (Blueprint $table) {
            $table->id();
            $table->integer('order')->default(0);
            $table->foreignId('service_category_form_id')->constrained('service_category_forms');
            $table->foreignId('service_category_document_id')->nullable()->constrained('service_category_documents');
            $table->foreignId('service_category_field_id')->nullable()->constrained('service_category_fields');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_category_form_items');
    }
};