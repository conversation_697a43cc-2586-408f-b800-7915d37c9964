<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_gateway_settings', function (Blueprint $table) {
            $table->id();
            $table->string('gateway_name')->default('Urway'); // Name of the payment gateway (e.g., Urway)
            $table->string('api_key')->nullable();                        // API Key for the payment gateway
            $table->string('merchant_id')->nullable();                    // Merchant ID for the payment gateway
            $table->string('terminal_id')->nullable();                    // Terminal ID for the payment gateway
            $table->string('base_url')->nullable();                       // Base URL for the payment gateway (e.g., Sandbox or Production)
            $table->string('currency', 3)->default('SAR');    // Currency code (e.g., SAR)
            $table->string('callback_url')->nullable();                   // Callback URL for payment notifications
            $table->boolean('is_active')->default(true);      // Enable/Disable the payment gateway
            $table->timestamps();                             // Created at and Updated at timestamps
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_settings');
    }
};
