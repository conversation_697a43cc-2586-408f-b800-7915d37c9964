<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_category_item_reservations', function (Blueprint $table) {
            // Add a composite index with a custom name
            $table->index(['reservation_from', 'reservation_to'], 'reservations_from_to_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('service_category_item_reservations', function (Blueprint $table) {
            // Drop the index using the custom name
            $table->dropIndex('reservations_from_to_index');
        });
    }
};
