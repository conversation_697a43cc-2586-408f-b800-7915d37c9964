<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_category_item_fields', function (Blueprint $table) {
            $table->foreignId(column: 'service_category_field_id')->nullable()->constrained('service_category_fields');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('service_category_item_fields', function (Blueprint $table) {
            //
        });
    }
};
