<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('service_category_item_galleries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_category_item_id')->nullable()
                ->constrained('service_category_items')
                ->onDelete('set null')
                ->name('sci_gallery_sci_id_fk'); // Shortened name
            $table->text('image')->nullable();
            $table->boolean('cover')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('service_category_item_galleries');
    }
};
