<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            $table->decimal('rating', 3, 2)->nullable();
            $table->integer('no_of_rates')->nullable();
            $table->integer('no_guests')->nullable();
            $table->integer('beds')->nullable();
            $table->integer('baths')->nullable();
            $table->text('booking_rules')->nullable();
            $table->text('cancelation_rules')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            //
        });
    }
};
