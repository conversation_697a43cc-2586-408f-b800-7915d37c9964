<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            $table->foreignId('city_id')->nullable()->after('id')
                ->constrained('cities')
                ->onUpdate('cascade')
                ->onDelete('set null')
                ->name('sci_cities_sci_city_id_fk'); // Shortened name
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            //
        });
    }
};
