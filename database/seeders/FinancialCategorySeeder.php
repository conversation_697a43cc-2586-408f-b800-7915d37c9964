<?php

namespace Database\Seeders;

use App\Models\Admin\FinancialCategory;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class FinancialCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [];
        $data[] = ['title_en' => 'Purchase', 'title_ar' => 'مشتريات', 'deduct' => 1, 'created_at' => Carbon::now()->addSeconds(1), 'updated_at' =>  Carbon::now()->addSeconds(1)];
        $data[] = ['title_en' => 'Sell', 'title_ar' => 'مبيعات', 'deduct' => 0, 'created_at' =>  Carbon::now()->addSeconds(2), 'updated_at' =>  Carbon::now()->addSeconds(2)];
        $data[] = ['title_en' => 'Cash In', 'title_ar' => 'كاش داخل', 'deduct' => 0, 'created_at' =>  Carbon::now()->addSeconds(3), 'updated_at' =>  Carbon::now()->addSeconds(4)];
        $data[] = ['title_en' => 'Cash Out', 'title_ar' => 'كاش خارج', 'deduct' => 1, 'created_at' =>  Carbon::now()->addSeconds(4), 'updated_at' => Carbon::now()->addSeconds(5)];
        FinancialCategory::insert($data);
    }
}
