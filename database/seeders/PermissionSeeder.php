<?php

namespace Database\Seeders;

use App\Models\Level;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Carbon\Carbon;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //all levels in the systems will be added here to can access with permissions by users
        Level::insert([
            ['name' => 'all_data', 'actions' => 'r', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'roles', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'user_types', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'users', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'transactions', 'actions' => 'crd', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'cash_in', 'actions' => 'c', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'cash_out', 'actions' => 'c', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'invoices', 'actions' => 'crd', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'cash_total', 'actions' => 'r', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'attendances', 'actions' => 'crd', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'email_setting', 'actions' => 'ru', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'credential_setting', 'actions' => 'ru', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'settings', 'actions' => 'ru', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'financial_categories', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'product_categories', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'products', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'countries', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'report_cashier', 'actions' => 'r', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'report_cashier_in_out', 'actions' => 'r', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'report_earning', 'actions' => 'r', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'audit_log', 'actions' => 'r', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
        ]);

        $levels = Level::all();
        $actions = ['create', 'read', 'update', 'delete'];  // permissions
        $data = [];
        foreach ($levels as $level) {
            foreach ($actions as $action) {
                $firstChar = substr($action, 0, 1);
                $checked = strpos($level->actions, $firstChar);
                if ($checked === false) {
                    continue;
                } else {
                    $row = ['name' => $action . ' ' . $level->name, 'guard_name' => 'web', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
                    array_push($data, $row);
                }
            }
        }
        Permission::insert($data);
    }

}
