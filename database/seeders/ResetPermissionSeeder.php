<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use App\Models\Level;
use Carbon\Carbon;

class ResetPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $actions = ['create', 'read', 'update', 'delete'];  // permissions
        $permissions = [
            ['name' => 'product_categories', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'products', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'countries', 'actions' => 'crud', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
        ];

        foreach($permissions as $perm) {
            $level =   Level::create(['name' => $perm['name'], 'actions' => $perm['actions']]);
            $data = [];
            foreach ($actions as $action) {
                $firstChar = substr($action, 0, 1);
                $checked = strpos($level->actions, $firstChar);
                if ($checked === false) {
                    continue;
                } else {
                    $row = ['name' => $action . ' ' . $level->name, 'guard_name' => 'web'];
                    array_push($data, $row);
                }
            }

            Permission::insert($data);
        }
    }
}
