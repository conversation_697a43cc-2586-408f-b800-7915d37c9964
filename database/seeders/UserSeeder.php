<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class UserSeeder extends Seeder {

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run() {
        $user = User::create([
                    'name' => 'Super Admin',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('12345678'),
                    'user_type_id' => 1,
                    'phone' => '01017827785'
        ]);
        $user->assignRole('super_admin');
    }

}
