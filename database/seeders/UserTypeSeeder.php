<?php

namespace Database\Seeders;

use App\Models\UserTypes;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class UserTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [];
        $data[] = ['title_en' => 'Admin', 'title_ar' => 'الإدارة', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
        $data[] = ['title_en' => 'Client', 'title_ar' => 'عميل', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
        $data[] = ['title_en' => 'Service Provider', 'title_ar' => 'مقدم خدمة', 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
        UserTypes::insert($data);

    }
}
