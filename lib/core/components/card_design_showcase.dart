import 'package:flutter/material.dart';
import 'package:gather_point/core/components/enhanced_property_card.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/theme_helper.dart';

/// Showcase screen for the enhanced card design
class CardDesignShowcase extends StatelessWidget {
  const CardDesignShowcase({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Enhanced Card Design',
          style: AppTextStyles.font18Bold.copyWith(color: context.primaryTextColor),
        ),
        backgroundColor: context.cardColor,
        elevation: 0.5,
        foregroundColor: context.primaryTextColor,
        actions: [
          IconButton(
            icon: Icon(
              context.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: context.iconColor,
            ),
            onPressed: () {
              // Theme toggle would go here
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    context.isDarkMode
                        ? 'Switch to Light Mode'
                        : 'Switch to Dark Mode'
                  ),
                  backgroundColor: context.accentColor,
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: ThemeHelper.getCardDecoration(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        color: context.accentColor,
                        size: 28
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Enhanced UI Features',
                        style: AppTextStyles.font20Bold.copyWith(
                          color: context.primaryTextColor
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem('✨ Beautiful shadow effects', context),
                  _buildFeatureItem('🎨 Modern gradient overlays', context),
                  _buildFeatureItem('📱 Responsive design', context),
                  _buildFeatureItem('💫 Smooth animations', context),
                  _buildFeatureItem('🏷️ Enhanced typography', context),
                  _buildFeatureItem('🎯 Better visual hierarchy', context),
                  _buildFeatureItem('🌙 Dark/Light theme support', context),
                ],
              ),
            ),

            const SizedBox(height: 30),

            Text(
              'Card Examples',
              style: AppTextStyles.font18Bold.copyWith(color: AppColors.black),
            ),
            const SizedBox(height: 16),

            // Grid of enhanced cards
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 20,
                childAspectRatio: 0.85,
              ),
              itemCount: _sampleProperties.length,
              itemBuilder: (context, index) {
                final property = _sampleProperties[index];
                return EnhancedPropertyCard(
                  property: property,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Tapped: ${property["city"]}'),
                        backgroundColor: AppColors.brown,
                      ),
                    );
                  },
                  onFavoritePressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Added to favorites: ${property["city"]}'),
                        backgroundColor: Colors.red[400],
                      ),
                    );
                  },
                );
              },
            ),

            const SizedBox(height: 30),

            // Design specifications
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Design Specifications',
                    style: AppTextStyles.font18Bold.copyWith(color: AppColors.black),
                  ),
                  const SizedBox(height: 16),
                  _buildSpecItem('Border Radius', '20px'),
                  _buildSpecItem('Shadow Blur', '20px primary, 6px secondary'),
                  _buildSpecItem('Shadow Offset', '(0, 8) primary, (0, 2) secondary'),
                  _buildSpecItem('Shadow Opacity', '8% primary, 4% secondary'),
                  _buildSpecItem('Image Height', '160px'),
                  _buildSpecItem('Content Padding', '16px'),
                  _buildSpecItem('Card Aspect Ratio', '0.85'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: AppTextStyles.font14Regular.copyWith(
          color: context.secondaryTextColor
        ),
      ),
    );
  }

  Widget _buildSpecItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.font14Bold.copyWith(color: AppColors.black),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.font14Regular.copyWith(color: AppColors.darkGrey2),
            ),
          ),
        ],
      ),
    );
  }

  static final List<Map<String, dynamic>> _sampleProperties = [
    {
      "id": 1,
      "city": "Riyadh",
      "country": "Saudi Arabia",
      "price": "450",
      "image": "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400",
      "reviews": "4.8",
      "wifi": true,
      "no_guests": 4,
    },
    {
      "id": 2,
      "city": "Jeddah",
      "country": "Saudi Arabia", 
      "price": "380",
      "image": "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400",
      "reviews": "4.6",
      "wifi": true,
      "no_guests": 6,
    },
    {
      "id": 3,
      "city": "Dammam",
      "country": "Saudi Arabia",
      "price": "320",
      "image": "https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=400",
      "reviews": "4.7",
      "wifi": false,
      "no_guests": 2,
    },
    {
      "id": 4,
      "city": "Mecca",
      "country": "Saudi Arabia",
      "price": "500",
      "image": "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=400",
      "reviews": "4.9",
      "wifi": true,
      "no_guests": 8,
    },
  ];
}
