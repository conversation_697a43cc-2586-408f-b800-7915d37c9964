// ignore_for_file: unused_field

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../styles/app_colors.dart';
import '../styles/app_text_styles.dart';

class CustomTextFormField extends StatefulWidget {
  const CustomTextFormField({
    super.key,
    this.keyboardType,
    this.controller,
    this.labelText,
    this.suffix,
    this.prefix,
    this.validator,
    this.hintText,
    this.maxLines,
    this.minLines,
    this.inputFormatters,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.borderRadius,
    this.showValidatorIcon = false,
    this.mainTitle,
    this.initialValue,
    this.isPassword = false,
  });

  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final Widget? suffix;
  final Widget? prefix;
  final String? Function(String?)? validator;
  final int? maxLines;
  final int? minLines;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final BorderRadius? borderRadius;
  final bool showValidatorIcon;
  final String? mainTitle;
  final String? initialValue;
  final bool isPassword;

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  bool _obscureText = true; // State for password visibility

  void _togglePasswordVisibility() {
    setState(() => _obscureText = !_obscureText);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.mainTitle != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              widget.mainTitle!,
              style: AppTextStyles.font18Bold.copyWith(
                color: AppColors.darkGrey6,
              ),
            ),
          ),
        TextFormField(
          readOnly: widget.readOnly,
          onTap: widget.onTap,
          initialValue: widget.initialValue,
          onChanged: widget.onChanged,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          controller: widget.controller,
          keyboardType: widget.keyboardType,
          validator: widget.validator,
          cursorColor: AppColors.brown,
          style: AppTextStyles.font14Regular.copyWith(
            color: AppColors.black,
          ),
          textInputAction: TextInputAction.next,
          inputFormatters: widget.inputFormatters,
          obscureText: widget.isPassword ? _obscureText : false,
          decoration: InputDecoration(
            labelText: widget.labelText,
            labelStyle: AppTextStyles.font14Regular.copyWith(
              color: AppColors.darkGrey3,
            ),
            hintText: widget.hintText,
            hintStyle: AppTextStyles.font14Regular.copyWith(
              color: AppColors.lightGrey3,
            ),
            suffixIcon: widget.isPassword
                ? IconButton(
                    icon: Icon(
                      _obscureText ? Icons.visibility_off : Icons.visibility,
                      color: AppColors.darkGrey3,
                    ),
                    onPressed: _togglePasswordVisibility,
                  )
                : Padding(
                    padding: const EdgeInsetsDirectional.only(end: 16),
                    child: widget.suffix,
                  ),
            suffixIconConstraints: const BoxConstraints(
              minHeight: 20,
              minWidth: 20,
            ),
            prefixIcon: widget.prefix,
            isDense: true,
            border: OutlineInputBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.lightGrey4,
                width: 0.25,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.lightGrey4,
                width: 0.25,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.lightGrey4,
                width: 0.25,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 14,
            ),
          ),
        ),
      ],
    );
  }
}
