import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

TabBar genericTabBar({
  required List<Widget> tabs,
  bool isScrollable = false,
  void Function(int)? onTap,
  TabController? controller,
}) {
  return TabBar(
    controller: controller,
    onTap: onTap,
    dividerColor: AppColors.lightGrey2,
    labelColor: AppColors.lightBrown2,
    unselectedLabelColor: AppColors.darkGrey5,
    labelStyle: AppTextStyles.font16Bold.copyWith(
      color: AppColors.lightBrown2,
    ),
    unselectedLabelStyle: AppTextStyles.font16Regular.copyWith(
      color: AppColors.darkGrey5,
    ),
    indicatorColor: AppColors.lightBrown2,
    indicatorSize: TabBarIndicatorSize.tab,
    indicatorWeight: 2,
    isScrollable: isScrollable,
    tabAlignment: isScrollable ? TabAlignment.start : null,
    overlayColor: WidgetStateProperty.all(Colors.transparent),
    tabs: tabs,
  );
}
