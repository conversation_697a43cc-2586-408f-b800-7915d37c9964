import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

PreferredSizeWidget? primaryAppBar({
  required BuildContext context,
  required String title,
  void Function()? onBack,
  bool showBack = true,
  List<Widget>? actions,
}) {
  return AppBar(
    actions: actions,
    elevation: 1.5,
    scrolledUnderElevation: 1.5,
    surfaceTintColor: AppColors.white,
    centerTitle: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        bottom: Radius.circular(40),
      ),
    ),
    title: Text(
      title,
      style: AppTextStyles.font16Bold.copyWith(color: AppColors.darkBlue),
    ),
    backgroundColor: AppColors.white,
    leadingWidth: 60,
    shadowColor: AppColors.darkGrey2.withOpacity(0.4),
    leading: showBack
        ? GestureDetector(
            onTap: () {
              if (onBack != null) {
                onBack();
              } else if (GoRouter.of(context).canPop()) {
                GoRouter.of(context).pop();
              }
            },
            child: Container(
              color: Colors.transparent,
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.black,
                  size: 20,
                ),
              ),
            ),
          )
        : null,
  );
}
