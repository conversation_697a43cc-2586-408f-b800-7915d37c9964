import 'package:flutter/material.dart';
import '../styles/app_colors.dart';
import '../styles/app_text_styles.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    required this.onPressed,
    required this.label,
    this.fullWidth = true,
    this.backgroundColor,
    this.textStyle,
    this.padding,
    this.margin,
    this.borderRadius,
    this.iconPath,
    this.visualDensity,
  });

  final void Function()? onPressed;
  final String label;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? borderRadius;
  final TextStyle? textStyle;
  final bool fullWidth;
  final String? iconPath;
  final VisualDensity? visualDensity;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: fullWidth ? double.infinity : null,
      margin: margin,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          visualDensity: visualDensity,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 20),
          ),
          padding: padding ?? const EdgeInsets.all(8),
          backgroundColor: backgroundColor ?? AppColors.yellow,
        ),
        onPressed: onPressed,
        child: Text(
          label,
          style: textStyle ??
              AppTextStyles.font16Bold.copyWith(color: AppColors.black),
        ),
      ),
    );
  }
}
