import 'package:flutter/material.dart';
import '../styles/app_colors.dart';
import '../styles/app_text_styles.dart';

class SecondaryButton extends StatelessWidget {
  const SecondaryButton({
    super.key,
    required this.onPressed,
    required this.label,
    this.fullWidth = true,
    this.borderRadius,
    this.borderColor,
    this.textStyle,
    this.padding,
    this.iconPath,
    this.visualDensity,
    this.backgroundColor,
  });

  final void Function()? onPressed;
  final String label;
  final EdgeInsetsGeometry? padding;
  final Color? borderColor;
  final double? borderRadius;
  final TextStyle? textStyle;
  final bool fullWidth;
  final String? iconPath;
  final VisualDensity? visualDensity;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: fullWidth ? double.infinity : null,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          visualDensity: visualDensity,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 20),
          ),
          padding: padding ?? const EdgeInsets.all(8),
          side: BorderSide(color: borderColor ?? AppColors.yellow),
          backgroundColor: backgroundColor,
        ),
        onPressed: onPressed,
        child: Text(
          label,
          style: textStyle ??
              AppTextStyles.font16Bold.copyWith(color: AppColors.yellow),
        ),
      ),
    );
  }
}
