import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:gather_point/core/init/runtime_variables.dart';
import 'exceptions.dart';
import 'failure.dart';

Future<Either<Failure, dynamic>> apiErrorWrapper(
  Future<dynamic> Function(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
  }) apiCall,
  String path, {
  Object? data,
  Map<String, dynamic>? queryParameters,
}) async {
  try {
    var response = await apiCall.call(
      path,
      data: data,
      queryParameters: queryParameters,
    );
    var body = response['data'];

    return Right(body);
  } catch (e, s) {
    logger.e(e);
    logger.e(s);

    if (e is DioException) {
      logger.e(e.response?.data ?? 'server empty error message');
      logger.e(s);
      return left(ServerFailure.fromDioException(e));
    }
    logger.e(e);
    logger.e(s);
    return left(ServerFailure(errMessage: e.toString()));
  }
}

Future<Either<Failure, dynamic>> apiErrorWrapperFull(
  Future<dynamic> Function(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
  }) apiCall,
  String path, {
  Object? data,
  Map<String, dynamic>? queryParameters,
}) async {
  try {
    var response = await apiCall.call(
      path,
      data: data,
      queryParameters: queryParameters,
    );
    return Right(response);
  } catch (e, s) {
    logger.e(e);
    logger.e(s);

    if (e is DioException) {
      logger.e(e.response?.data ?? 'server empty error message');
      logger.e(s);
      var msg = e.response?.data['message'];
      if (msg is String) {
        return left(Failure(errMessage: msg));
      }
      return left(ServerFailure.fromDioException(e));
    }
    logger.e(e);
    logger.e(s);
    return left(ServerFailure(errMessage: e.toString()));
  }
}
