import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/models/option_model.dart';

class CheckboxField extends StatefulWidget {
  const CheckboxField({
    super.key,
    required this.title,
    required this.options,
    this.onChanged,
    this.fieldKey,
    this.isRequired,
  });

  final String title;
  final List<OptionModel> options;
  final void Function(List<String>)? onChanged;
  final GlobalKey<FormFieldState>? fieldKey; // Key for validation
  final bool? isRequired;

  @override
  State<CheckboxField> createState() => _CheckboxFieldState();
}

class _CheckboxFieldState extends State<CheckboxField> {
  final List<String> _selectedValues = [];

  @override
  Widget build(BuildContext context) {
    return FormField<List<String>>(
      key: widget.fieldKey,
      validator: (value) {
        if (widget.isRequired == true && _selectedValues.isEmpty) {
          return "Please choose at least one value";
        }
        return null;
      },
      builder: (state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: AppTextStyles.font14Regular.copyWith(
                color: AppColors.black,
              ),
            ),
            const SizedBox(height: 6),
            ...widget.options.map(
              (option) => CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
                visualDensity: VisualDensity.compact,
                activeColor: AppColors.yellow,
                dense: true,
                title: Text(option.title, style: AppTextStyles.font14Medium),
                value: _selectedValues.contains(option.title),
                onChanged: (bool? checked) {
                  setState(() {
                    if (checked == true) {
                      _selectedValues.add(option.title);
                    } else {
                      _selectedValues.remove(option.title);
                    }
                    state.didChange(_selectedValues); // Update state value
                    widget.onChanged?.call(_selectedValues);
                  });
                },
              ),
            ),
            if (state.errorText != null)
              Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: Text(
                  state.errorText!,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: AppColors.errorRed,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
