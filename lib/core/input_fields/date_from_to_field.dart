import 'package:flutter/material.dart';
import 'date_input_field.dart';

/// Widget for Date From and To Input
class DateFromTo<PERSON>ield extends StatelessWidget {
  final TextEditingController? fromController;
  final TextEditingController? toController;
  final String? labelFrom;
  final String? labelTo;
  final bool? isRequired;

  const DateFromToField({
    super.key,
    this.fromController,
    this.toController,
    this.labelFrom,
    this.labelTo,
    this.isRequired,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DateInput<PERSON>ield(
          controller: fromController,
          title: labelFrom,
          isRequired: isRequired,
        ),
        const <PERSON><PERSON><PERSON><PERSON>(height: 8),
        DateInput<PERSON>ield(
          controller: toController,
          title: labelTo,
          isRequired: isRequired,
        ),
      ],
    );
  }
}
