import 'package:flutter/material.dart';
import '../widgets/custom_text_form_field.dart';
import 'package:intl/intl.dart';

/// Widget for Date Input
class DateInputField extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final void Function(DateTime)? onDateSelected;
  final bool? isRequired;

  const DateInputField({
    super.key,
    this.controller,
    this.title,
    this.onDateSelected,
    this.isRequired,
  });

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      controller?.text = DateFormat('yyyy-MM-dd').format(picked);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      controller: controller,
      title: title,
      readOnly: true,
      onTap: () => _selectDate(context),
      isRequired: isRequired,
    );
  }
}
