import 'package:flutter/material.dart';
import 'date_time_input_field.dart';

/// Widget for DateTime From and To Input
class DateTimeFromToField extends StatelessWidget {
  final TextEditingController? fromController;
  final TextEditingController? toController;
  final String? labelFrom;
  final String? labelTo;
  final bool? isRequired;

  const DateTimeFromToField({
    super.key,
    this.fromController,
    this.toController,
    this.labelFrom,
    this.labelTo,
    this.isRequired,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DateTimeInputField(
          controller: fromController,
          title: labelFrom,
          isRequired: isRequired,
        ),
        const Si<PERSON><PERSON><PERSON>(height: 8),
        DateTimeInputField(
          controller: toController,
          title: labelTo,
          isRequired: isRequired,
        ),
      ],
    );
  }
}
