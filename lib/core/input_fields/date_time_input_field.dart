import 'package:flutter/material.dart';
import '../widgets/custom_text_form_field.dart';
import 'package:intl/intl.dart';

/// Widget for DateTime Input
class DateTimeInputField extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final void Function(DateTime)? onDateTimeSelected;
  final bool? isRequired;

  const DateTimeInputField({
    super.key,
    this.controller,
    this.title,
    this.onDateTimeSelected,
    this.isRequired,
  });

  Future<void> _selectDateTime(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (context.mounted && picked != null) {
      final TimeOfDay? timePicked = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );
      if (timePicked != null) {
        final DateTime dateTime = DateTime(
          picked.year,
          picked.month,
          picked.day,
          timePicked.hour,
          timePicked.minute,
        );
        onDateTimeSelected?.call(dateTime);
        controller?.text = DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      controller: controller,
      title: title,
      readOnly: true,
      onTap: () => _selectDateTime(context),
      isRequired: isRequired,
    );
  }
}
