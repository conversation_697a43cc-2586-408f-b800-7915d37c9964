import 'package:flutter/material.dart';
import 'package:gather_point/core/input_fields/checkbox_field.dart';
import 'package:gather_point/core/input_fields/radio_field.dart';
import 'package:gather_point/core/models/option_model.dart';
import 'date_from_to_field.dart';
import 'date_input_field.dart';
import 'date_time_from_to_field.dart';
import 'date_time_input_field.dart';
import 'numeric_decimal_input_field.dart';
import 'numeric_input_field.dart';
import 'password_numeric_input_field.dart';
import 'password_text_input_field.dart';
import 'phone_input_field.dart';
import 'text_area_field.dart';
import 'text_input_field.dart';
import 'time_input_field.dart';

// Wrapper widget to call the matching widget based on `typeId`
class InputTypeWrapper extends StatelessWidget {
  final int typeId;
  final String title;
  final bool isRequired;
  final TextEditingController? controller;
  final TextEditingController? toController;
  final List<OptionModel>? options;

  const InputTypeWrapper({
    super.key,
    required this.typeId,
    required this.title,
    required this.isRequired,
    this.controller,
    this.toController,
    this.options,
  });

  @override
  Widget build(BuildContext context) {
    // Map each typeId to its corresponding widget
    final GlobalKey<FormFieldState> formFieldKey = GlobalKey<FormFieldState>();

    final Map<int, Widget Function()> widgetMapping = {
      1: () => TextInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      2: () => NumericInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      3: () => NumericDecimalInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      4: () => PhoneInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      5: () => PasswordTextInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      6: () => PasswordNumericInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      7: () => CheckboxField(
            title: title,
            options: options ?? [],
            onChanged: (value) {},
            fieldKey: formFieldKey,
            isRequired: isRequired,
          ),
      8: () => RadioField(
            title: title,
            options: options ?? [],
            onChanged: (value) {},
            fieldKey: formFieldKey,
            isRequired: isRequired,
          ),
      9: () => TextAreaField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      10: () => DateInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      11: () => DateTimeInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      12: () => TimeInputField(
            title: title,
            controller: controller,
            isRequired: isRequired,
          ),
      13: () => DateFromToField(
            labelFrom: title,
            labelTo: title,
            fromController: controller,
            toController: toController,
            isRequired: isRequired,
          ),
      14: () => DateTimeFromToField(
            labelFrom: title,
            labelTo: title,
            fromController: controller,
            toController: toController,
            isRequired: isRequired,
          ),
    };

    // Return the corresponding widget or an error message for invalid typeId
    return widgetMapping[typeId]?.call() ??
        const Text("Invalid Type ID. Please check the configuration.");
  }
}
