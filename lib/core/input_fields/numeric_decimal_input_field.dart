import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/custom_text_form_field.dart';

/// Widget for Numeric Decimal Input
class NumericDecimalInputField extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final String? hintText;
  final bool? isRequired;

  const NumericDecimalInputField({
    super.key,
    this.controller,
    this.title,
    this.hintText,
    this.isRequired,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      controller: controller,
      title: title,
      hintText: hintText,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      isRequired: isRequired,
    );
  }
}
