import 'package:flutter/material.dart';
import '../widgets/custom_text_form_field.dart';

/// Widget for Password Text Input
class PasswordTextInputField extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final String? hintText;
  final bool? isRequired;

  const PasswordTextInputField({
    super.key,
    this.controller,
    this.title,
    this.hintText,
    this.isRequired,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      isPassword: true,
      keyboardType: TextInputType.text,
      controller: controller,
      title: title,
      hintText: hintText,
      isRequired: isRequired,
    );
  }
}
