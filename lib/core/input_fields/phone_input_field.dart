import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/custom_text_form_field.dart';

/// Widget for Phone Input
class PhoneInput<PERSON>ield extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final String? hintText;
  final bool? isRequired;

  const PhoneInputField({
    super.key,
    this.controller,
    this.title,
    this.hintText,
    this.isRequired,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      keyboardType: TextInputType.phone,
      controller: controller,
      title: title,
      hintText: hintText,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      isRequired: isRequired,
    );
  }
}
