import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/models/option_model.dart';

class RadioField extends StatefulWidget {
  const RadioField({
    super.key,
    required this.title,
    required this.options,
    this.onChanged,
    this.fieldKey,
    this.isRequired,
  });

  final String title;
  final List<OptionModel> options;
  final void Function(String)? onChanged;
  final GlobalKey<FormFieldState>? fieldKey; // Key for validation
  final bool? isRequired;

  @override
  State<RadioField> createState() => _RadioFieldState();
}

class _RadioFieldState extends State<RadioField> {
  String? _selectedValue;

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      key: widget.fieldKey,
      validator: (value) {
        if (widget.isRequired == true &&
            (_selectedValue == null || _selectedValue!.isEmpty)) {
          return "Please choose a value";
        }
        return null;
      },
      builder: (state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: AppTextStyles.font14Regular.copyWith(
                color: AppColors.black,
              ),
            ),
            const SizedBox(height: 6),
            ...widget.options.map(
              (option) => RadioListTile<String>(
                contentPadding: EdgeInsets.zero,
                visualDensity: VisualDensity.compact,
                activeColor: AppColors.yellow,
                dense: true,
                title: Text(option.title, style: AppTextStyles.font14Medium),
                value: option.title,
                groupValue: _selectedValue,
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _selectedValue = value);
                    state.didChange(value); // Update state value
                    widget.onChanged?.call(value);
                  }
                },
              ),
            ),
            if (state.errorText != null)
              Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: Text(
                  state.errorText!,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: AppColors.errorRed,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
