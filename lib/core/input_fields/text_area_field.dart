import 'package:flutter/material.dart';
import '../widgets/custom_text_form_field.dart';

/// Widget for Text Area
class TextAreaField extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final String? hintText;
  final bool? isRequired;

  const TextAreaField({
    super.key,
    this.controller,
    this.title,
    this.hintText,
    this.isRequired,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      keyboardType: TextInputType.multiline,
      controller: controller,
      title: title,
      hintText: hintText,
      maxLines: 5, // Set to a high number for a text area
      isRequired: isRequired,
    );
  }
}
