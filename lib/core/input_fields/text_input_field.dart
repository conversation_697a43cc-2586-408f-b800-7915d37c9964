import 'package:flutter/material.dart';
import '../widgets/custom_text_form_field.dart';

/// Widget for Text Input
class TextInput<PERSON>ield extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final String? hintText;
  final bool? isRequired;

  const TextInputField({
    super.key,
    this.controller,
    this.title,
    this.hintText,
    this.isRequired,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      keyboardType: TextInputType.text,
      controller: controller,
      title: title,
      hintText: hintText,
      isRequired: isRequired,
    );
  }
}
