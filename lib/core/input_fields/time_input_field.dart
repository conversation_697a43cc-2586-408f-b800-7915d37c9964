import 'package:flutter/material.dart';
import '../widgets/custom_text_form_field.dart';

/// Widget for Time Input
class TimeInputField extends StatelessWidget {
  final TextEditingController? controller;
  final String? title;
  final void Function(TimeOfDay)? onTimeSelected;
  final bool? isRequired;

  const TimeInputField({
    super.key,
    this.controller,
    this.title,
    this.onTimeSelected,
    this.isRequired,
  });

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (context.mounted && picked != null) {
      onTimeSelected?.call(picked);
      controller?.text = picked.format(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      controller: controller,
      title: title,
      readOnly: true,
      onTap: () => _selectTime(context),
      isRequired: isRequired,
    );
  }
}
