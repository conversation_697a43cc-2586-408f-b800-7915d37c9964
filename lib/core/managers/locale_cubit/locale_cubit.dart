import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart' show Locale;
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:meta/meta.dart';
import '../../utils/app_constants.dart';

part 'locale_state.dart';

class LocaleCubit extends Cubit<LocaleState> {
  // Passing an initial value (state) with a default 'Locale' to the super class.
  LocaleCubit()
      : super(
          SelectedLocale(
            Locale(
              Hive.box<String>(AppConstants.kLanguageBoxName)
                      .get(AppConstants.kLanguageKey) ??
                  AppConstants.kArabicLanguageCode,
            ),
          ),
        );

  // Once we call this method, the BlocBuilder<LocaleCubit>
  // in the 'main.dart' will rebuild the entire app with
  // the new emitted state that holds the 'ar' locale.
  void toArabic() {
    Hive.box<String>(AppConstants.kLanguageBoxName)
        .put(AppConstants.kLanguageKey, AppConstants.kArabicLanguageCode);
    emit(const SelectedLocale(Locale(AppConstants.kArabicLanguageCode)));
  }

  // Same as the previous method, but with the 'en' locale.
  void toEnglish() {
    Hive.box<String>(AppConstants.kLanguageBoxName)
        .put(AppConstants.kLanguageKey, AppConstants.kEnglishLanguageCode);
    emit(const SelectedLocale(Locale(AppConstants.kEnglishLanguageCode)));
  }

  // method to check is current language is Arabic
  bool isArabic() {
    return Hive.box<String>(AppConstants.kLanguageBoxName).get(
          AppConstants.kLanguageKey,
          defaultValue: AppConstants.kArabicLanguageCode,
        ) ==
        AppConstants.kArabicLanguageCode;
  }
}
