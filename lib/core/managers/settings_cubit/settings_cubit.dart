import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:hive/hive.dart';
import 'package:meta/meta.dart';

part 'settings_state.dart';

class SettingsCubit extends Cubit<SettingsState> {
  final Box<bool> _settingsBox;

  SettingsCubit(this._settingsBox)
      : super(SettingsState(
          soundClick: _settingsBox.get(AppConstants.kSoundClickKey,
                  defaultValue: true) ??
              true,
          soundScroll: _settingsBox.get(AppConstants.kSoundScrollKey,
                  defaultValue: true) ??
              true,
        ));

  void toggleSoundClick(bool value) {
    _settingsBox.put(AppConstants.kSoundClickKey, value);
    emit(state.copyWith(soundClick: value));
  }

  void toggleSoundScroll(bool value) {
    _settingsBox.put(AppConstants.kSoundScrollKey, value);
    emit(state.copyWith(soundScroll: value));
  }
}
