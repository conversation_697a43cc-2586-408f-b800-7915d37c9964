part of 'settings_cubit.dart';

@immutable
class SettingsState {
  final bool soundClick;
  final bool soundScroll;

  const SettingsState({
    required this.soundClick,
    required this.soundScroll,
  });

  SettingsState copyWith({
    bool? soundClick,
    bool? soundScroll,
  }) {
    return SettingsState(
      soundClick: soundClick ?? this.soundClick,
      soundScroll: soundScroll ?? this.soundScroll,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SettingsState &&
        other.soundClick == soundClick &&
        other.soundScroll == soundScroll;
  }

  @override
  int get hashCode => soundClick.hashCode ^ soundScroll.hashCode;
}
