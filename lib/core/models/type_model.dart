import 'package:gather_point/core/models/option_model.dart';

class TypeModel {
  final String title;
  final int typeId;
  final String icon;
  final bool required;
  final List<OptionModel>? options;

  TypeModel({
    required this.title,
    required this.typeId,
    required this.icon,
    required this.required,
    this.options,
  });

  factory TypeModel.fromJson(Map<String, dynamic> json) {
    return TypeModel(
      title: json['title'],
      typeId: json['id'],
      icon: json['icon'],
      required: json['required'],
      options: json['options'] != null
          ? List<OptionModel>.from(
              json['options'].map((x) => OptionModel.fromJson(x)),
            )
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'id': typeId,
      'icon': icon,
      'required': required,
      'options': options?.map((x) => x.toJson()).toList(),
    };
  }

  TypeModel copyWith({
    String? title,
    int? id,
    String? icon,
    bool? required,
    List<OptionModel>? options,
  }) {
    return TypeModel(
      title: title ?? this.title,
      typeId: id ?? typeId,
      icon: icon ?? this.icon,
      required: required ?? this.required,
      options: options ?? this.options,
    );
  }
}
