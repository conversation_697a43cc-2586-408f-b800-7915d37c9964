import 'dart:convert';
import 'dart:developer';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/services/navigation_service.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/core/widgets/notification_widgets.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FCMService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  
  static const String _fcmTokenKey = 'fcm_token';
  static const String _notificationChannelId = 'gather_point_notifications';
  static const String _notificationChannelName = 'Gather Point Notifications';
  static const String _notificationChannelDescription = 'Notifications for Gather Point app';

  /// Initialize FCM service
  static Future<void> initialize() async {
    try {
      // Request permissions
      await _requestPermissions();
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Get and store FCM token
      await _handleTokenRefresh();
      
      // Set up message handlers
      _setupMessageHandlers();
      
      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen(_onTokenRefresh);
      
      log('FCM Service initialized successfully');
    } catch (e) {
      log('Error initializing FCM Service: $e');
    }
  }

  /// Request notification permissions
  static Future<void> _requestPermissions() async {
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
      announcement: false,
      carPlay: false,
      criticalAlert: false,
    );
    
    log('Notification permission status: ${settings.authorizationStatus}');
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
    
    // Create notification channel for Android
    await _createNotificationChannel();
  }

  /// Create notification channel for Android
  static Future<void> _createNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      _notificationChannelId,
      _notificationChannelName,
      description: _notificationChannelDescription,
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
    );
    
    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  /// Handle token refresh and API update
  static Future<void> _handleTokenRefresh() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _storeTokenLocally(token);
        await _sendTokenToServer(token);
        log('FCM Token: $token');
      }
    } catch (e) {
      log('Error handling token refresh: $e');
    }
  }

  /// Store FCM token locally
  static Future<void> _storeTokenLocally(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_fcmTokenKey, token);
  }

  /// Get stored FCM token
  static Future<String?> getStoredToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_fcmTokenKey);
  }

  /// Send FCM token to server
  static Future<void> _sendTokenToServer(String token) async {
    try {
      final userBox = getIt<Box<UserEntity>>();
      final user = userBox.get(AppConstants.kMyProfileKey);
      
      if (user != null && user.token.isNotEmpty) {
        final dioConsumer = getIt<DioConsumer>();
        
        await dioConsumer.post(
          'auth/edit_profile',
          data: {'fb_token': token},
        );
        
        log('FCM token sent to server successfully');
      }
    } catch (e) {
      log('Error sending FCM token to server: $e');
    }
  }

  /// Handle token refresh
  static Future<void> _onTokenRefresh(String token) async {
    await _storeTokenLocally(token);
    await _sendTokenToServer(token);
    log('FCM Token refreshed: $token');
  }

  /// Set up message handlers
  static void _setupMessageHandlers() {
    // Foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Background messages (when app is in background but not terminated)
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);
    
    // Handle initial message (when app is opened from terminated state)
    _handleInitialMessage();
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    log('Received foreground message: ${message.messageId}');
    log('Message data: ${message.data}');
    
    if (message.notification != null) {
      await _showLocalNotification(message);
      _showInAppNotification(message);
    }
  }

  /// Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    log('App opened from background notification: ${message.messageId}');
    _handleNotificationNavigation(message);
  }

  /// Handle initial message
  static Future<void> _handleInitialMessage() async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      log('App opened from terminated state by notification: ${initialMessage.messageId}');
      _handleNotificationNavigation(initialMessage);
    }
  }

  /// Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    if (notification == null) return;
    
    const androidDetails = AndroidNotificationDetails(
      _notificationChannelId,
      _notificationChannelName,
      channelDescription: _notificationChannelDescription,
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      playSound: true,
      enableVibration: true,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _localNotifications.show(
      message.hashCode,
      notification.title,
      notification.body,
      details,
      payload: jsonEncode(message.data),
    );
  }

  /// Show in-app notification
  static void _showInAppNotification(RemoteMessage message) {
    if (message.notification != null) {
      // For now, just log the notification - overlay will be shown later when context is ready
      log('Foreground notification received: ${message.notification?.title} - ${message.notification?.body}');

      // Try to show overlay after a delay, but don't fail if it doesn't work
      Future.delayed(const Duration(seconds: 1), () {
        try {
          final context = _getCurrentContext();
          if (context != null && context.mounted) {
            NotificationOverlay.show(
              context,
              message.notification!,
              onTap: () => _handleNotificationNavigation(message),
            );
          }
        } catch (e) {
          // Silently fail - the local notification will still show
          log('Could not show in-app notification overlay: $e');
        }
      });
    }
  }

  /// Get current context from navigator
  static BuildContext? _getCurrentContext() {
    try {
      return NavigationService.currentContext;
    } catch (e) {
      log('Error getting current context: $e');
      return null;
    }
  }

  /// Handle notification navigation
  static void _handleNotificationNavigation(RemoteMessage message) {
    final data = message.data;
    log('Handling notification navigation with data: $data');

    // For now, always navigate to home tab since it's the safest option
    // You can enhance this later when you have specific routes for different content
    _navigateToHome();

    // Log the notification type for debugging
    if (data.containsKey('type')) {
      final type = data['type'];
      log('Notification type: $type');

      // Log additional data for future implementation
      switch (type) {
        case 'event':
          log('Event ID: ${data['event_id']}');
          break;
        case 'message':
          log('Chat ID: ${data['chat_id']}');
          break;
        case 'profile':
          log('User ID: ${data['user_id']}');
          break;
      }
    }
  }



  /// Navigate to home
  static void _navigateToHome() {
    Future.delayed(const Duration(milliseconds: 1000), () {
      NavigationService.navigateToHome();
      log('Navigating to home');
    });
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!) as Map<String, dynamic>;
        _handleNotificationNavigation(RemoteMessage(data: data));
      } catch (e) {
        log('Error parsing notification payload: $e');
      }
    }
  }

  /// Get current FCM token
  static Future<String?> getCurrentToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      log('Error getting FCM token: $e');
      return null;
    }
  }

  /// Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      log('Subscribed to topic: $topic');
    } catch (e) {
      log('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      log('Unsubscribed from topic: $topic');
    } catch (e) {
      log('Error unsubscribing from topic $topic: $e');
    }
  }

  /// Update FCM token for logged in user
  static Future<void> updateTokenForUser() async {
    try {
      final token = await getCurrentToken();
      if (token != null) {
        await _sendTokenToServer(token);
      }
    } catch (e) {
      log('Error updating FCM token for user: $e');
    }
  }

  /// Clear FCM token on logout
  static Future<void> clearTokenOnLogout() async {
    try {
      // Send empty token to server to clear it
      await _sendTokenToServer('');

      // Clear local token
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_fcmTokenKey);

      log('FCM token cleared on logout');
    } catch (e) {
      log('Error clearing FCM token on logout: $e');
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  log('Handling background message: ${message.messageId}');
  log('Message data: ${message.data}');
  
  // You can perform background tasks here
  // Note: Don't call Flutter UI methods here
}
