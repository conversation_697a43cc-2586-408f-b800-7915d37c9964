import 'dart:developer';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/services/fcm_service.dart';
import 'package:gather_point/core/services/navigation_service.dart';
import 'package:gather_point/core/widgets/notification_widgets.dart';

/// Helper class for testing FCM functionality
class FCMTestHelper {
  
  /// Test FCM token generation
  static Future<void> testTokenGeneration() async {
    try {
      final token = await FCMService.getCurrentToken();
      if (token != null) {
        log('✅ FCM Token generated successfully: ${token.substring(0, 20)}...');
      } else {
        log('❌ FCM Token generation failed');
      }
    } catch (e) {
      log('❌ Error testing FCM token: $e');
    }
  }
  
  /// Test notification permissions
  static Future<void> testNotificationPermissions() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.getNotificationSettings();
      
      switch (settings.authorizationStatus) {
        case AuthorizationStatus.authorized:
          log('✅ Notification permissions: Authorized');
          break;
        case AuthorizationStatus.denied:
          log('❌ Notification permissions: Denied');
          break;
        case AuthorizationStatus.notDetermined:
          log('⚠️ Notification permissions: Not determined');
          break;
        case AuthorizationStatus.provisional:
          log('⚠️ Notification permissions: Provisional');
          break;
      }
    } catch (e) {
      log('❌ Error testing notification permissions: $e');
    }
  }
  
  /// Test navigation service
  static Future<void> testNavigation() async {
    try {
      final context = NavigationService.currentContext;
      if (context != null) {
        log('✅ Navigation context available');
        
        // Test navigation to home
        await NavigationService.navigateToHome();
        log('✅ Navigation to home successful');
      } else {
        log('❌ Navigation context not available');
      }
    } catch (e) {
      log('❌ Error testing navigation: $e');
    }
  }
  
  /// Test in-app notification display
  static Future<void> testInAppNotification(BuildContext context) async {
    try {
      const testNotification = RemoteNotification(
        title: 'اختبار الإشعارات',
        body: 'هذا إشعار تجريبي للتأكد من عمل النظام',
      );
      
      NotificationOverlay.show(
        context,
        testNotification,
        duration: const Duration(seconds: 3),
      );
      
      log('✅ In-app notification test successful');
    } catch (e) {
      log('❌ Error testing in-app notification: $e');
    }
  }
  
  /// Test FCM service initialization
  static Future<void> testFCMInitialization() async {
    try {
      await FCMService.initialize();
      log('✅ FCM Service initialization successful');
    } catch (e) {
      log('❌ Error testing FCM initialization: $e');
    }
  }
  
  /// Run all FCM tests
  static Future<void> runAllTests({BuildContext? context}) async {
    log('🧪 Starting FCM Tests...');
    
    await testFCMInitialization();
    await testTokenGeneration();
    await testNotificationPermissions();
    await testNavigation();
    
    if (context != null) {
      await testInAppNotification(context);
    }
    
    log('🧪 FCM Tests completed');
  }
  
  /// Create a test notification for debugging
  static RemoteMessage createTestNotification({
    String? title,
    String? body,
    Map<String, dynamic>? data,
  }) {
    return RemoteMessage(
      notification: RemoteNotification(
        title: title ?? 'إشعار تجريبي',
        body: body ?? 'هذا إشعار تجريبي من تطبيق Gather Point',
      ),
      data: data ?? {
        'type': 'test',
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      },
    );
  }
  
  /// Simulate receiving a notification
  static Future<void> simulateNotification({
    String? title,
    String? body,
    Map<String, dynamic>? data,
  }) async {
    try {
      final testMessage = createTestNotification(
        title: title,
        body: body,
        data: data,
      );
      
      log('📱 Simulating notification: ${testMessage.notification?.title}');
      
      // Simulate foreground notification handling
      if (testMessage.notification != null) {
        final context = NavigationService.currentContext;
        if (context != null) {
          NotificationOverlay.show(
            context,
            testMessage.notification!,
            duration: const Duration(seconds: 4),
          );
        }
      }
      
      log('✅ Notification simulation successful');
    } catch (e) {
      log('❌ Error simulating notification: $e');
    }
  }
  
  /// Test notification with different types
  static Future<void> testNotificationTypes(BuildContext context) async {
    log('🧪 Testing different notification types...');
    
    // Test event notification
    await Future.delayed(const Duration(seconds: 1));
    await simulateNotification(
      title: 'حدث جديد',
      body: 'تم إضافة حدث جديد في منطقتك',
      data: {'type': 'event', 'event_id': '123'},
    );
    
    // Test message notification
    await Future.delayed(const Duration(seconds: 3));
    await simulateNotification(
      title: 'رسالة جديدة',
      body: 'لديك رسالة جديدة من أحمد',
      data: {'type': 'message', 'chat_id': '456'},
    );
    
    // Test profile notification
    await Future.delayed(const Duration(seconds: 3));
    await simulateNotification(
      title: 'زيارة ملف شخصي',
      body: 'قام شخص بزيارة ملفك الشخصي',
      data: {'type': 'profile', 'user_id': '789'},
    );
    
    log('✅ Notification types test completed');
  }
}

/// Widget for testing FCM functionality
class FCMTestWidget extends StatelessWidget {
  const FCMTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: () => FCMTestHelper.runAllTests(context: context),
              child: const Text('Run All FCM Tests'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => FCMTestHelper.testInAppNotification(context),
              child: const Text('Test In-App Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => FCMTestHelper.testNotificationTypes(context),
              child: const Text('Test Notification Types'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => FCMTestHelper.testNavigation(),
              child: const Text('Test Navigation'),
            ),
          ],
        ),
      ),
    );
  }
}
