import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';

class LocationService {
  late final DioConsumer _dioConsumer;

  static const _cityIdKey = 'selectedCityId';
  static const _latKey = 'selectedLat';
  static const _lonKey = 'selectedLon';

  Future<bool> checkLocationPermissions() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) return false;

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) return false;
    }

    if (permission == LocationPermission.deniedForever) return false;

    return true;
  }

  Future<Map<String, dynamic>> getCitiesAndClosest(Position position) async {
    final hasPermission = await checkLocationPermissions();
    if (!hasPermission) {
      throw Exception('Location permission denied');
    }
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    final prefs = await SharedPreferences.getInstance();

    final response = await _dioConsumer.post(
      'https://backend.gatherpoint.sa/api/general/cities',
      data: {'lat': position.latitude, 'lng': position.longitude},
    );

    if (response['data'] != null) {
      final data = response['data']['cities'] as List;
      final cities = data.map((e) => City.fromJson(e)).toList();

      City? closestCity;
      if (cities.isNotEmpty) {
        cities.sort((a, b) => a.distance!.compareTo(b.distance!));
        closestCity = cities.first;

        // Save in local storage
        await prefs.setInt(_cityIdKey, closestCity.id);
        await prefs.setDouble(_latKey, position.latitude);
        await prefs.setDouble(_lonKey, position.longitude);
      }

      return {
        'allCities': cities,
        'closestCity': closestCity,
      };
    } else {
      throw Exception('فشل تحميل المدن');
    }
  }
}
