import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/routing/app_router.dart';

/// Navigation service for handling app navigation
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Get current context from GoRouter
  static BuildContext? get currentContext {
    try {
      // Try to get context from GoRouter first
      final routerDelegate = AppRouter.router.routerDelegate;
      final navigatorKey = routerDelegate.navigatorKey;
      return navigatorKey.currentContext;
    } catch (e) {
      // Fallback to our navigator key
      return NavigationService.navigatorKey.currentContext;
    }
  }

  /// Navigate to a specific route
  static Future<void> navigateTo(String route, {Object? arguments}) async {
    final context = currentContext;
    if (context != null && context.mounted) {
      try {
        // Check if GoRouter is available in context
        final router = GoRouter.maybeOf(context);
        if (router != null) {
          context.push(route, extra: arguments);
        } else {
          // Fallback to Navigator if Go<PERSON><PERSON><PERSON> is not available
          Navigator.of(context).pushNamed(route, arguments: arguments);
        }
      } catch (e) {
        debugPrint('Navigation error: $e');
      }
    }
  }

  /// Navigate and replace current route
  static Future<void> navigateAndReplace(String route, {Object? arguments}) async {
    final context = currentContext;
    if (context != null && context.mounted) {
      try {
        final router = GoRouter.maybeOf(context);
        if (router != null) {
          context.pushReplacement(route, extra: arguments);
        } else {
          Navigator.of(context).pushReplacementNamed(route, arguments: arguments);
        }
      } catch (e) {
        debugPrint('Navigation error: $e');
      }
    }
  }
  
  /// Navigate to home tab
  static Future<void> navigateToHome() async {
    await navigateTo(RoutesKeys.kHomeViewTab);
  }

  /// Navigate to event details (for now, go to home tab)
  static Future<void> navigateToEvent(String eventId) async {
    // Since there's no specific event details route, navigate to home tab
    // You can implement specific event navigation later
    await navigateToHome();
    debugPrint('Event navigation requested for ID: $eventId');
  }

  /// Navigate to chat (for now, go to home tab)
  static Future<void> navigateToChat(String chatId) async {
    // Since there's no specific chat route, navigate to home tab
    // You can implement specific chat navigation later
    await navigateToHome();
    debugPrint('Chat navigation requested for ID: $chatId');
  }

  /// Navigate to profile tab
  static Future<void> navigateToProfile(String userId) async {
    await navigateTo(RoutesKeys.kProfileViewTab);
    debugPrint('Profile navigation requested for user ID: $userId');
  }

  /// Navigate to search tab (closest to notifications)
  static Future<void> navigateToNotifications() async {
    await navigateTo(RoutesKeys.kSearchViewTab);
    debugPrint('Notifications navigation requested');
  }
  
  /// Pop current route
  static void pop([Object? result]) {
    final context = currentContext;
    if (context != null && context.mounted) {
      try {
        final router = GoRouter.maybeOf(context);
        if (router != null && context.canPop()) {
          context.pop(result);
        } else if (Navigator.canPop(context)) {
          Navigator.of(context).pop(result);
        }
      } catch (e) {
        debugPrint('Pop error: $e');
      }
    }
  }
  
  /// Show dialog
  static Future<T?> showCustomDialog<T>(Widget dialog) async {
    final context = currentContext;
    if (context != null) {
      return await showDialog<T>(
        context: context,
        builder: (_) => dialog,
      );
    }
    return null;
  }
  
  /// Show bottom sheet
  static Future<T?> showBottomSheet<T>(Widget bottomSheet) async {
    final context = currentContext;
    if (context != null) {
      return await showModalBottomSheet<T>(
        context: context,
        builder: (_) => bottomSheet,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
      );
    }
    return null;
  }
  
  /// Show snackbar
  static void showSnackBar(String message, {bool isError = false}) {
    final context = currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    }
  }
}
