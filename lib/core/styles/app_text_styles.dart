import 'package:flutter/material.dart';

abstract class AppTextStyles {
  ////*******  Typography with Hacen Tunisia Font - Perfect Arabic & English Support  *******////

  // Base font family
  static const String _fontFamily = 'HacenTunisia';

  // Headings - Using Hacen Tunisia for perfect Arabic-English support
  static const TextStyle font36ExtraBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 36,
    fontWeight: FontWeight.w800,
    letterSpacing: -0.3,
    height: 1.2,
    inherit: true,
  );

  static const TextStyle font30SemiBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 30,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: -0.2,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font28Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.2,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font24Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.1,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font24SemiBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: -0.1,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font24Medium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w400, // Using regular since w500 not available
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font24Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  // Body text - Using Hacen Tunisia for Arabic-English consistency
  static const TextStyle font20Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font20SemiBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font20Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.5,
    inherit: true,
  );

  static const TextStyle font18Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font18Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.5,
    inherit: true,
  );

  static const TextStyle font16Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font16SemiBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font16Medium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400, // Using regular since w500 not available
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font16Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.5,
    inherit: true,
  );

  // Small text - Using Hacen Tunisia for Arabic-English consistency
  static const TextStyle font14ExtraBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w700, // Using bold since w800 not available
    letterSpacing: 0,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font14Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font14SemiBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: 0,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font14Medium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400, // Using regular since w500 not available
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font14Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font13Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 13,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font12Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w700,
    letterSpacing: 0.1,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font12SemiBold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: 0.1,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font12Medium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400, // Using regular since w500 not available
    letterSpacing: 0.1,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font12Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.1,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle font10Bold = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 10,
    fontWeight: FontWeight.w700,
    letterSpacing: 0.2,
    height: 1.2,
    inherit: true,
  );

  static const TextStyle font10Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 10,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.2,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle font8Regular = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 8,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.3,
    height: 1.2,
    inherit: true,
  );

  ////*******  Arabic-Optimized Typography with Hacen Tunisia  *******////

  // Arabic headings with optimized spacing for Arabic text
  static const TextStyle arabicHeadingLarge = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle arabicHeadingMedium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 22,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: 0,
    height: 1.4,
    inherit: true,
  );

  static const TextStyle arabicBodyText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.6,
    inherit: true,
  );

  static const TextStyle arabicCaption = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.5,
    inherit: true,
  );

  ////*******  English-Optimized Typography with Hacen Tunisia  *******////

  // English text with optimized letter spacing for Latin characters
  static const TextStyle englishHeadingLarge = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.2,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle englishHeadingMedium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 22,
    fontWeight: FontWeight.w700, // Using bold since w600 not available
    letterSpacing: -0.1,
    height: 1.3,
    inherit: true,
  );

  static const TextStyle englishBodyText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.5,
    inherit: true,
  );

  static const TextStyle englishCaption = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.1,
    height: 1.4,
    inherit: true,
  );
}
