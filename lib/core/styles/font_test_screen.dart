import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/typography_helper.dart';

/// Test screen to verify Hacen Tunisia font is working correctly
class FontTestScreen extends StatelessWidget {
  const FontTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hacen Tunisia Font Test'),
        backgroundColor: AppColors.black,
      ),
      backgroundColor: AppColors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.lightGrey,
                borderRadius: BorderRadius.circular(8),
              ),
              child: <PERSON>umn(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Font Status: ✅ Hacen Tunisia Active',
                    style: AppTextStyles.font18Bold.copyWith(color: Colors.green),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Available weights: Regular (400) & Bold (700)',
                    style: AppTextStyles.font14Regular.copyWith(color: AppColors.darkGrey2),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Arabic Text Tests
            _buildSection('Arabic Text (العربية)', [
              Text(
                'مرحباً بكم في نقطة التجمع',
                style: AppTextStyles.font24Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'هذا نص تجريبي باللغة العربية لاختبار خط حاسن تونس',
                style: AppTextStyles.font16Regular.copyWith(color: AppColors.darkGrey2),
              ),
              const SizedBox(height: 8),
              Text(
                'نص صغير للتسميات والتوضيحات',
                style: AppTextStyles.font12Regular.copyWith(color: AppColors.darkGrey3),
              ),
            ]),

            const SizedBox(height: 30),

            // English Text Tests
            _buildSection('English Text', [
              Text(
                'Welcome to Gather Point',
                style: AppTextStyles.font24Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'This is a test text in English to verify the Hacen Tunisia font rendering.',
                style: AppTextStyles.font16Regular.copyWith(color: AppColors.darkGrey2),
              ),
              const SizedBox(height: 8),
              Text(
                'Small caption text for labels and descriptions',
                style: AppTextStyles.font12Regular.copyWith(color: AppColors.darkGrey3),
              ),
            ]),

            const SizedBox(height: 30),

            // Smart Typography Tests
            _buildSection('Smart Typography', [
              TypographyHelper.smartText(
                'Smart Arabic: نص ذكي عربي',
                isLargeHeading: true,
                color: AppColors.brown,
              ),
              const SizedBox(height: 8),
              TypographyHelper.smartText(
                'Smart English: Intelligent Typography',
                isLargeHeading: true,
                color: AppColors.brown,
              ),
              const SizedBox(height: 16),
              TypographyHelper.smartText(
                'Mixed text: Hello مرحبا World عالم!',
                color: AppColors.darkGrey2,
              ),
            ]),

            const SizedBox(height: 30),

            // Font Weight Tests
            _buildSection('Font Weight Tests', [
              Text(
                'Regular Weight (400)',
                style: AppTextStyles.font18Regular.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'Bold Weight (700)',
                style: AppTextStyles.font18Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'وزن عادي (400)',
                style: AppTextStyles.font18Regular.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'وزن عريض (700)',
                style: AppTextStyles.font18Bold.copyWith(color: AppColors.black),
              ),
            ]),

            const SizedBox(height: 30),

            // Size Tests
            _buildSection('Font Size Tests', [
              Text(
                'Size 24: كبير / Large',
                style: AppTextStyles.font24Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'Size 18: متوسط / Medium',
                style: AppTextStyles.font18Regular.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'Size 16: عادي / Normal',
                style: AppTextStyles.font16Regular.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'Size 14: صغير / Small',
                style: AppTextStyles.font14Regular.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'Size 12: تسمية / Caption',
                style: AppTextStyles.font12Regular.copyWith(color: AppColors.black),
              ),
            ]),

            const SizedBox(height: 30),

            // Success Message
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Column(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 48),
                  const SizedBox(height: 8),
                  Text(
                    'Hacen Tunisia Font Successfully Loaded!',
                    style: AppTextStyles.font16Bold.copyWith(color: Colors.green),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'خط حاسن تونس يعمل بشكل مثالي!',
                    style: AppTextStyles.font14Regular.copyWith(color: Colors.green),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.font20Bold.copyWith(color: AppColors.brown),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.lightGrey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.lightGrey3),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      ],
    );
  }
}
