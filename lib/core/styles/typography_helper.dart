import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

/// Typography helper for automatic Arabic-English font selection
class TypographyHelper {
  
  /// Detects if text contains Arabic characters
  static bool isArabicText(String text) {
    if (text.isEmpty) return false;
    
    // Arabic Unicode range: U+0600 to U+06FF
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(text);
  }

  /// Returns optimized text style based on text language
  static TextStyle getHeadingLarge(String text, {Color? color}) {
    final baseStyle = isArabicText(text) 
        ? AppTextStyles.arabicHeadingLarge 
        : AppTextStyles.englishHeadingLarge;
    
    return color != null ? baseStyle.copyWith(color: color) : baseStyle;
  }

  /// Returns optimized text style for medium headings
  static TextStyle getHeadingMedium(String text, {Color? color}) {
    final baseStyle = isArabicText(text) 
        ? AppTextStyles.arabicHeadingMedium 
        : AppTextStyles.englishHeadingMedium;
    
    return color != null ? baseStyle.copyWith(color: color) : baseStyle;
  }

  /// Returns optimized text style for body text
  static TextStyle getBodyText(String text, {Color? color}) {
    final baseStyle = isArabicText(text) 
        ? AppTextStyles.arabicBodyText 
        : AppTextStyles.englishBodyText;
    
    return color != null ? baseStyle.copyWith(color: color) : baseStyle;
  }

  /// Returns optimized text style for captions
  static TextStyle getCaption(String text, {Color? color}) {
    final baseStyle = isArabicText(text) 
        ? AppTextStyles.arabicCaption 
        : AppTextStyles.englishCaption;
    
    return color != null ? baseStyle.copyWith(color: color) : baseStyle;
  }

  /// Smart Text widget that automatically applies correct typography
  static Widget smartText(
    String text, {
    TextStyle? style,
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool isHeading = false,
    bool isLargeHeading = false,
    bool isCaption = false,
  }) {
    TextStyle finalStyle;
    
    if (style != null) {
      // Use provided style
      finalStyle = style;
    } else if (isLargeHeading) {
      finalStyle = getHeadingLarge(text, color: color);
    } else if (isHeading) {
      finalStyle = getHeadingMedium(text, color: color);
    } else if (isCaption) {
      finalStyle = getCaption(text, color: color);
    } else {
      finalStyle = getBodyText(text, color: color);
    }

    return Text(
      text,
      style: finalStyle,
      textAlign: textAlign ?? (isArabicText(text) ? TextAlign.right : TextAlign.left),
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  /// Returns appropriate text direction based on text content
  static TextDirection getTextDirection(String text) {
    return isArabicText(text) ? TextDirection.rtl : TextDirection.ltr;
  }

  /// Returns appropriate text alignment based on text content
  static TextAlign getTextAlign(String text) {
    return isArabicText(text) ? TextAlign.right : TextAlign.left;
  }
}

/// Extension on String for easy typography access
extension StringTypography on String {
  bool get isArabic => TypographyHelper.isArabicText(this);
  TextDirection get textDirection => TypographyHelper.getTextDirection(this);
  TextAlign get textAlign => TypographyHelper.getTextAlign(this);
  
  TextStyle headingLarge({Color? color}) => TypographyHelper.getHeadingLarge(this, color: color);
  TextStyle headingMedium({Color? color}) => TypographyHelper.getHeadingMedium(this, color: color);
  TextStyle bodyText({Color? color}) => TypographyHelper.getBodyText(this, color: color);
  TextStyle caption({Color? color}) => TypographyHelper.getCaption(this, color: color);
}
