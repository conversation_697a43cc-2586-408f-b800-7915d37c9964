import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { light, dark }

class ThemeCubit extends Cubit<AppThemeMode> {
  ThemeCubit() : super(AppThemeMode.light) {
    _loadThemePreference();
  }

  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final theme = prefs.getString('theme') ?? 'dark';
    emit(theme == 'dark' ? AppThemeMode.dark : AppThemeMode.light);
  }

  Future<void> toggleTheme() async {
    final newTheme = state == AppThemeMode.light ? AppThemeMode.dark : AppThemeMode.light;
    emit(newTheme);

    // Save the theme preference
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme', newTheme == AppThemeMode.dark ? 'dark' : 'light');
  }
}