import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';

Future<dynamic> showGenericDialog({
  required BuildContext context,
  required Widget content,
  EdgeInsetsGeometry? padding,
  bool barrierDismissible = true,
}) {
  return showDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (BuildContext context) {
      return Dialog(
        backgroundColor: AppColors.white,
        insetPadding: const EdgeInsets.all(34),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: SizedBox(
          width: double.infinity,
          child: Padding(
            padding: padding ??
                const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
            child: content,
          ),
        ),
      );
    },
  );
}
