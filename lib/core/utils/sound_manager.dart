import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/managers/settings_cubit/settings_cubit.dart';

class SoundManager {
  static BuildContext? _context;

  // Initialize with context from root widget
  static void initialize(BuildContext context) {
    _context = context;
  }

  static Future<void> playClickSound() async {
    if (_context == null) {
      throw Exception('SoundManager not initialized with context!');
    }

    final settings = _context!.read<SettingsCubit>().state;
    if (settings.soundClick) {
      final audioPlayer = AudioPlayer();
      await audioPlayer.stop();
      await audioPlayer.play(AssetSource('sounds/click_sound.mp3'));
    }
  }
}
