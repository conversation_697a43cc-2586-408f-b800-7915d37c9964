import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

class NoInternetView extends StatelessWidget {
  const NoInternetView({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(AppAssets.gifsNoInternet),
              const SizedBox(height: 16),
              Text(
                'الانترنت الخاص بك غير متصل، يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
                textAlign: TextAlign.center,
                style: AppTextStyles.font20Regular.copyWith(
                  color: AppColors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
