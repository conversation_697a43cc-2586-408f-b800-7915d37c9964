import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../core/styles/app_colors.dart';

import '../styles/app_text_styles.dart';

class CustomDropDown extends StatefulWidget {
  const CustomDropDown({
    super.key,
    required this.items,
    required this.hint,
    required this.icon,
    required this.onValueChanged,
  });

  final List<DropdownMenuItem<String>>? items;

  final String hint;
  final String icon;
  final ValueChanged<String?> onValueChanged;

  @override
  State<CustomDropDown> createState() => _CustomDropDownState();
}

class _CustomDropDownState extends State<CustomDropDown> {
  String? selectedValue;
  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField2<String>(
      hint: Row(
        children: [
          SvgPicture.asset(widget.icon),
          const SizedBox(width: 16),
          Text(widget.hint, style: AppTextStyles.font16Medium),
        ],
      ),
      items: widget.items,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: const BorderSide(color: AppColors.green),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: const BorderSide(color: AppColors.green),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: const BorderSide(color: AppColors.green),
        ),
      ),
      value: selectedValue,
      iconStyleData: const IconStyleData(
        icon: Icon(Icons.arrow_drop_down, color: AppColors.green),
        openMenuIcon: Icon(Icons.arrow_drop_up, color: AppColors.green),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      onChanged: (String? newValue) {
        setState(() {
          selectedValue = newValue!;
          widget.onValueChanged(newValue);
        });
      },
      dropdownStyleData: DropdownStyleData(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        useSafeArea: true,
      ),
    );
  }
}
