import 'package:flutter/material.dart';
import '../../core/styles/app_colors.dart';

import '../styles/app_text_styles.dart';

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textStyle,
    this.transparent = false,
    this.radius = 4,
    this.borderColor,
    this.margin,
    this.padding,
    this.isFullWidth = false,
  });

  final String text;
  final void Function()? onPressed;
  final Color? backgroundColor;
  final TextStyle? textStyle;
  final double radius;
  final bool transparent;
  final Color? borderColor;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final bool isFullWidth;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      padding: margin ?? EdgeInsets.zero,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: padding ?? const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius),
            side: BorderSide(color: borderColor ?? Colors.transparent),
          ),
          backgroundColor: backgroundColor ?? AppColors.yellow,
        ),
        onPressed: onPressed,
        child: Text(
          text,
          style: textStyle ??
              AppTextStyles.font16Medium.copyWith(color: AppColors.white),
        ),
      ),
    );
  }
}
