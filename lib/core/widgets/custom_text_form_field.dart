import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../styles/app_colors.dart';
import '../styles/app_text_styles.dart';

class CustomTextFormField extends StatefulWidget {
  const CustomTextFormField({
    super.key,
    this.keyboardType,
    this.controller,
    // this.labelText,
    this.suffix,
    this.prefix,
    this.hintText,
    this.maxLines = 1,
    this.borderColor = AppColors.green,
    this.inputFormatters,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.isPassword = false,
    this.isRequired = false,
    this.title,
  });

  final TextInputType? keyboardType;
  final TextEditingController? controller;
  // final String? labelText;
  final String? hintText;
  final Widget? suffix;
  final Widget? prefix;
  final int maxLines;
  final Color borderColor;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final bool isPassword;
  final bool? isRequired;
  final String? title;

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Text(
              widget.title!,
              style: AppTextStyles.font14Regular.copyWith(
                color: AppColors.black,
              ),
            ),
          ),
        TextFormField(
          obscureText: widget.isPassword ? _obscureText : false,
          readOnly: widget.readOnly,
          onTap: widget.onTap,
          onChanged: widget.onChanged,
          maxLines: widget.maxLines,
          controller: widget.controller,
          keyboardType: widget.keyboardType,
          validator: widget.isRequired == true
              ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'This field is required';
                  }
                  return null;
                }
              : null,
          cursorColor: AppColors.yellow,
          style: AppTextStyles.font16Regular,
          textInputAction: TextInputAction.next,
          inputFormatters: widget.inputFormatters,
          decoration: InputDecoration(
            // labelText: widget.labelText,
            labelStyle:
                AppTextStyles.font16Regular.copyWith(color: AppColors.black),
            hintText: widget.hintText,
            hintStyle:
                AppTextStyles.font16Regular.copyWith(color: AppColors.black),
            suffixIcon: widget.isPassword
                ? IconButton(
                    icon: Icon(
                      _obscureText ? Icons.visibility_off : Icons.visibility,
                      color: Theme.of(context).hintColor.withOpacity(0.3),
                    ),
                    onPressed: _toggle,
                  )
                : null,
            prefixIcon: widget.prefix != null
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: widget.prefix,
                  )
                : null,
            isDense: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.lightGrey2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.lightGrey2),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.lightGrey2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  void _toggle() {
    setState(() => _obscureText = !_obscureText);
  }
}
