import 'package:gather_point/Core/databases/api/end_points.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/auth/Data/Models/user_model/user_model.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';

abstract class LoginRemoteDataSource {
  Future<void> login({required String phone});

  Future<UserEntity> validateOTP({required String phone, required String otp});

  Future<UserEntity> loginToken({required String accessToken});

  Future<UserEntity> loginGuest();
}

class LoginRemoteDataSourceImpl implements LoginRemoteDataSource {
  final DioConsumer apiConsumer;

  LoginRemoteDataSourceImpl({required this.apiConsumer});

  @override
  Future<void> login({required String phone}) async {
    await apiConsumer.post(
      EndPoints.login,
      data: {'phone': phone},
    );
  }

  @override
  Future<UserEntity> validateOTP(
      {required String phone, required String otp}) async {
    var data = await apiConsumer.post(
      EndPoints.validateOTP,
      data: {'phone': phone, 'otp': otp},
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<UserEntity> loginToken({required String accessToken}) async {
    var data = await apiConsumer.post(
      EndPoints.loginToken,
      data: {
        'login_token': accessToken,
        'login_method': '2',
      },
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<UserEntity> loginGuest() async {
    var data = await apiConsumer.post(
      EndPoints.loginGuest,
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }
}
