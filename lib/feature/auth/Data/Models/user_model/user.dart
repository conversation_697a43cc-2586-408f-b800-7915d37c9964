class User {
  int? id;
  String? fullName;
  String? username;
  dynamic bio;
  String? email;
  dynamic phone;
  int? gender;
  dynamic birthdate;
  String? referral;
  bool? otpApproved;
  bool? isGuest;
  bool? isHosterMode;
  int? userType;
  String? profilePicture;

  User({
    this.id,
    this.fullName,
    this.username,
    this.bio,
    this.email,
    this.phone,
    this.gender,
    this.birthdate,
    this.referral,
    this.otpApproved,
    this.isGuest,
    this.isHosterMode,
    this.userType,
    this.profilePicture,
  });

  @override
  String toString() {
    return 'User(id: $id, fullName: $fullName, username: $username, bio: $bio, email: $email, phone: $phone, gender: $gender, birthdate: $birthdate, referral: $referral, otpApproved: $otpApproved, is_guest: $isGuest, is_hoster_mode: $isHosterMode, user_type_id: $userType, profilePicture: $profilePicture)';
  }

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'] as int?,
        fullName: json['full_name'] as String?,
        username: json['username'] as String?,
        bio: json['bio'] as dynamic,
        email: json['email'] as String?,
        phone: json['phone'] as dynamic,
        gender: json['gender'] as int?,
        birthdate: json['birthdate'] as dynamic,
        referral: json['referral'] as String,
        otpApproved: json['otp_approved'] as bool?,
        isGuest: json['is_guest'] as bool?,
        isHosterMode: json['is_hoster_mode'] as bool?,
        userType: json['user_type_id'] as int?,
        profilePicture: json['profile_picture'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'full_name': fullName,
        'username': username,
        'bio': bio,
        'email': email,
        'phone': phone,
        'gender': gender,
        'birthdate': birthdate,
        'referral': referral,
        'otp_approved': otpApproved,
        'is_guest': isGuest,
        'is_hoster_mode': isHosterMode,
        'user_type_id': userType,
        'profile_picture': profilePicture,
      };
}
