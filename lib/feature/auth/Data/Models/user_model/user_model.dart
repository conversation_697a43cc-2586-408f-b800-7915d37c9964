import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';

import 'user.dart';

class UserModel extends UserEntity {
  User? user;
  String? accessToken;
  String? tokenType;
  String? expiresAt;

  UserModel({
    this.user,
    this.accessToken,
    this.tokenType,
    this.expiresAt,
  }) : super(
          fullName: user?.fullName ?? '',
          email: user?.email ?? '',
          id: user?.id ?? 0,
          token: accessToken ?? '',
          image: user?.profilePicture ?? '',
          referral: user?.referral ?? '',
          phone: user?.phone ?? '',
          username: user?.username ?? '',
          bio: user?.bio ?? '',
          birthdate: user?.birthdate ?? '',
          gender: user?.gender ?? 0,
          otpApproved: user?.otpApproved ?? false,
          isGuest: user?.isGuest ?? false,
          isHosterMode: user?.isHosterMode ?? false,
          userType: user?.userType ?? 0,
        );

  @override
  String toString() {
    return 'UserModel(user: $user, accessToken: $accessToken, tokenType: $tokenType, expiresAt: $expiresAt)';
  }

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        user: json['user'] == null
            ? null
            : User.fromJson(json['user'] as Map<String, dynamic>),
        accessToken: json['access_token'] as String?,
        tokenType: json['token_type'] as String?,
        expiresAt: json['expires_at'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'user': user?.toJson(),
        'access_token': accessToken,
        'token_type': tokenType,
        'expires_at': expiresAt,
      };
}
