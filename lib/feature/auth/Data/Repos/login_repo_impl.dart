import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:gather_point/core/errors/exceptions.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/feature/auth/Data/Data%20Sources/login_local_data_source.dart';
import 'package:gather_point/feature/auth/Data/Data%20Sources/login_remote_data_source.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Repos/login_repo.dart';

class LoginRepoImpl implements LoginRepo {
  final LoginRemoteDataSource loginRemoteDataSource;
  final LoginLocalDataSource loginLocalDataSource;

  LoginRepoImpl({
    required this.loginRemoteDataSource,
    required this.loginLocalDataSource,
  });

  @override
  Future<Either<Failure, void>> login({required String phone}) async {
    try {
      await loginRemoteDataSource.login(phone: phone);

      return const Right(null);
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> validateOTP({
    required String phone,
    required String otp,
  }) async {
    try {
      UserEntity user =
          await loginRemoteDataSource.validateOTP(phone: phone, otp: otp);

      return Right(user);
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> loginToken({
    required String accessToken,
  }) async {
    try {
      UserEntity user =
          await loginRemoteDataSource.loginToken(accessToken: accessToken);

      return Right(user);
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> loginGuest() async {
    try {
      UserEntity user =
          await loginRemoteDataSource.loginGuest();

      return Right(user);
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioException(e));
      }
      return left(ServerFailure(errMessage: e.toString()));
    }
  }
}
