import 'package:hive_flutter/hive_flutter.dart';

part 'user_entity.g.dart';

@HiveType(typeId: 1)
class UserEntity extends HiveObject {
  @HiveField(0)
  String fullName;
  @HiveField(1)
  String email;
  @HiveField(2)
  int id;
  @Hive<PERSON>ield(3)
  String token;
  @HiveField(4)
  String image;
  @HiveField(5)
  String referral;
  @HiveField(6)
  String phone;
  @Hive<PERSON>ield(7)
  String username;
  @HiveField(8)
  String bio;
  @HiveField(9)
  int gender;
  @HiveField(10)
  bool otpApproved;
  @HiveField(11)
  String birthdate;
  @HiveField(12)
  bool isGuest;
  @HiveField(13)
  int userType;
  @HiveField(14)
  bool isHosterMode; // Tracks if the user is in hoster mode

  UserEntity({
    required this.fullName,
    required this.email,
    required this.id,
    required this.token,
    required this.image,
    required this.referral,
    required this.phone,
    required this.username,
    required this.bio,
    required this.birthdate,
    required this.gender,
    required this.otpApproved,
    required this.isGuest,
    required this.userType,
    required this.isHosterMode,
  });

  UserEntity copyWith({
    String? fullName,
    String? email,
    int? id,
    String? token,
    String? image,
    String? referral,
    String? phone,
    String? username,
    String? bio,
    String? birthdate,
    int? gender,
    bool? otpApproved,
    bool? isGuest,
    int? userType,
    bool? isHosterMode,
  }) {
    return UserEntity(
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      id: id ?? this.id,
      token: token ?? this.token,
      image: image ?? this.image,
      referral: referral ?? this.referral,
      phone: phone ?? this.phone,
      username: username ?? this.username,
      bio: bio ?? this.bio,
      birthdate: birthdate ?? this.birthdate,
      gender: gender ?? this.gender,
      otpApproved: otpApproved ?? this.otpApproved,
      isGuest: isGuest ?? this.isGuest,
      userType: userType ?? this.userType,
      isHosterMode: isHosterMode ?? this.isHosterMode,
    );
  }

  @override
  String toString() {
    return 'UserEntity(fullName: $fullName, username: $username, email: $email, '
        'id: $id, token: $token, image: $image, referral: $referral, '
        'phone: $phone, bio: $bio, gender: $gender, otpApproved: $otpApproved, '
        'birthdate: $birthdate),  isGuest: $isGuest,  userType: $userType)';
  }
}
