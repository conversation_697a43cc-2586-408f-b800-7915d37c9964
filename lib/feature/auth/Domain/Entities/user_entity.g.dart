// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_entity.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserEntityAdapter extends TypeAdapter<UserEntity> {
  @override
  final int typeId = 1;

  @override
  UserEntity read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserEntity(
      fullName: fields[0] as String,
      email: fields[1] as String,
      id: fields[2] as int,
      token: fields[3] as String,
      image: fields[4] as String,
      referral: fields[5] as String,
      phone: fields[6] as String,
      username: fields[7] as String,
      bio: fields[8] as String,
      birthdate: fields[11] as String,
      gender: fields[9] as int,
      otpApproved: fields[10] as bool,
      isGuest: fields[12] as bool,
      userType: fields[13] as int,
      isHosterMode: fields[14] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, UserEntity obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.fullName)
      ..writeByte(1)
      ..write(obj.email)
      ..writeByte(2)
      ..write(obj.id)
      ..writeByte(3)
      ..write(obj.token)
      ..writeByte(4)
      ..write(obj.image)
      ..writeByte(5)
      ..write(obj.referral)
      ..writeByte(6)
      ..write(obj.phone)
      ..writeByte(7)
      ..write(obj.username)
      ..writeByte(8)
      ..write(obj.bio)
      ..writeByte(9)
      ..write(obj.gender)
      ..writeByte(10)
      ..write(obj.otpApproved)
      ..writeByte(11)
      ..write(obj.birthdate)
      ..writeByte(12)
      ..write(obj.isGuest)
      ..writeByte(13)
      ..write(obj.userType)
      ..writeByte(14)
      ..write(obj.isHosterMode);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserEntityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
