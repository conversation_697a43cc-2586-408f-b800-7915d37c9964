import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';

abstract class LoginRepo {
  Future<Either<Failure, void>> login({required String phone});
  Future<Either<Failure, UserEntity>> validateOTP({
    required String phone,
    required String otp,
  });

  Future<Either<Failure, UserEntity>> loginToken({required String accessToken});
  Future<Either<Failure, UserEntity>> loginGuest();
}
