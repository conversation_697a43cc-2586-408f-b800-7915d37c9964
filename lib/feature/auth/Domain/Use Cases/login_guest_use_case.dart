import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Repos/login_repo.dart';

class LoginGuestUseCase extends UseCase<UserEntity, String> {
  final LoginRepo loginRepo;

  LoginGuestUseCase({required this.loginRepo});

  @override
  Future<Either<Failure, UserEntity>> call(String param) async {
    return await loginRepo.loginGuest();
  }
}
