import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/feature/auth/Domain/Repos/login_repo.dart';

class LoginUseCase extends UseCase<void, String> {
  final LoginRepo loginRepo;

  LoginUseCase({required this.loginRepo});

  @override
  Future<Either<Failure, void>> call(String param) async {
    return await loginRepo.login(phone: param);
  }
}
