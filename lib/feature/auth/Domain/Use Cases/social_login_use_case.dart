import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/services/social_auth_service.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use Cases/login_token_use_case.dart';

/// Use case for social login (Google and Apple)
/// Integrates with existing LoginTokenUseCase for backend authentication
class SocialLoginUseCase {
  final SocialAuthService _socialAuthService;
  final LoginTokenUseCase _loginTokenUseCase;

  SocialLoginUseCase({
    required SocialAuthService socialAuthService,
    required LoginTokenUseCase loginTokenUseCase,
  })  : _socialAuthService = socialAuthService,
        _loginTokenUseCase = loginTokenUseCase;

  /// Sign in with Google
  /// Returns Either<Failure, UserEntity> where UserEntity contains user data
  Future<Either<Failure, UserEntity>> signInWithGoogle() async {
    try {
      // Step 1: Authenticate with Google
      final String? googleAccessToken = await _socialAuthService.signInWithGoogle();

      if (googleAccessToken == null) {
        return Left(SocialAuthFailure('Google sign-in was cancelled or failed'));
      }

      // Step 2: Send Google token to backend for verification and get user data
      final result = await _loginTokenUseCase.call(googleAccessToken);

      return result.fold(
        (failure) {
          debugPrint('Backend authentication failed: ${failure.toString()}');
          return Left(failure);
        },
        (userEntity) {
          debugPrint('Social login successful: Google -> User data received');
          return Right(userEntity);
        },
      );

    } catch (error) {
      debugPrint('Google sign-in error: $error');
      return Left(SocialAuthFailure('Google sign-in failed: $error'));
    }
  }

  /// Sign in with Apple
  /// Returns Either<Failure, UserEntity> where UserEntity contains user data
  Future<Either<Failure, UserEntity>> signInWithApple() async {
    try {
      // Step 1: Authenticate with Apple
      final String? appleIdentityToken = await _socialAuthService.signInWithApple();

      if (appleIdentityToken == null) {
        return Left(SocialAuthFailure('Apple sign-in was cancelled or failed'));
      }

      // Step 2: Send Apple token to backend for verification and get user data
      final result = await _loginTokenUseCase.call(appleIdentityToken);

      return result.fold(
        (failure) {
          debugPrint('Backend authentication failed: ${failure.toString()}');
          return Left(failure);
        },
        (userEntity) {
          debugPrint('Social login successful: Apple -> User data received');
          return Right(userEntity);
        },
      );

    } catch (error) {
      debugPrint('Apple sign-in error: $error');
      return Left(SocialAuthFailure('Apple sign-in failed: $error'));
    }
  }

  /// Sign out from all social providers
  Future<void> signOut() async {
    await _socialAuthService.signOutAll();
  }

  /// Check if user is signed in with Google
  Future<bool> isSignedInWithGoogle() async {
    return await _socialAuthService.isSignedInWithGoogle();
  }

  /// Get current Google user info
  Map<String, dynamic>? getCurrentGoogleUserInfo() {
    return _socialAuthService.getGoogleUserInfo();
  }

  /// Disconnect from Google (revoke access)
  Future<void> disconnectGoogle() async {
    await _socialAuthService.disconnectGoogle();
  }
}



/// Social authentication specific failure
class SocialAuthFailure extends Failure {
  SocialAuthFailure(String message) : super(errMessage: message);
}

/// Social login result with additional metadata
class SocialLoginResult {
  final bool success;
  final String? backendToken;
  final String? errorMessage;
  final SocialAuthProvider? provider;
  final Map<String, dynamic>? userInfo;

  SocialLoginResult({
    required this.success,
    this.backendToken,
    this.errorMessage,
    this.provider,
    this.userInfo,
  });

  factory SocialLoginResult.success({
    required String backendToken,
    required SocialAuthProvider provider,
    Map<String, dynamic>? userInfo,
  }) {
    return SocialLoginResult(
      success: true,
      backendToken: backendToken,
      provider: provider,
      userInfo: userInfo,
    );
  }

  factory SocialLoginResult.failure({
    required String errorMessage,
    SocialAuthProvider? provider,
  }) {
    return SocialLoginResult(
      success: false,
      errorMessage: errorMessage,
      provider: provider,
    );
  }
}

/// Helper extension for social login integration
extension SocialLoginUseCaseExtension on SocialLoginUseCase {
  /// Unified social login method that handles both Google and Apple
  Future<Either<Failure, UserEntity>> signInWithProvider(SocialAuthProvider provider) async {
    switch (provider) {
      case SocialAuthProvider.google:
        return await signInWithGoogle();
      case SocialAuthProvider.apple:
        return await signInWithApple();
    }
  }

  /// Get available social login providers based on platform
  List<SocialAuthProvider> getAvailableProviders() {
    final providers = <SocialAuthProvider>[SocialAuthProvider.google];
    
    // Apple Sign-In is only available on iOS and macOS
    if (defaultTargetPlatform == TargetPlatform.iOS || 
        defaultTargetPlatform == TargetPlatform.macOS) {
      providers.add(SocialAuthProvider.apple);
    }
    
    return providers;
  }

  /// Check if a specific provider is available
  bool isProviderAvailable(SocialAuthProvider provider) {
    return getAvailableProviders().contains(provider);
  }
}
