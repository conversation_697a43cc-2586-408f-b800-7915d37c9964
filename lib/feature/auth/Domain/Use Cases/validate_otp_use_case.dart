import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Repos/login_repo.dart';

class ValidateOTPUseCase extends UseCase<UserEntity, Map<String, String>> {
  final LoginRepo loginRepo;

  ValidateOTPUseCase({required this.loginRepo});

  @override
  Future<Either<Failure, UserEntity>> call(Map<String, String> param) async {
    return await loginRepo.validateOTP(
      phone: param['phone']!,
      otp: param['otp']!,
    );
  }
}
