import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_otp_use_case.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'validate_otp_state.dart';

class ValidateOtpCubit extends Cubit<ValidateOtpState> {
  ValidateOtpCubit({
    required this.validateOTPUseCase,
    required this.loginTokenUseCase,
  }) : super(ValidateOtpInitial());

  final ValidateOTPUseCase validateOTPUseCase;
  final LoginTokenUseCase loginTokenUseCase;

  String otpCode = '';

  void validateOtp({required String phone}) async {
    emit(ValidateOtpLoadingState());

    final result =
        await validateOTPUseCase.call({'phone': phone, 'otp': otpCode});
    result.fold(
      (failure) =>
          emit(ValidateOtpFailureState(errMessage: failure.errMessage)),
      (success) {
        Hive.box<UserEntity>(AppConstants.kMyProfileBoxName).put(AppConstants.kMyProfileKey, success);
        emit(ValidateOtpSuccessState());
      },
    );
  }

  void loginToken({required String accessToken}) async {
    emit(LoginTokenLoadingState());

    final result = await loginTokenUseCase.call(accessToken);
    result.fold(
      (failure) => emit(LoginTokenFailureState(errMessage: failure.errMessage)),
      (success) {
        Hive.box<UserEntity>(AppConstants.kMyProfileBoxName).put(AppConstants.kMyProfileKey, success);
        emit(LoginTokenSuccessState());
      },
    );
  }

}
