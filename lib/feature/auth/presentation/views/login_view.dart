import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:gather_point/feature/auth/presentation/manager/login_cubit/login_cubit.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/login_view_body.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_guest_use_case.dart';
import 'package:gather_point/core/services/service_locator.dart';

class LoginView extends StatelessWidget {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Set status bar style based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      ),
    );

    return BlocProvider(
      create: (context) => LoginCubit(
        loginUseCase: getIt<LoginUseCase>(),
        loginTokenUseCase: getIt<LoginTokenUseCase>(),
        loginGuestUseCase: getIt<LoginGuestUseCase>(),
      ),
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            // Use theme-aware background
            gradient: isDark
              ? LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black,
                    Colors.grey[900]!,
                  ],
                )
              : const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1E3A8A),
                    Color(0xFF3B82F6),
                  ],
                ),
            image: DecorationImage(
              image: const AssetImage(AppAssets.imagesAuthBg),
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(
                isDark
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.blue.withValues(alpha: 0.2),
                BlendMode.overlay,
              ),
            ),
          ),
          child: const LoginViewBody(),
        ),
      ),
    );
  }
}
