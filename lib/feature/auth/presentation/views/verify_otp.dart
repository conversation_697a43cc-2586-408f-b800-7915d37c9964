// VerifyOTPView
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_otp_use_case.dart';
import 'package:gather_point/feature/auth/presentation/Manager/login_cubit/login_cubit.dart';
import 'package:gather_point/feature/auth/presentation/Manager/validate_otp_cubit/validate_otp_cubit.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/verify_otp_view_body.dart';
import 'package:go_router/go_router.dart';

class VerifyOTPView extends StatelessWidget {
  const VerifyOTPView({super.key});

  @override
  Widget build(BuildContext context) {
    final String phone = BlocProvider.of<LoginCubit>(context).phoneController.text;//GoRouterState.of(context).extra! as String;

    return BlocProvider(
      create: (context) => ValidateOtpCubit(
        validateOTPUseCase: getIt<ValidateOTPUseCase>(),
        loginTokenUseCase: getIt<LoginTokenUseCase>(),
      ),
      child: Scaffold(
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        SoundManager.playClickSound();
                        GoRouter.of(context).pop();
                      },
                      child: Container(
                        width: 35,
                        height: 35,
                        decoration: const BoxDecoration(
                          color: AppColors.yellow,
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(AppAssets.iconsBack),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'التحقق من رقم جوالك',
                      style: AppTextStyles.font20Bold
                          .copyWith(color: AppColors.white),
                    ),
                    const Spacer(),
                  ],
                ),
                const SizedBox(height: 24),
                Expanded(child: VerifyOTPViewBody(phone: phone)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
