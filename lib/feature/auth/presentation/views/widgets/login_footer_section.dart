import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

class LoginFooterSection extends StatelessWidget {
  const LoginFooterSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return FractionallySizedBox(
      widthFactor: 1.5,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 0),
              color: AppColors.black.withOpacity(0.75),
              blurRadius: 50,
            ),
          ],
        ),
        child: Column(
          children: [
            /*Text(
              'أو تابع بإستخدام',
              style: AppTextStyles.font14Bold.copyWith(
                color: AppColors.white,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(AppAssets.iconsGoogle),
                const SizedBox(width: 30),
                SvgPicture.asset(AppAssets.iconsFacebook),
                const SizedBox(width: 30),
                SvgPicture.asset(AppAssets.iconsApple),
              ],
            ),*/
            const SizedBox(height: 20),
            Text(
              'الضغط علي متابعة يعني أنك علي علم',
              style: AppTextStyles.font14Bold.copyWith(
                color: AppColors.white,
                inherit: true, // Ensure compatibility for lerp
              ),
            ),
            RichText(
              text: TextSpan(
                text: 'بشروط الإستخدام',
                style: AppTextStyles.font14Bold.copyWith(
                  color: AppColors.yellow,
                  inherit: true, // Ensure compatibility for lerp
                ),
                children: <TextSpan>[
                  TextSpan(
                    text: ' و ',
                    style: AppTextStyles.font14Bold.copyWith(
                      color: AppColors.white,
                      inherit: true, // Ensure compatibility for lerp
                    ),
                  ),
                  TextSpan(
                    text: 'سياسة الخصوصية',
                    style: AppTextStyles.font14Bold.copyWith(
                      color: AppColors.yellow,
                      inherit: true, // Ensure compatibility for lerp
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
