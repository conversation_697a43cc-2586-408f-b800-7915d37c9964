// VerifyOTPViewBody
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/feature/auth/presentation/Manager/validate_otp_cubit/validate_otp_cubit.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/enter_otp_section.dart';
import 'package:go_router/go_router.dart';

class VerifyOTPViewBody extends StatelessWidget {
  final String phone;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  VerifyOTPViewBody({super.key, required this.phone});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ValidateOtpCubit, ValidateOtpState>(
      listener: (context, state) {
        if (state is ValidateOtpSuccessState) {
          GoRouter.of(context).go(RoutesKeys.kHomeViewTab);
        } else if (state is ValidateOtpFailureState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.errMessage)),
          );
        }
      },
      builder: (context, state) {
        return SingleChildScrollView(
          child: Column(
            children: [
              Image.asset(AppAssets.imagesVerifyOtp),
              const SizedBox(height: 35),
              Text(
                'أدخل كود التحقق',
                style:
                    AppTextStyles.font20Bold.copyWith(color: AppColors.white),
              ),
              const SizedBox(height: 20),
              Text(
                'لقد تم إرسال رمز مكون من 4 أرقام إلى جوالك',
                style: AppTextStyles.font13Bold
                    .copyWith(color: AppColors.darkGrey8),
              ),
              const SizedBox(height: 35),
              EnterOTPSection(formKey: formKey),
              const SizedBox(height: 45),
              ConditionalBuilder(
                condition: state is! ValidateOtpLoadingState,
                builder: (context) => PrimaryButton(
                  onPressed: () {
                    SoundManager.playClickSound();
                    if (formKey.currentState?.validate() ?? false) {
                      context
                          .read<ValidateOtpCubit>()
                          .validateOtp(phone: phone);
                    }
                  },
                  label: 'المتابعة',
                  padding: const EdgeInsets.all(12),
                ),
                fallback: (context) => const CircularProgressIndicator(
                  color: AppColors.yellow,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
