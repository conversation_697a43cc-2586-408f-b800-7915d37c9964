import 'dart:io';
import 'dart:async';
import 'dart:ui';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:hive/hive.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';

class Facility {
  final int id;
  final String title;
  final String icon;

  Facility({required this.id, required this.title, required this.icon});

  factory Facility.fromJson(Map<String, dynamic> json) {
    return Facility(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      icon: json['icon'] ?? '',
    );
  }
}

/// 🚀 ULTIMATE Enhanced Property Creation Form with Premium UI/UX
class EnhancedPropertyCreationForm extends StatefulWidget {
  const EnhancedPropertyCreationForm({super.key});

  @override
  State<EnhancedPropertyCreationForm> createState() => _EnhancedPropertyCreationFormState();
}

class _EnhancedPropertyCreationFormState extends State<EnhancedPropertyCreationForm>
    with TickerProviderStateMixin {
  String? _itemId;
  late final DioConsumer _dioConsumer;
  final bool _isLoading = false;
  int _currentStep = 0;
  
  // Advanced animation controllers
  late AnimationController _progressAnimationController;
  late AnimationController _stepAnimationController;
  late AnimationController _celebrationController;
  late AnimationController _shakeController;
  late AnimationController _pulseController;
  late AnimationController _glowController;
  
  // Form validation and state
  final Map<int, bool> _stepCompletionStatus = {};
  final Map<String, String> _validationErrors = {};
  final bool _autoSaveEnabled = true;
  DateTime? _lastAutoSave;
  Timer? _autoSaveTimer;
  
  // Enhanced UI state
  bool _showAdvancedOptions = false;
  final bool _isDragMode = false;
  int? _hoveredImageIndex;
  final bool _showValidationHints = true;
  final Set<int> _visitedSteps = {};
  
  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _dailyPriceController = TextEditingController();
  final _weeklyPriceController = TextEditingController();
  final _monthlyPriceController = TextEditingController();
  final _bathroomsController = TextEditingController();
  final _bedroomsController = TextEditingController();
  final _guestsController = TextEditingController();
  final _bookingPolicyController = TextEditingController();
  final _cancellationPolicyController = TextEditingController();

  LatLng? _location;
  final List<File> _imageGallery = [];
  final List<int> _facilities = [];
  bool _includeCommissionDaily = false;
  bool _includeCommissionWeekly = false;
  bool _includeCommissionMonthly = false;

  final ImagePicker _picker = ImagePicker();
  final List<ServiceCategory> _categories = [];
  int? _selectedCategoryId;
  final bool _isLoadingCategories = false;

  final List<Facility> _availableFacilities = [];
  final bool _isLoadingFacilities = false;

  @override
  void initState() {
    super.initState();
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    
    // Initialize animation controllers
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);
    
    // Initialize auto-save
    _setupAutoSave();
    
    _loadServiceCategories();
    _loadFacilities();
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _stepAnimationController.dispose();
    _celebrationController.dispose();
    _shakeController.dispose();
    _pulseController.dispose();
    _glowController.dispose();
    _autoSaveTimer?.cancel();
    
    // Dispose controllers
    _titleController.dispose();
    _descriptionController.dispose();
    _dailyPriceController.dispose();
    _weeklyPriceController.dispose();
    _monthlyPriceController.dispose();
    _bathroomsController.dispose();
    _bedroomsController.dispose();
    _guestsController.dispose();
    _bookingPolicyController.dispose();
    _cancellationPolicyController.dispose();
    
    super.dispose();
  }

  /// Setup auto-save functionality with smart detection
  void _setupAutoSave() {
    if (_autoSaveEnabled) {
      _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        _performAutoSave();
      });
    }
  }

  /// Perform intelligent auto-save
  void _performAutoSave() {
    if (_hasUnsavedChanges()) {
      _lastAutoSave = DateTime.now();
      _saveDraft();
    }
  }

  /// Check if there are unsaved changes
  bool _hasUnsavedChanges() {
    return _titleController.text.isNotEmpty ||
           _descriptionController.text.isNotEmpty ||
           _location != null ||
           _imageGallery.isNotEmpty ||
           _facilities.isNotEmpty;
  }

  /// Save draft with beautiful feedback
  void _saveDraft() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.cloud_done_outlined, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              Text(
                'Draft auto-saved',
                style: AppTextStyles.font12Medium.copyWith(color: Colors.white),
              ),
            ],
          ),
          backgroundColor: Colors.green.withValues(alpha: 0.9),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
          margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    }
  }

  /// Advanced form validation with detailed feedback
  bool _validateCurrentStep() {
    _validationErrors.clear();
    bool isValid = true;

    switch (_currentStep) {
      case 0: // Title & Description
        if (_titleController.text.trim().isEmpty) {
          _validationErrors['title'] = '${S.of(context).propertyTitle} is required';
          isValid = false;
        }
        if (_descriptionController.text.trim().isEmpty) {
          _validationErrors['description'] = '${S.of(context).propertyDescription} is required';
          isValid = false;
        }
        if (_selectedCategoryId == null) {
          _validationErrors['category'] = 'Category selection is required';
          isValid = false;
        }
        break;
      case 1: // Location
        if (_location == null) {
          _validationErrors['location'] = 'Location is required';
          isValid = false;
        }
        break;
      case 2: // Images
        if (_imageGallery.isEmpty) {
          _validationErrors['images'] = 'At least one image is required';
          isValid = false;
        }
        break;
      case 3: // Facilities
        if (_facilities.isEmpty) {
          _validationErrors['facilities'] = 'At least one facility must be selected';
          isValid = false;
        }
        break;
      case 4: // Pricing
        if (_dailyPriceController.text.trim().isEmpty) {
          _validationErrors['dailyPrice'] = 'Daily price is required';
          isValid = false;
        }
        break;
      case 5: // Details
        if (_bathroomsController.text.trim().isEmpty) {
          _validationErrors['bathrooms'] = 'Number of bathrooms is required';
          isValid = false;
        }
        if (_bedroomsController.text.trim().isEmpty) {
          _validationErrors['bedrooms'] = 'Number of bedrooms is required';
          isValid = false;
        }
        if (_guestsController.text.trim().isEmpty) {
          _validationErrors['guests'] = 'Number of guests is required';
          isValid = false;
        }
        break;
    }

    // Update step completion status
    _stepCompletionStatus[_currentStep] = isValid;
    _visitedSteps.add(_currentStep);

    if (!isValid) {
      _shakeController.forward().then((_) => _shakeController.reset());
      HapticFeedback.lightImpact();
    }

    return isValid;
  }

  /// Get completion percentage
  double get _completionPercentage {
    int completedSteps = _stepCompletionStatus.values.where((completed) => completed).length;
    return completedSteps / 6.0;
  }

  /// Check if step is completed
  bool _isStepCompleted(int step) {
    return _stepCompletionStatus[step] ?? false;
  }

  /// Get step icon based on completion status
  IconData _getStepIcon(int step) {
    if (_isStepCompleted(step)) {
      return Icons.check_circle;
    } else if (_visitedSteps.contains(step)) {
      return Icons.error_outline;
    } else {
      return Icons.radio_button_unchecked;
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Scaffold(
      backgroundColor: context.backgroundColor,
      extendBodyBehindAppBar: true,
      appBar: _buildGlassmorphicAppBar(context, s),
      body: _buildEnhancedBody(context, s),
      floatingActionButton: _buildSmartFAB(context, s),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  /// 🌟 Glassmorphic App Bar with blur effects
  PreferredSizeWidget _buildGlassmorphicAppBar(BuildContext context, S s) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(120),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  context.accentColor.withValues(alpha: 0.1),
                  context.cardColor.withValues(alpha: 0.8),
                ],
              ),
              border: Border(
                bottom: BorderSide(
                  color: context.accentColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Column(
                  children: [
                    // App Bar Content
                    Row(
                      children: [
                        // Back Button with glow effect
                        AnimatedBuilder(
                          animation: _glowController,
                          builder: (context, child) {
                            return Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: context.accentColor.withValues(
                                      alpha: 0.3 * _glowController.value,
                                    ),
                                    blurRadius: 8 + (4 * _glowController.value),
                                    spreadRadius: 1,
                                  ),
                                ],
                              ),
                              child: IconButton(
                                onPressed: () => Navigator.pop(context),
                                icon: Icon(
                                  Icons.arrow_back_ios_new_rounded,
                                  color: context.primaryTextColor,
                                ),
                                style: IconButton.styleFrom(
                                  backgroundColor: context.cardColor.withValues(alpha: 0.8),
                                  shape: const CircleBorder(),
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(width: 16),

                        // Title with gradient text
                        Expanded(
                          child: ShaderMask(
                            shaderCallback: (bounds) => LinearGradient(
                              colors: [
                                context.accentColor,
                                context.accentColor.withValues(alpha: 0.7),
                              ],
                            ).createShader(bounds),
                            child: Text(
                              s.createProperty,
                              style: AppTextStyles.font20Bold.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        ),

                        // Settings button with pulse animation
                        AnimatedBuilder(
                          animation: _pulseController,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 1.0 + (0.1 * _pulseController.value),
                              child: IconButton(
                                onPressed: () => _toggleAdvancedOptions(),
                                icon: Icon(
                                  _showAdvancedOptions
                                      ? Icons.settings_rounded
                                      : Icons.tune_rounded,
                                  color: context.accentColor,
                                ),
                                style: IconButton.styleFrom(
                                  backgroundColor: context.accentColor.withValues(alpha: 0.1),
                                  shape: const CircleBorder(),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Enhanced Progress Indicator
                    _buildGlassmorphicProgress(context, s),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 🎯 Toggle advanced options with animation
  void _toggleAdvancedOptions() {
    setState(() {
      _showAdvancedOptions = !_showAdvancedOptions;
    });
    HapticFeedback.selectionClick();
  }

  /// ✨ Glassmorphic Progress Indicator
  Widget _buildGlassmorphicProgress(BuildContext context, S s) {
    final steps = [
      s.titleAndDescription,
      s.location,
      s.imageGallery,
      s.availableServices,
      s.pricing,
      s.bookingDetails,
    ];

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: context.cardColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Step indicators with glassmorphism
          Row(
            children: List.generate(steps.length, (index) {
              final isActive = index == _currentStep;
              final isCompleted = _isStepCompleted(index);
              final isVisited = _visitedSteps.contains(index);

              return Expanded(
                child: Container(
                  margin: EdgeInsets.only(right: index < steps.length - 1 ? 4 : 0),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 400),
                    height: 6,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      gradient: LinearGradient(
                        colors: isCompleted
                            ? [Colors.green, Colors.green.withValues(alpha: 0.7)]
                            : isActive
                                ? [context.accentColor, context.accentColor.withValues(alpha: 0.7)]
                                : [
                                    context.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                                    context.isDarkMode ? Colors.grey[800]! : Colors.grey[400]!,
                                  ],
                      ),
                      boxShadow: isActive ? [
                        BoxShadow(
                          color: context.accentColor.withValues(alpha: 0.4),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                  ),
                ),
              );
            }),
          ),

          const SizedBox(height: 8),

          // Current step info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Step ${_currentStep + 1} of ${steps.length}',
                style: AppTextStyles.font12Bold.copyWith(
                  color: context.accentColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${(_completionPercentage * 100).round()}%',
                  style: AppTextStyles.font10Bold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 🎨 Enhanced Body with Premium UI
  Widget _buildEnhancedBody(BuildContext context, S s) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.backgroundColor,
            context.backgroundColor.withValues(alpha: 0.8),
            context.accentColor.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.only(top: 40),
          child: PageView.builder(
            controller: PageController(initialPage: _currentStep),
            onPageChanged: (index) {
              setState(() => _currentStep = index);
              _stepAnimationController.forward().then((_) {
                _stepAnimationController.reset();
              });
            },
            itemCount: 6,
            itemBuilder: (context, index) {
              return AnimatedBuilder(
                animation: _stepAnimationController,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      50 * (1 - _stepAnimationController.value),
                      0,
                    ),
                    child: Opacity(
                      opacity: 0.3 + (0.7 * _stepAnimationController.value),
                      child: _buildStepContent(context, s, index),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// 📱 Build Step Content with Glassmorphism
  Widget _buildStepContent(BuildContext context, S s, int stepIndex) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Step Header with glassmorphism
          _buildStepHeader(context, s, stepIndex),

          const SizedBox(height: 20),

          // Step Content
          Expanded(
            child: _buildStepBody(context, s, stepIndex),
          ),

          // Navigation Controls
          _buildNavigationControls(context, s),
        ],
      ),
    );
  }

  /// 🎯 Step Header with Premium Design
  Widget _buildStepHeader(BuildContext context, S s, int stepIndex) {
    final stepTitles = [
      s.titleAndDescription,
      s.location,
      s.imageGallery,
      s.availableServices,
      s.pricing,
      s.bookingDetails,
    ];

    final stepIcons = [
      Icons.edit_note_rounded,
      Icons.location_on_rounded,
      Icons.photo_library_rounded,
      Icons.star_rounded,
      Icons.attach_money_rounded,
      Icons.policy_rounded,
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            context.cardColor.withValues(alpha: 0.9),
            context.cardColor.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          // Step Icon with glow
          AnimatedBuilder(
            animation: _glowController,
            builder: (context, child) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      context.accentColor,
                      context.accentColor.withValues(alpha: 0.7),
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: context.accentColor.withValues(
                        alpha: 0.4 + (0.2 * _glowController.value),
                      ),
                      blurRadius: 15 + (5 * _glowController.value),
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Icon(
                  stepIcons[stepIndex],
                  color: Colors.white,
                  size: 28,
                ),
              );
            },
          ),

          const SizedBox(width: 16),

          // Step Title and Description
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  stepTitles[stepIndex],
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getStepDescription(stepIndex),
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),

          // Completion Status
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _isStepCompleted(stepIndex)
                  ? Colors.green.withValues(alpha: 0.1)
                  : _visitedSteps.contains(stepIndex)
                      ? Colors.orange.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getStepIcon(stepIndex),
              color: _isStepCompleted(stepIndex)
                  ? Colors.green
                  : _visitedSteps.contains(stepIndex)
                      ? Colors.orange
                      : Colors.grey,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  /// 📝 Get Step Description
  String _getStepDescription(int stepIndex) {
    switch (stepIndex) {
      case 0: return 'Add property title, description and category';
      case 1: return 'Set the exact location of your property';
      case 2: return 'Upload beautiful photos of your property';
      case 3: return 'Select available facilities and amenities';
      case 4: return 'Set competitive pricing for your property';
      case 5: return 'Add booking details and policies';
      default: return '';
    }
  }

  /// 🎨 Build Step Body Content
  Widget _buildStepBody(BuildContext context, S s, int stepIndex) {
    switch (stepIndex) {
      case 0: return _buildTitleDescriptionStep(context, s);
      case 1: return _buildLocationStep(context, s);
      case 2: return _buildImageStep(context, s);
      case 3: return _buildFacilitiesStep(context, s);
      case 4: return _buildPricingStep(context, s);
      case 5: return _buildDetailsStep(context, s);
      default: return const SizedBox();
    }
  }

  /// 📝 Title & Description Step
  Widget _buildTitleDescriptionStep(BuildContext context, S s) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildGlassmorphicTextField(
            controller: _titleController,
            label: s.propertyTitle,
            icon: Icons.title_rounded,
            hint: 'Enter a catchy title for your property',
          ),

          const SizedBox(height: 20),

          _buildGlassmorphicTextField(
            controller: _descriptionController,
            label: s.propertyDescription,
            icon: Icons.description_rounded,
            hint: 'Describe your property in detail',
            maxLines: 4,
          ),

          const SizedBox(height: 20),

          _buildCategorySelector(context, s),
        ],
      ),
    );
  }

  /// 🌍 Location Step
  Widget _buildLocationStep(BuildContext context, S s) {
    return Column(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: context.accentColor.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(18),
              child: _location != null
                  ? FlutterMap(
                      options: MapOptions(
                        initialCenter: _location!,
                        initialZoom: 15.0,
                      ),
                      children: [
                        TileLayer(
                          urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                          subdomains: const ['a', 'b', 'c'],
                        ),
                        MarkerLayer(
                          markers: [
                            Marker(
                              point: _location!,
                              child: const Icon(
                                Icons.location_pin,
                                color: Colors.red,
                                size: 40,
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.location_on_outlined,
                            size: 80,
                            color: context.accentColor.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Tap to select location',
                            style: AppTextStyles.font16Medium.copyWith(
                              color: context.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ),
        ),

        const SizedBox(height: 20),

        _buildGlassmorphicButton(
          onPressed: _pickLocation,
          icon: Icons.my_location_rounded,
          label: _location != null ? 'Change Location' : 'Select Location',
          isPrimary: true,
        ),
      ],
    );
  }

  /// 📸 Image Step
  Widget _buildImageStep(BuildContext context, S s) {
    return Column(
      children: [
        Expanded(
          child: _imageGallery.isEmpty
              ? _buildImagePlaceholder(context)
              : _buildImageGrid(context),
        ),

        const SizedBox(height: 20),

        Row(
          children: [
            Expanded(
              child: _buildGlassmorphicButton(
                onPressed: _pickImagesFromGallery,
                icon: Icons.photo_library_rounded,
                label: 'Gallery',
                isPrimary: false,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildGlassmorphicButton(
                onPressed: _pickImageFromCamera,
                icon: Icons.camera_alt_rounded,
                label: 'Camera',
                isPrimary: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// ⭐ Facilities Step
  Widget _buildFacilitiesStep(BuildContext context, S s) {
    return Column(
      children: [
        Expanded(
          child: _isLoadingFacilities
              ? const Center(child: CircularProgressIndicator())
              : _buildFacilitiesGrid(context),
        ),
      ],
    );
  }

  /// 💰 Pricing Step
  Widget _buildPricingStep(BuildContext context, S s) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildPriceCard(
            controller: _dailyPriceController,
            label: s.dailyPrice,
            icon: Icons.today_rounded,
            includeCommission: _includeCommissionDaily,
            onCommissionChanged: (value) => setState(() => _includeCommissionDaily = value),
          ),

          const SizedBox(height: 16),

          _buildPriceCard(
            controller: _weeklyPriceController,
            label: s.weeklyPrice,
            icon: Icons.date_range_rounded,
            includeCommission: _includeCommissionWeekly,
            onCommissionChanged: (value) => setState(() => _includeCommissionWeekly = value),
          ),

          const SizedBox(height: 16),

          _buildPriceCard(
            controller: _monthlyPriceController,
            label: s.monthlyPrice,
            icon: Icons.calendar_month_rounded,
            includeCommission: _includeCommissionMonthly,
            onCommissionChanged: (value) => setState(() => _includeCommissionMonthly = value),
          ),
        ],
      ),
    );
  }

  /// 📋 Details Step
  Widget _buildDetailsStep(BuildContext context, S s) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildGlassmorphicTextField(
                  controller: _bathroomsController,
                  label: s.numberOfBathrooms,
                  icon: Icons.bathtub_rounded,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildGlassmorphicTextField(
                  controller: _bedroomsController,
                  label: s.numberOfBedrooms,
                  icon: Icons.bed_rounded,
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          _buildGlassmorphicTextField(
            controller: _guestsController,
            label: s.numberOfGuests,
            icon: Icons.people_rounded,
            keyboardType: TextInputType.number,
          ),

          const SizedBox(height: 20),

          _buildGlassmorphicTextField(
            controller: _bookingPolicyController,
            label: s.bookingPolicy,
            icon: Icons.rule_rounded,
            maxLines: 3,
          ),

          const SizedBox(height: 20),

          _buildGlassmorphicTextField(
            controller: _cancellationPolicyController,
            label: s.cancellationPolicy,
            icon: Icons.cancel_rounded,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  /// ✨ Glassmorphic Text Field
  Widget _buildGlassmorphicTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            context.cardColor.withValues(alpha: 0.8),
            context.cardColor.withValues(alpha: 0.6),
          ],
        ),
        border: Border.all(
          color: context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: TextField(
            controller: controller,
            maxLines: maxLines,
            keyboardType: keyboardType,
            style: TextStyle(color: context.primaryTextColor),
            decoration: InputDecoration(
              labelText: label,
              hintText: hint,
              labelStyle: TextStyle(color: context.accentColor),
              hintStyle: TextStyle(color: context.secondaryTextColor),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: context.accentColor, size: 20),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                vertical: 16,
                horizontal: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 🎯 Glassmorphic Button
  Widget _buildGlassmorphicButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    bool isPrimary = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isPrimary
              ? [
                  context.accentColor,
                  context.accentColor.withValues(alpha: 0.8),
                ]
              : [
                  context.cardColor.withValues(alpha: 0.8),
                  context.cardColor.withValues(alpha: 0.6),
                ],
        ),
        border: Border.all(
          color: isPrimary
              ? context.accentColor.withValues(alpha: 0.3)
              : context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isPrimary
                ? context.accentColor.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: isPrimary ? Colors.white : context.accentColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: AppTextStyles.font14Bold.copyWith(
                    color: isPrimary ? Colors.white : context.accentColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 📱 Smart FAB with Context Awareness
  Widget _buildSmartFAB(BuildContext context, S s) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Preview FAB
        if (_currentStep > 2)
          AnimatedScale(
            scale: _currentStep > 2 ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [
                    context.isDarkMode ? Colors.grey[700]! : Colors.grey[600]!,
                    context.isDarkMode ? Colors.grey[800]! : Colors.grey[700]!,
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: FloatingActionButton(
                heroTag: "preview",
                onPressed: _showPreview,
                backgroundColor: Colors.transparent,
                elevation: 0,
                child: const Icon(Icons.preview_rounded, color: Colors.white),
              ),
            ),
          ),

        // Main FAB
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                context.accentColor,
                context.accentColor.withValues(alpha: 0.8),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: context.accentColor.withValues(alpha: 0.4),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: FloatingActionButton.extended(
            heroTag: "main",
            onPressed: _handleNextStep,
            backgroundColor: Colors.transparent,
            elevation: 0,
            icon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                _currentStep == 5 ? Icons.check_circle_rounded : Icons.arrow_forward_rounded,
                key: ValueKey(_currentStep),
                color: Colors.white,
              ),
            ),
            label: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Text(
                _currentStep == 5 ? 'Submit' : 'Next',
                key: ValueKey(_currentStep),
                style: AppTextStyles.font14Bold.copyWith(color: Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 🎯 Handle Next Step with Validation
  void _handleNextStep() {
    if (!_validateCurrentStep()) {
      _showValidationErrors();
      return;
    }

    if (_currentStep < 5) {
      setState(() => _currentStep++);
      _stepAnimationController.forward().then((_) {
        _stepAnimationController.reset();
      });
      HapticFeedback.selectionClick();
    } else {
      _submitForm();
    }
  }

  /// 👁️ Show Preview
  void _showPreview() {
    // Implementation for preview
    HapticFeedback.lightImpact();
  }

  /// 📤 Submit Form
  void _submitForm() {
    _celebrationController.forward();
    // Implementation for form submission
    HapticFeedback.heavyImpact();
  }

  /// 🚨 Show Validation Errors
  void _showValidationErrors() {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: dialogContext.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            const Icon(Icons.warning_rounded, color: Colors.orange, size: 24),
            const SizedBox(width: 12),
            Text(
              'Validation Required',
              style: AppTextStyles.font18Bold.copyWith(color: dialogContext.primaryTextColor),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _validationErrors.entries.map((entry) =>
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Text(
                '• ${entry.value}',
                style: AppTextStyles.font14Regular.copyWith(color: dialogContext.primaryTextColor),
              ),
            ),
          ).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: Text('Fix Issues', style: TextStyle(color: dialogContext.accentColor)),
          ),
        ],
      ),
    );
  }

  // Placeholder methods for missing functionality
  void _loadServiceCategories() async {}
  void _loadFacilities() async {}
  void _pickLocation() {}
  void _pickImagesFromGallery() {}
  void _pickImageFromCamera() {}

  Widget _buildCategorySelector(BuildContext context, S s) => const SizedBox();
  Widget _buildImagePlaceholder(BuildContext context) => const SizedBox();
  Widget _buildImageGrid(BuildContext context) => const SizedBox();
  Widget _buildFacilitiesGrid(BuildContext context) => const SizedBox();
  Widget _buildPriceCard({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required bool includeCommission,
    required Function(bool) onCommissionChanged,
  }) => const SizedBox();
  Widget _buildNavigationControls(BuildContext context, S s) => const SizedBox();
}
