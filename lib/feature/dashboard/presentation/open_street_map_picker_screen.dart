import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OSMMapPicker extends StatefulWidget {
  final LatLng? initialPosition;

  const OSMMapPicker({super.key, this.initialPosition});

  @override
  State<OSMMapPicker> createState() => _OSMMapPickerState();
}

class _OSMMapPickerState extends State<OSMMapPicker> {
  LatLng? _selectedPosition;
  static const defaultLocation = LatLng(24.7136, 46.6753); // Riyadh

  Future<LatLng> _getInitialLocation() async {
    if (widget.initialPosition != null) {
      return widget.initialPosition!;
    }

    final prefs = await SharedPreferences.getInstance();
    final lat = prefs.getDouble('selectedLat');
    final lon = prefs.getDouble('selectedLon');

    if (lat != null && lon != null) {
      return LatLng(lat, lon);
    }

    return defaultLocation;
  }

  @override
  void initState() {
    super.initState();
    _selectedPosition = widget.initialPosition;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Pick Location")),
      body: FutureBuilder<LatLng>(
        future: _getInitialLocation(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          }

          final center = snapshot.data!;
          _selectedPosition ??= center;

          return Stack(
            children: [
              FlutterMap(
                options: MapOptions(
                  center: center,
                  zoom: 13.0,
                  onTap: (tapPosition, point) {
                    setState(() {
                      _selectedPosition = point;
                    });
                  },
                ),
                children: [
                  TileLayer(
                    urlTemplate:
                        'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                    subdomains: const ['a', 'b', 'c'],
                  ),
                  if (_selectedPosition != null)
                    MarkerLayer(
                      markers: [
                        Marker(
                          point: _selectedPosition!,
                          width: 40,
                          height: 40,
                          child: const Icon(Icons.location_pin,
                              color: Colors.red, size: 40),
                        ),
                      ],
                    ),
                ],
              ),
              if (_selectedPosition != null)
                Positioned(
                  bottom: 80,
                  left: 20,
                  right: 20,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context, _selectedPosition);
                    },
                    child: const Text("Confirm Location"),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
