import 'package:flutter/material.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/home_view_app_bar.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/home_view_body.dart';
import 'package:geolocator/geolocator.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  City? currentCity;
  List<City> cities = [];
  bool isLoading = true;
  String? errorMessage;
  final Dio _dio = Dio();
  StreamSubscription<Position>? _positionSubscription;

  // SharedPreferences keys
  final String _cityIdKey = 'selectedCityId';
  final String _latKey = 'selectedLat';
  final String _lonKey = 'selectedLon';

  // Location tracking parameters
  final int _updateDistance = 500;
  final LocationAccuracy _trackingAccuracy = LocationAccuracy.high;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  @override
  void dispose() {
    _positionSubscription?.cancel();
    super.dispose();
  }

  Future<void> _initializeLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedCityId = prefs.getInt(_cityIdKey);
      final savedLat = prefs.getDouble(_latKey);
      final savedLon = prefs.getDouble(_lonKey);

      if (savedCityId != null && savedLat != null && savedLon != null) {
        await _handleLocationUpdate(
          Position(
            latitude: savedLat,
            longitude: savedLon,
            timestamp: DateTime.now(),
            accuracy: 0,
            altitude: 0,
            heading: 0,
            speed: 0,
            speedAccuracy: 0,
            altitudeAccuracy: 0,
            headingAccuracy: 0,
          ),
          forceUpdate: false,
        );
      } else {
        await _getCurrentLocation();
      }
    } catch (e) {
      _handleError('Initialization error: ${e.toString()}');
    }
  }

  Future<bool> _checkLocationPermissions() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _handleError('Location services are disabled. Please enable them.');
      return false;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _handleError('Location permissions are denied');
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _handleError('Location permissions are permanently denied. Please enable them in app settings.');
      return false;
    }

    return true;
  }

  void _startLocationTracking() {
    _positionSubscription = Geolocator.getPositionStream(
      locationSettings: LocationSettings(
        accuracy: _trackingAccuracy,
        distanceFilter: _updateDistance,
      ),
    ).listen(
          (position) => _handleLocationUpdate(position),
      onError: (e) {
        String errorMessage;
        if (e is PermissionDeniedException) {
          errorMessage = 'Location permissions denied. Please enable in settings.';
        } else if (e is LocationServiceDisabledException) {
          errorMessage = 'Location services disabled. Please enable them.';
        } else {
          errorMessage = 'Location tracking error: ${e.toString()}';
        }
        _handleError(errorMessage);
      },
    );
  }

  Future<void> _handleLocationUpdate(Position position, {bool forceUpdate = true}) async {
    if (!mounted) return;

    setState(() => isLoading = true);

    try {
      await _fetchCities(position);

      final closestCity = cities.isNotEmpty
          ? cities.reduce((a, b) => a.distance! < b.distance! ? a : b)
          : null;

      if (closestCity != null) {
        await _saveLocationData(closestCity.id, position);
        setState(() {
          currentCity = closestCity;
          isLoading = false;
        });

        if (forceUpdate) {
          print('Location changed to: ${closestCity.name}');
        }
      } else {
        _handleError('No cities found near your location');
      }
    } catch (e) {
      _handleError('Update failed: ${e.toString()}');
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool hasPermission = await _checkLocationPermissions();
      if (!hasPermission) return;

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: _trackingAccuracy,
      );
      await _handleLocationUpdate(position);
      _startLocationTracking();
    } catch (e) {
      _handleError('Location error: ${e.toString()}');
    }
  }

  Future<void> _fetchCities(Position position) async {
    try {
      final response = await _dio.post(
        'https://backend.gatherpoint.sa/api/general/cities',
        data: {'lat': position.latitude, 'lng': position.longitude},
        options: Options(contentType: Headers.jsonContentType),
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        setState(() {
          cities = (data['cities'] as List)
              .map((json) => City.fromJson(json))
              .toList();
        });
      }
    } catch (e) {
      throw Exception('Failed to fetch cities: ${e.toString()}');
    }
  }

  Future<void> _saveLocationData(int cityId, Position position) async {
    final prefs = await SharedPreferences.getInstance();
    await Future.wait([
      prefs.setInt(_cityIdKey, cityId),
      prefs.setDouble(_latKey, position.latitude),
      prefs.setDouble(_lonKey, position.longitude),
    ]);
  }

  void _handleError(String message) {
    if (!mounted) return;
    setState(() {
      errorMessage = message;
      isLoading = false;
    });
  }

  void _onCitySelected(City city) async {
    try {
      final position = await Geolocator.getLastKnownPosition() ??
          await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.high);

      await _saveLocationData(city.id, position);
      setState(() => currentCity = city);
    } catch (e) {
      _handleError('Selection error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            if (isLoading) const LinearProgressIndicator(),
            if (errorMessage != null)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center),
                    const SizedBox(height: 20),
                    if (errorMessage!.contains('permanently denied'))
                      ElevatedButton(
                        onPressed: () async {
                          await Geolocator.openAppSettings();
                          await Future.delayed(const Duration(seconds: 1));
                          _getCurrentLocation();
                        },
                        child: const Text('Open Settings'),
                      )
                    else
                      ElevatedButton(
                        onPressed: _getCurrentLocation,
                        child: const Text('Retry'),
                      ),
                  ],
                ),
              )
            else
              Column(
                children: [
                  HomeViewAppBar(
                    selectedCity: currentCity?.name ?? 'حدد المدينة',
                    cities: cities.map((c) => c.name).toList(),
                    onCitySelected: (cityName) {
                      final city = cities.firstWhere((c) => c.name == cityName);
                      _onCitySelected(city);
                    },
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 70), // Add 70px bottom padding for navigation
                      child: HomeViewBody(currentCity: currentCity),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}