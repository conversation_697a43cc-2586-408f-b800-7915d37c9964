import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/generated/l10n.dart';

class HomeViewAppBar extends StatelessWidget {
  final String selectedCity;
  final Function(String) onCitySelected;
  final List<String> cities;

  const HomeViewAppBar({
    super.key,
    required this.selectedCity,
    required this.onCitySelected,
    required this.cities,
  });

  void _showCitySelectionDialog(BuildContext context) {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(s.selectCity),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: cities.length,
              itemBuilder: (BuildContext context, int index) {
                final city = cities[index];
                return ListTile(
                  title: Text(city),
                  onTap: () {
                    Navigator.pop(context); // Close the dialog
                    onCitySelected(city); // Update the selected city
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Image.asset(AppAssets.imagesLogoCircle, width: 35, height: 35),
          const Spacer(),
          SvgPicture.asset(AppAssets.iconsLocation),
          const Spacer(),
          GestureDetector(
            onTap: () => _showCitySelectionDialog(context),
            child: Text(
              selectedCity,
              style: AppTextStyles.font18Bold.copyWith(
                color: AppColors.yellow,
              ),
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => _showCitySelectionDialog(context),
            child: SvgPicture.asset(AppAssets.iconsArrowDropDown),
          ),
          const Spacer(flex: 2),
        ],
      ),
    );
  }
}