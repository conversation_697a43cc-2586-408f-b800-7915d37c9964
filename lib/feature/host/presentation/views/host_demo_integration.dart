import 'package:flutter/material.dart';
import 'package:gather_point/feature/host/presentation/views/host_home_page.dart';

/// Demo integration example for the Host Home Page
/// This shows how to integrate the host dashboard into your app
class HostDemoIntegration extends StatelessWidget {
  const HostDemoIntegration({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Host Dashboard Demo'),
        backgroundColor: Colors.amber,
      ),
      body: const HostHomePage(),
    );
  }
}

/// Example of how to navigate to the host dashboard
class NavigateToHostDashboard {
  static void navigateToHostDashboard(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const HostDemoIntegration(),
      ),
    );
  }
}

/// Example usage in your main app:
/// 
/// ```dart
/// ElevatedButton(
///   onPressed: () => NavigateToHostDashboard.navigateToHostDashboard(context),
///   child: Text('Open Host Dashboard'),
/// )
/// ```
/// 
/// Or if you want to use it as a tab in bottom navigation:
/// 
/// ```dart
/// BottomNavigationBar(
///   items: [
///     BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
///     BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'Host Dashboard'),
///   ],
///   onTap: (index) {
///     if (index == 1) {
///       Navigator.push(context, MaterialPageRoute(builder: (_) => HostHomePage()));
///     }
///   },
/// )
/// ```
