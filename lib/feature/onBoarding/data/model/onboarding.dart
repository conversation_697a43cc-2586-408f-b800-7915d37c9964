import 'package:gather_point/core/utils/app_assets.dart';

class OnboardingModel {
  String title;
  String subTitle;
  String description;
  String image;

  OnboardingModel({
    required this.title,
    required this.description,
    required this.subTitle,
    required this.image,
  });

  static List<OnboardingModel> onboardingItem = [
    OnboardingModel(
      title: 'لحظاتك',
      subTitle: 'الحلوه تبدأ معنا',
      description: '''حجز سهل سريع  وآمن وبدون تعقيد 
منصتنا مرخصة من وزارة السياحة 
احجز وكبر المخدة واحنا نكمل الباقي.
تفكرون تطلعو مع العائله او الاصحاب وتبحثو  على شقة مفروشة أو استراحة او شاليه بحري أو مخيم شتوي''',
      image: AppAssets.imagesOnBoarding1,
    ),
    OnboardingModel(
      title: 'حفلتك..',
      subTitle: 'على أصولها',
      description:
          'عندكم مناسبه ننسقها لكم حفلات خاصة بالعائله او بالأصدقاء او حفلات عامة نرتب لكم كل التفاصيل كل الي عليكم تختارو الي يناسبكم ومالكم إلا الرضى .',
      image: AppAssets.imagesOnBoarding2,
    ),
    OnboardingModel(
      title: 'ولائم',
      subTitle: 'تليق بضيوفك',
      description:
          'عندك عزيمة أومناسبة خاصة  ازهلها وشوف الخيارات الموجوده لدينا وتوصلك للمكان الي انت فيه وتبيض وجهك امام ضيوفك .',
      image: AppAssets.imagesOnBoarding3,
    ),
  ];
}
