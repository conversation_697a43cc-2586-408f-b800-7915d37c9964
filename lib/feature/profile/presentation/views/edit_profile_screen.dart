import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:hive/hive.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

class EditProfileScreen extends StatefulWidget {
  final UserEntity user;

  const EditProfileScreen({super.key, required this.user});

  @override
  _EditProfileScreenState createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late final DioConsumer _dioConsumer;
  late TextEditingController _nameController;
  late TextEditingController _bioController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late int _gender;
  late String _birthdate;
  File? _profileImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.user.fullName);
    _bioController = TextEditingController(text: widget.user.bio);
    _emailController = TextEditingController(text: widget.user.email);
    _phoneController = TextEditingController(text: widget.user.phone);
    _gender = widget.user.gender;
    _birthdate = widget.user.birthdate;
  }

  Future<void> _pickImage() async {
    final pickedFile =
        await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() => _profileImage = File(pickedFile.path));
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 18)),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(
          () => _birthdate = DateFormat('yyyy-MM-dd', 'en_US').format(picked));
    }
  }

  Future<void> _updateProfile() async {
    if (!_validateForm()) return;

    setState(() => _isLoading = true);

    try {
      final formData = await _createFormData();
      final response = await _dioConsumer.post(
        '/api/client/edit_profile',
        data: formData,
        isFormData: false,
      );

      if (response['status'] == 200) {
        final updatedUser = _updateLocalUser();
        Navigator.pop(context, updatedUser);
      }
    } on DioException catch (e) {
      _handleApiError(e);
    } catch (e) {
      _showError('حدث خطأ غير متوقع: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  bool _validateForm() {
    if (!_validatePhone(_phoneController.text)) {
      _showError('رقم الجوال غير صحيح');
      return false;
    }
    if (_birthdate.isEmpty) {
      _showError('يرجى اختيار تاريخ الميلاد');
      return false;
    }
    return true;
  }

  bool _validatePhone(String phone) {
    return RegExp(r'^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$')
        .hasMatch(phone);
  }

  Future<FormData> _createFormData() async {
    return FormData.fromMap({
      'full_name': _nameController.text,
      'email': _emailController.text,
      'phone': _phoneController.text,
      'gender': _gender.toString(),
      'birthdate': _birthdate,
      'bio': _bioController.text,
      if (_profileImage != null)
        'profile_picture': await MultipartFile.fromFile(_profileImage!.path),
    });
  }

  UserEntity _updateLocalUser() {
    final updatedUser = widget.user.copyWith(
      fullName: _nameController.text,
      email: _emailController.text,
      phone: _phoneController.text,
      gender: _gender,
      birthdate: _birthdate,
      bio: _bioController.text,
      image: _profileImage?.path ?? widget.user.image,
    );

    Hive.box<UserEntity>(AppConstants.kMyProfileBoxName)
      .put(AppConstants.kMyProfileKey, updatedUser);
    return updatedUser;
  }

  void _handleApiError(DioException e) {
    if (e.response?.statusCode == 422) {
      final errors = e.response?.data['errors'] as Map<String, dynamic>?;
      final messages =
          errors?.entries.map((entry) => entry.value.join('\n')).join('\n') ??
              'خطأ في التحقق من البيانات';
      _showError(messages);
    } else {
      _showError('حدث خطأ في الخادم: ${e.message}');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text(message, style: AppTextStyles.font14Regular.copyWith(color: Colors.white))));
  }

  @override
  void dispose() {
    _nameController.dispose();
    _bioController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: 'تعديل الملف الشخصي',
      hasBottomNavigation: false,
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                context.accentColor,
                context.accentColor.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: _isLoading
              ? Container(
                  padding: const EdgeInsets.all(12),
                  child: const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                )
              : IconButton(
                  icon: const Icon(
                    Icons.save_rounded,
                    color: Colors.white,
                  ),
                  onPressed: _updateProfile,
                  tooltip: 'حفظ التغييرات',
                ),
        ),
      ],
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Enhanced Profile Image Section
            EnhancedCard(
              child: Column(
                children: [
                  Text(
                    'الصورة الشخصية',
                    style: AppTextStyles.font16Bold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _ProfileImagePicker(
                    image: _profileImage?.path ?? widget.user.image,
                    onPressed: _pickImage,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'اضغط لتغيير الصورة',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Personal Information Section
            EnhancedCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: context.accentColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.person_rounded,
                          color: context.accentColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'المعلومات الشخصية',
                        style: AppTextStyles.font16Bold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  _EditFormField(
                    controller: _nameController,
                    label: 'الاسم الكامل',
                    icon: Icons.person_outline_rounded,
                  ),
                  _EditFormField(
                    controller: _bioController,
                    label: 'الوصف',
                    maxLines: 3,
                    icon: Icons.description_outlined,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Contact Information Section
            EnhancedCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.contact_mail_rounded,
                          color: Colors.blue,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'معلومات التواصل',
                        style: AppTextStyles.font16Bold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  _EditFormField(
                    controller: _emailController,
                    label: 'البريد الإلكتروني',
                    icon: Icons.email_outlined,
                  ),
                  _EditFormField(
                    controller: _phoneController,
                    label: 'رقم الجوال',
                    icon: Icons.phone_outlined,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Additional Information Section
            EnhancedCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.purple.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.info_outline_rounded,
                          color: Colors.purple,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'معلومات إضافية',
                        style: AppTextStyles.font16Bold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  _GenderDropdown(
                    value: _gender,
                    onChanged: (value) => setState(() => _gender = value!),
                  ),
                  const SizedBox(height: 16),
                  _BirthdatePicker(
                    date: _birthdate,
                    onPressed: () => _selectDate(context),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

class _ProfileImagePicker extends StatelessWidget {
  final String? image;
  final VoidCallback onPressed;

  const _ProfileImagePicker({
    required this.image,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: CircleAvatar(
        radius: 50,
        backgroundImage: _getImageProvider(),
        child: image == null ? const Icon(Icons.camera_alt, size: 40) : null,
      ),
    );
  }

  ImageProvider? _getImageProvider() {
    if (image == null) return null;
    if (image!.startsWith('http')) return NetworkImage(image!);
    return FileImage(File(image!));
  }
}

class _EditFormField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final int maxLines;
  final IconData? icon;

  const _EditFormField({
    required this.controller,
    required this.label,
    this.maxLines = 1,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: EnhancedTextField(
        controller: controller,
        hintText: label,
        prefixIcon: icon != null ? Icon(icon, color: context.accentColor) : null,
        maxLines: maxLines,
      ),
    );
  }
}

class _GenderDropdown extends StatelessWidget {
  final int value;
  final ValueChanged<int?> onChanged;

  const _GenderDropdown({
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.secondaryTextColor.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.person_outline_rounded,
            color: context.accentColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الجنس',
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                DropdownButton<int>(
                  value: value,
                  isExpanded: true,
                  underline: const SizedBox(),
                  icon: Icon(
                    Icons.keyboard_arrow_down_rounded,
                    color: context.secondaryTextColor,
                  ),
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  dropdownColor: context.cardColor,
                  items: [
                    DropdownMenuItem(
                      value: 0,
                      child: Text(
                        'غير محدد',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ),
                    DropdownMenuItem(
                      value: 1,
                      child: Text(
                        'ذكر',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ),
                    DropdownMenuItem(
                      value: 2,
                      child: Text(
                        'أنثي',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ),
                  ],
                  onChanged: onChanged,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _BirthdatePicker extends StatelessWidget {
  final String date;
  final VoidCallback onPressed;

  const _BirthdatePicker({
    required this.date,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: context.cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: context.secondaryTextColor.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today_rounded,
              color: context.accentColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تاريخ الميلاد',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    date.isEmpty ? 'اختر تاريخ الميلاد' : date,
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: date.isEmpty
                          ? context.secondaryTextColor
                          : context.primaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: context.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
