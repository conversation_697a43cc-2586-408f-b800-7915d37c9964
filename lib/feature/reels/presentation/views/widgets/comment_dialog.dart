import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/reels/domain/entities/comment_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timeago/timeago.dart' as timeago;

class CommentDialog extends StatefulWidget {
  final int videoId;
  final List<CommentEntity> comments;
  final Function(String) onCommentPost;
  final Function(int) onCommentLike;
  final Function(int) onCommentDelete;

  const CommentDialog({
    super.key,
    required this.videoId,
    required this.comments,
    required this.onCommentPost,
    required this.onCommentLike,
    required this.onCommentDelete,
  });

  @override
  State<CommentDialog> createState() => _CommentDialogState();
}

class _CommentDialogState extends State<CommentDialog> {
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isPosting = false;

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _postComment() async {
    if (_commentController.text.trim().isEmpty) return;

    setState(() => _isPosting = true);
    
    try {
      await widget.onCommentPost(_commentController.text.trim());
      _commentController.clear();
      
      // Scroll to bottom to show new comment
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).commentFailed),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isPosting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isRTL = context.read<LocaleCubit>().isArabic();
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkGrey : AppColors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: isDark ? AppColors.darkGrey2 : AppColors.lightGrey2,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.comment_rounded,
                  color: AppColors.yellow,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    S.of(context).comments,
                    style: AppTextStyles.font18Bold.copyWith(
                      color: ThemeHelper.getPrimaryTextColor(context),
                    ),
                  ),
                ),
                Text(
                  '${widget.comments.length}',
                  style: AppTextStyles.font14Medium.copyWith(
                    color: ThemeHelper.getSecondaryTextColor(context),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close_rounded,
                    color: ThemeHelper.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
          
          // Comments List
          Expanded(
            child: widget.comments.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: widget.comments.length,
                    itemBuilder: (context, index) {
                      return _buildCommentItem(widget.comments[index], isRTL);
                    },
                  ),
          ),
          
          // Comment Input
          _buildCommentInput(isDark, isRTL),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.comment_outlined,
            size: 64,
            color: ThemeHelper.getSecondaryTextColor(context),
          ),
          const SizedBox(height: 16),
          Text(
            S.of(context).noComments,
            style: AppTextStyles.font16Regular.copyWith(
              color: ThemeHelper.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentItem(CommentEntity comment, bool isRTL) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
        children: [
          // Avatar
          CircleAvatar(
            radius: 18,
            backgroundColor: AppColors.yellow,
            backgroundImage: comment.userAvatar.isNotEmpty
                ? NetworkImage(comment.userAvatar)
                : null,
            child: comment.userAvatar.isEmpty
                ? Text(
                    comment.userName.isNotEmpty 
                        ? comment.userName[0].toUpperCase()
                        : '?',
                    style: AppTextStyles.font14Bold.copyWith(
                      color: AppColors.black,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 12),
          
          // Comment Content
          Expanded(
            child: Column(
              crossAxisAlignment: isRTL 
                  ? CrossAxisAlignment.end 
                  : CrossAxisAlignment.start,
              children: [
                // Username and time
                Row(
                  children: [
                    Text(
                      comment.userName,
                      style: AppTextStyles.font14SemiBold.copyWith(
                        color: ThemeHelper.getPrimaryTextColor(context),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      timeago.format(comment.createdAt),
                      style: AppTextStyles.font12Regular.copyWith(
                        color: ThemeHelper.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                
                // Comment text
                Text(
                  comment.content,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: ThemeHelper.getPrimaryTextColor(context),
                  ),
                  textAlign: isRTL ? TextAlign.right : TextAlign.left,
                ),
                const SizedBox(height: 8),
                
                // Actions
                Row(
                  children: [
                    InkWell(
                      onTap: () => widget.onCommentLike(comment.id),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            comment.isLiked 
                                ? Icons.favorite 
                                : Icons.favorite_border,
                            size: 16,
                            color: comment.isLiked 
                                ? AppColors.red 
                                : ThemeHelper.getSecondaryTextColor(context),
                          ),
                          if (comment.likesCount > 0) ...[
                            const SizedBox(width: 4),
                            Text(
                              '${comment.likesCount}',
                              style: AppTextStyles.font12Regular.copyWith(
                                color: ThemeHelper.getSecondaryTextColor(context),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput(bool isDark, bool isRTL) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkGrey2 : AppColors.lightGrey6,
        border: Border(
          top: BorderSide(
            color: isDark ? AppColors.darkGrey : AppColors.lightGrey2,
            width: 1,
          ),
        ),
      ),
      child: Row(
        textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
              style: AppTextStyles.font14Regular.copyWith(
                color: ThemeHelper.getPrimaryTextColor(context),
              ),
              decoration: InputDecoration(
                hintText: S.of(context).writeComment,
                hintStyle: AppTextStyles.font14Regular.copyWith(
                  color: ThemeHelper.getSecondaryTextColor(context),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: isDark ? AppColors.darkGrey : AppColors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          const SizedBox(width: 12),
          
          // Send Button
          Container(
            decoration: BoxDecoration(
              color: AppColors.yellow,
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              onPressed: _isPosting ? null : _postComment,
              icon: _isPosting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.black),
                      ),
                    )
                  : Icon(
                      isRTL ? Icons.send : Icons.send,
                      color: AppColors.black,
                      size: 20,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
