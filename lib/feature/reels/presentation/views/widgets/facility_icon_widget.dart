import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class FacilityIcon extends StatelessWidget {
  final String iconUrl;
  final String title;
  final double iconSize;

  const FacilityIcon({
    super.key,
    required this.iconUrl,
    required this.title,
    this.iconSize = 30,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CachedNetworkImage(
          imageUrl: iconUrl,
          width: iconSize,
          height: iconSize,
          color: Colors.white,
          errorWidget: (context, url, error) =>
              Icon(Icons.error, size: iconSize, color: Colors.white),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(color: Colors.white, fontSize: 18,fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}
