// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBuFjd32g5ANbnj5gbp_K76pHokIREfPvI',
    appId: '1:165986387816:web:a2d9a58ce374687534b8dd',
    messagingSenderId: '165986387816',
    projectId: 'gather-point-qzho1h',
    authDomain: 'gather-point-qzho1h.firebaseapp.com',
    storageBucket: 'gather-point-qzho1h.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBgSL_LUP0Htl6KfzF4o51IJBw23bAjJ5k',
    appId: '1:165986387816:android:c406db278fa2b81434b8dd',
    messagingSenderId: '165986387816',
    projectId: 'gather-point-qzho1h',
    storageBucket: 'gather-point-qzho1h.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDBhzt5GkY4AS0c_nTyy76nJdj3mRgUXKs',
    appId: '1:165986387816:ios:00887f51fe6fae0f34b8dd',
    messagingSenderId: '165986387816',
    projectId: 'gather-point-qzho1h',
    storageBucket: 'gather-point-qzho1h.firebasestorage.app',
    androidClientId: '165986387816-vhkl4fn6cqscfj6dgf9h3b4pa811bleh.apps.googleusercontent.com',
    iosClientId: '165986387816-b9ablmjfa0a6cra1mcpsi1vrecmr86ch.apps.googleusercontent.com',
    iosBundleId: 'sa.gatherpoint.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDBhzt5GkY4AS0c_nTyy76nJdj3mRgUXKs',
    appId: '1:165986387816:ios:00887f51fe6fae0f34b8dd',
    messagingSenderId: '165986387816',
    projectId: 'gather-point-qzho1h',
    storageBucket: 'gather-point-qzho1h.firebasestorage.app',
    androidClientId: '165986387816-vhkl4fn6cqscfj6dgf9h3b4pa811bleh.apps.googleusercontent.com',
    iosClientId: '165986387816-b9ablmjfa0a6cra1mcpsi1vrecmr86ch.apps.googleusercontent.com',
    iosBundleId: 'sa.gatherpoint.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBuFjd32g5ANbnj5gbp_K76pHokIREfPvI',
    appId: '1:165986387816:web:a2d9a58ce374687534b8dd',
    messagingSenderId: '165986387816',
    projectId: 'gather-point-qzho1h',
    authDomain: 'gather-point-qzho1h.firebaseapp.com',
    storageBucket: 'gather-point-qzho1h.firebasestorage.app',
  );

}