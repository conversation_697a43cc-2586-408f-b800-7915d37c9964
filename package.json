{"name": "Vue<PERSON>", "version": "1.0.0", "private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "dependencies": {"@fortawesome/fontawesome-free": "~6.2.0", "@fullcalendar/core": "~6.0.0", "@fullcalendar/daygrid": "~6.0.0", "@fullcalendar/interaction": "~6.0.0", "@fullcalendar/list": "~6.0.0", "@fullcalendar/timegrid": "~6.0.0", "@fullcalendar/timeline": "~6.0.0", "@popperjs/core": "^2.11.6", "@simonwep/pickr": "~1.8.2", "@yaireo/tagify": "~4.16.4", "alpinejs": "^3.9.0", "animate.css": "~4.1.1", "aos": "~2.3.4", "apexcharts-clevision": "^3.28.5", "autosize": "~5.0.1", "block-ui": "~2.70.1", "bloodhound-js": "~1.2.3", "bootstrap": "~5.2.3", "bootstrap-datepicker": "~1.9.0", "bootstrap-daterangepicker": "~3.1.0", "bootstrap-maxlength": "~1.10.1", "bootstrap-select": "snapappointments/bootstrap-select#main", "bs-stepper": "~1.7.0", "chart.js": "~3.9.1", "cleave.js": "~1.6.0", "clipboard": "~2.0.11", "datatables.net-bs5": "~1.13.1", "datatables.net-buttons": "~2.3.3", "datatables.net-buttons-bs5": "~2.3.3", "datatables.net-fixedcolumns-bs5": "~4.2.1", "datatables.net-fixedheader-bs5": "~3.3.1", "datatables.net-responsive": "~2.4.0", "datatables.net-responsive-bs5": "~2.4.0", "datatables.net-rowgroup-bs5": "~1.3.0", "datatables.net-select-bs5": "~1.5.0", "dropzone": "~5.9.3", "flag-icons": "^6.6.6", "flatpickr": "~4.6.13", "hammerjs": "~2.0.8", "highlight.js": "~11.6.0", "i18next": "~21.9.2", "i18next-browser-languagedetector": "~6.1.5", "i18next-http-backend": "~1.4.4", "jkanban": "~1.3.1", "jquery": "~3.6.1", "jquery-datatables-checkboxes": "~1.2.13", "jquery-sticky": "~1.0.4", "jquery.repeater": "~1.2.1", "jstree": "~3.3.12", "jszip": "~3.10.1", "katex": "~0.16.2", "leaflet": "~1.9.0", "masonry-layout": "~4.2.2", "moment": "~2.29.4", "node-waves": "~0.7.6", "nouislider": "~15.6.1", "numeral": "~2.0.6", "pdfmake": "~0.2.5", "perfect-scrollbar": "~1.5.5", "plyr": "^3.7.2", "quill": "~1.3.7", "rateyo": "~2.3.5", "sass": "1.55.0", "select2": "~4.0.13", "shepherd.js": "~10.0.1", "sortablejs": "~1.15.0", "spinkit": "~2.0.1", "sweetalert2": "~11.4.33", "swiper": "~8.4.2", "timepicker": "~1.13.18", "toastr": "~2.1.4", "typeahead.js": "~0.11.1"}, "devDependencies": {"babel-loader": "^8.2.4", "@babel/core": "7.15.8", "@babel/plugin-proposal-object-rest-spread": "7.15.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-runtime": "7.15.8", "@babel/preset-env": "7.15.8", "@prettier/plugin-php": "0.19.1", "axios": "^0.27.2", "browser-sync": "^2.27.10", "browser-sync-webpack-plugin": "2.3.0", "cross-env": "^7.0.3", "laravel-mix": "^6.0.49", "lodash": "^4.17.21", "postcss": "^8.4.16", "prettier": "2.7.1", "resolve-url-loader": "5.0.0", "sass-loader": "13.0.2"}, "overrides": {"autoprefixer": "10.4.5", "webpack": "5.60.0", "webpack-cli": "4.9.1", "prop-types": "15.7.2"}, "resolutions": {"autoprefixer": "10.4.5", "webpack": "5.60.0", "webpack-cli": "4.9.1", "prop-types": "15.7.2"}, "browserslist": [">= 1%", "last 2 versions", "not dead", "Chrome >= 45", "Firefox >= 38", "Edge >= 12", "Explorer >= 10", "iOS >= 9", "Safari >= 9", "Android >= 4.4", "Opera >= 30"], "babel": {"presets": [["@babel/env", {"targets": {"browsers": [">= 1%", "last 2 versions", "not dead", "Chrome >= 45", "Firefox >= 38", "Edge >= 12", "Explorer >= 10", "iOS >= 9", "Safari >= 9", "Android >= 4.4", "Opera >= 30"]}}]]}}