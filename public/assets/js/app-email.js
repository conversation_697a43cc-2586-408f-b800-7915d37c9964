"use strict";document.addEventListener("DOMContentLoaded",(function(){!function(){const e=document.querySelector(".email-list"),t=[].slice.call(document.querySelectorAll(".email-list-item")),l=[].slice.call(document.querySelectorAll(".email-list-item-input")),a=document.querySelector(".app-email-view-content"),r=document.querySelector(".email-filters"),i=[].slice.call(document.querySelectorAll(".email-filter-folders li")),o=document.querySelector(".email-editor"),s=document.querySelector(".app-email-sidebar"),c=document.querySelector(".app-overlay"),d=document.querySelector(".email-reply-editor"),n=[].slice.call(document.querySelectorAll(".email-list-item-bookmark")),m=document.getElementById("email-select-all"),u=document.querySelector(".email-search-input"),p=document.querySelector(".email-compose-toggle-cc"),v=document.querySelector(".email-compose-toggle-bcc"),g=document.querySelector(".app-email-compose"),h=document.querySelector(".email-list-delete"),S=document.querySelector(".email-list-read"),y=document.querySelector(".email-refresh"),f=document.getElementById("app-email-view"),k=[].slice.call(document.querySelectorAll(".email-filter-folders li")),L=[].slice.call(document.querySelectorAll(".email-list-item-actions li"));if(e){new PerfectScrollbar(e,{wheelPropagation:!1,suppressScrollX:!0})}if(r&&new PerfectScrollbar(r,{wheelPropagation:!1,suppressScrollX:!0}),a&&new PerfectScrollbar(a,{wheelPropagation:!1,suppressScrollX:!0}),o&&new Quill(".email-editor",{modules:{toolbar:".email-editor-toolbar"},placeholder:"Write your message... ",theme:"snow"}),d&&new Quill(".email-reply-editor",{modules:{toolbar:".email-reply-toolbar"},placeholder:"Write your message... ",theme:"snow"}),n&&n.forEach((e=>{e.addEventListener("click",(e=>{let t=e.currentTarget.parentNode.parentNode,l=t.getAttribute("data-starred");e.stopPropagation(),l?t.removeAttribute("data-starred"):t.setAttribute("data-starred","true")}))})),m&&m.addEventListener("click",(e=>{e.currentTarget.checked?l.forEach((e=>e.checked=1)):l.forEach((e=>e.checked=0))})),l&&l.forEach((e=>{e.addEventListener("click",(e=>{e.stopPropagation();let t=0;l.forEach((e=>{e.checked&&t++})),t<l.length?m.indeterminate=0!=t:t==l.length?(m.indeterminate=!1,m.checked=!0):m.indeterminate=!1}))})),u&&u.addEventListener("keyup",(e=>{let t=e.currentTarget.value.toLowerCase(),l={},a=document.querySelector(".email-filter-folders .active").getAttribute("data-target");l="inbox"!=a?[].slice.call(document.querySelectorAll(".email-list-item[data-"+a+'="true"]')):[].slice.call(document.querySelectorAll(".email-list-item")),l.forEach((e=>{let l=e.textContent.toLowerCase();t?-1<l.indexOf(t)?e.classList.add("d-block"):e.classList.add("d-none"):e.classList.remove("d-none")}))})),i.forEach((e=>{e.addEventListener("click",(e=>{let l=e.currentTarget,a=l.getAttribute("data-target");s.classList.remove("show"),c.classList.remove("show"),Helpers._removeClass("active",i),l.classList.add("active"),t.forEach((e=>{"inbox"==a||e.hasAttribute("data-"+a)?(e.classList.add("d-block"),e.classList.remove("d-none")):(e.classList.add("d-none"),e.classList.remove("d-block"))}))}))})),v&&v.addEventListener("click",(e=>{Helpers._toggleClass(document.querySelector(".email-compose-bcc"),"d-block","d-none")})),p&&p.addEventListener("click",(e=>{Helpers._toggleClass(document.querySelector(".email-compose-cc"),"d-block","d-none")})),g.addEventListener("hidden.bs.modal",(e=>{document.querySelector(".email-editor .ql-editor").innerHTML="",$("#emailContacts").val(""),E()})),h&&h.addEventListener("click",(e=>{l.forEach((e=>{e.checked&&e.parentNode.closest("li.email-list-item").remove()})),m.indeterminate=!1,m.checked=!1})),S&&S.addEventListener("click",(e=>{l.forEach((e=>{if(e.checked){e.checked=!1,e.parentNode.closest("li.email-list-item").classList.add("email-marked-read");let t=e.parentNode.closest("li.email-list-item").querySelector(".email-list-item-actions li");Helpers._hasClass("email-read",t)&&(t.classList.remove("email-read"),t.classList.add("email-unread"),t.querySelector("i").classList.remove("ti-mail-opened"),t.querySelector("i").classList.add("ti-mail"))}})),m.indeterminate=!1,m.checked=!1})),y&&e){let t=$(".email-list"),l=new PerfectScrollbar(e,{wheelPropagation:!1,suppressScrollX:!0});y.addEventListener("click",(e=>{t.block({message:'<div class="spinner-border text-primary" role="status"></div>',timeout:1e3,css:{backgroundColor:"transparent",border:"0"},overlayCSS:{backgroundColor:"#000",opacity:.1},onBlock:function(){l.settings.suppressScrollY=!0},onUnblock:function(){l.settings.suppressScrollY=!1}})}))}let b=$(".email-earlier-msgs");b.length&&b.on("click",(function(){let e=$(this);e.parents().find(".email-card-last").addClass("hide-pseudo"),e.next(".email-card-prev").slideToggle(),e.remove()}));let q=$("#emailContacts");function E(){if(q.length){function e(e){if(!e.id)return e.text;return"<div class='d-flex flex-wrap align-items-center'><div class='avatar avatar-xs me-2'><img src='"+assetsPath+"img/avatars/"+$(e.element).data("avatar")+"' alt='avatar' class='rounded-circle' /></div>"+e.text+"</div>"}q.wrap('<div class="position-relative"></div>').select2({placeholder:"Select value",dropdownParent:q.parent(),closeOnSelect:!1,templateResult:e,templateSelection:e,escapeMarkup:function(e){return e}})}}E();let w=$(".app-email-view-content");w.find(".scroll-to-reply").on("click",(function(){0===w[0].scrollTop&&w.animate({scrollTop:w[0].scrollHeight},1500)})),k&&k.forEach((e=>{e.addEventListener("click",(e=>{f.classList.remove("show")}))})),L&&L.forEach((e=>{e.addEventListener("click",(e=>{e.stopPropagation();let t=e.currentTarget;Helpers._hasClass("email-delete",t)?t.parentNode.closest("li.email-list-item").remove():Helpers._hasClass("email-read",t)?(t.parentNode.closest("li.email-list-item").classList.add("email-marked-read"),Helpers._toggleClass(t,"email-read","email-unread"),Helpers._toggleClass(t.querySelector("i"),"ti-mail-opened","ti-mail")):Helpers._hasClass("email-unread",t)&&(t.parentNode.closest("li.email-list-item").classList.remove("email-marked-read"),Helpers._toggleClass(t,"email-read","email-unread"),Helpers._toggleClass(t.querySelector("i"),"ti-mail-opened","ti-mail"))}))}))}()}));
