"use strict";$((function(){var e=$("#document-Status"),n=$("#document-Pause"),t=$("#document-Resume"),r=$("#document-Elapsed"),i=$("#document-Destroy"),a=$("#document-Init");if(e.length){$(document).on("idle.idleTimer",(function(n,t,r){e.val((function(e,n){return n+"Idle @ "+moment().format()+" \n"})).removeClass("alert-success").addClass("alert-warning")})),$(document).on("active.idleTimer",(function(n,t,r,i){e.val((function(e,n){return n+"Active ["+i.type+"] ["+i.target.nodeName+"] @ "+moment().format()+" \n"})).addClass("alert-success").removeClass("alert-warning")})),n.on("click",(function(){return $(document).idleTimer("pause"),e.val((function(e,n){return n+"Paused @ "+moment().format()+" \n"})),$(this).blur(),!1})),t.on("click",(function(){return $(document).idleTimer("resume"),e.val((function(e,n){return n+"Resumed @ "+moment().format()+" \n"})),$(this).blur(),!1})),r.on("click",(function(){return e.val((function(e,n){return n+"Elapsed (since becoming active): "+$(document).idleTimer("getElapsedTime")+" \n"})),$(this).blur(),!1})),i.on("click",(function(){return $(document).idleTimer("destroy"),e.val((function(e,n){return n+"Destroyed: @ "+moment().format()+" \n"})).removeClass("alert-success").removeClass("alert-warning"),$(this).blur(),!1})),a.on("click",(function(){return $(document).idleTimer({timeout:5e3}),e.val((function(e,n){return n+"Init: @ "+moment().format()+" \n"})),$(document).idleTimer("isIdle")?e.removeClass("alert-success").addClass("alert-warning"):e.addClass("alert-success").removeClass("alert-warning"),$(this).blur(),!1})),e.val(""),$(document).idleTimer(5e3),$(document).idleTimer("isIdle")?e.val((function(e,n){return n+"Initial Idle State @ "+moment().format()+" \n"})).removeClass("alert-success").addClass("alert-warning"):e.val((function(e,n){return n+"Initial Active State @ "+moment().format()+" \n"})).addClass("alert-success").removeClass("alert-warning")}var l=$("#element-Status"),s=$("#element-Reset"),o=$("#element-Remaining"),m=$("#element-LastActive"),u=$("#element-State");if(l.length){l.on("idle.idleTimer",(function(e,n,t){e.stopPropagation(),l.val((function(e,n){return n+"Idle @ "+moment().format()+" \n"})).removeClass("alert-success").addClass("alert-warning")})),l.on("active.idleTimer",(function(e){e.stopPropagation(),l.val((function(e,n){return n+"Active @ "+moment().format()+" \n"})).addClass("alert-success").removeClass("alert-warning")})),s.on("click",(function(){return l.idleTimer("reset").val((function(e,n){return n+"Reset @ "+moment().format()+" \n"})),$("#element-Status").idleTimer("isIdle")?l.removeClass("alert-success").addClass("alert-warning"):l.addClass("alert-success").removeClass("alert-warning"),$(this).blur(),!1})),o.on("click",(function(){return l.val((function(e,n){return n+"Remaining: "+l.idleTimer("getRemainingTime")+" \n"})),$(this).blur(),!1})),m.on("click",(function(){return l.val((function(e,n){return n+"LastActive: "+l.idleTimer("getLastActiveTime")+" \n"})),$(this).blur(),!1})),u.on("click",(function(){return l.val((function(e,n){return n+"State: "+($("#element-Status").idleTimer("isIdle")?"idle":"active")+" \n"})),$(this).blur(),!1})),l.val("").idleTimer(3e3),l.idleTimer("isIdle")?l.val((function(e,n){return n+"Initial Idle @ "+moment().format()+" \n"})).removeClass("alert-success").addClass("alert-warning"):l.val((function(e,n){return n+"Initial Active @ "+moment().format()+" \n"})).addClass("alert-success").removeClass("alert-warning")}}));
