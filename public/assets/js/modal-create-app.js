"use strict";$((function(){const e=document.getElementById("createApp"),t=document.querySelector(".app-credit-card-mask"),n=document.querySelector(".app-expiry-date-mask"),c=document.querySelector(".app-cvv-code-mask");let r;function a(){t&&(r=new Cleave(t,{creditCard:!0,onCreditCardTypeChanged:function(e){document.querySelector(".app-card-type").innerHTML=""!=e&&"unknown"!=e?'<img src="'+assetsPath+"img/icons/payments/"+e+'-cc.png" class="cc-icon-image" height="28"/>':""}}))}n&&new Cleave(n,{date:!0,delimiter:"/",datePattern:["m","y"]}),c&&new Cleave(c,{numeral:!0,numeralPositiveOnly:!0}),e.addEventListener("show.bs.modal",(function(e){const t=document.querySelector("#wizard-create-app");if(void 0!==typeof t&&null!==t){const e=[].slice.call(t.querySelectorAll(".btn-next")),n=[].slice.call(t.querySelectorAll(".btn-prev")),c=t.querySelector(".btn-submit"),r=new Stepper(t,{linear:!1});e&&e.forEach((e=>{e.addEventListener("click",(e=>{r.next(),a()}))})),n&&n.forEach((e=>{e.addEventListener("click",(e=>{r.previous(),a()}))})),c&&c.addEventListener("click",(e=>{alert("Submitted..!!")}))}}))}));
