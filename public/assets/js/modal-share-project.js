"use strict";$((function(){const e=$(".share-project-select");var t=document.getElementById("shareProject");t.addEventListener("show.bs.modal",(function(a){if(e.length){function s(e){return e.id?'<div class="d-flex align-items-center"><div class="avatar avatar-xs me-2 d-flex"><img src="'+assetsPath+$(e.element).data("image")+'" class="rounded-circle"></div><div class="name">'+$(e.element).data("name")+"</div></div>":e.text}e.wrap('<div class="position-relative"></div>').select2({dropdownParent:t,templateResult:s,templateSelection:s,placeholder:"Add Project Members",escapeMarkup:function(e){return e}})}}))}));
