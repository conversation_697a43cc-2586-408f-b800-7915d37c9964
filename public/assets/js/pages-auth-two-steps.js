"use strict";document.addEventListener("DOMContentLoaded",(function(e){!function(){let e=document.querySelector(".numeral-mask-wrapper");for(let t of e.children)t.onkeyup=function(e){t.nextElementSibling&&this.value.length===parseInt(this.attributes.maxlength.value)&&t.nextElementSibling.focus(),t.previousElementSibling&&(8!==e.keyCode&&46!==e.keyCode||t.previousElementSibling.focus())};const t=document.querySelector("#twoStepsForm");if(t){FormValidation.formValidation(t,{fields:{otp:{validators:{notEmpty:{message:"Please enter otp"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".mb-3"}),submitButton:new FormValidation.plugins.SubmitButton,defaultSubmit:new FormValidation.plugins.DefaultSubmit,autoFocus:new FormValidation.plugins.AutoFocus}});const e=t.querySelectorAll(".numeral-mask"),o=function(){let o=!0,n="";e.forEach((e=>{""===e.value&&(o=!1,t.querySelector('[name="otp"]').value=""),n+=e.value})),o&&(t.querySelector('[name="otp"]').value=n)};e.forEach((e=>{e.addEventListener("keyup",o)}))}}()}));
