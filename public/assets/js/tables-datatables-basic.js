"use strict";let fv,offCanvasEl;document.addEventListener("DOMContentLoaded",(function(e){!function(){const e=document.getElementById("form-add-new-record");setTimeout((()=>{const e=document.querySelector(".create-new"),t=document.querySelector("#add-new-record");e&&e.addEventListener("click",(function(){offCanvasEl=new bootstrap.Offcanvas(t),t.querySelector(".dt-full-name").value="",t.querySelector(".dt-post").value="",t.querySelector(".dt-email").value="",t.querySelector(".dt-date").value="",t.querySelector(".dt-salary").value="",offCanvasEl.show()}))}),200),fv=FormValidation.formValidation(e,{fields:{basicFullname:{validators:{notEmpty:{message:"The name is required"}}},basicPost:{validators:{notEmpty:{message:"Post field is required"}}},basicEmail:{validators:{notEmpty:{message:"The Email is required"},emailAddress:{message:"The value is not a valid email address"}}},basicDate:{validators:{notEmpty:{message:"Joining Date is required"},date:{format:"MM/DD/YYYY",message:"The value is not a valid date"}}},basicSalary:{validators:{notEmpty:{message:"Basic Salary is required"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-12"}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus},init:e=>{e.on("plugins.message.placed",(function(e){e.element.parentElement.classList.contains("input-group")&&e.element.parentElement.insertAdjacentElement("afterend",e.messageElement)}))}}),flatpickr(e.querySelector('[name="basicDate"]'),{enableTime:!1,dateFormat:"m/d/Y",onChange:function(){fv.revalidateField("basicDate")}})}()})),$((function(){var e,t=$(".datatables-basic"),a=$(".dt-complex-header"),s=$(".dt-row-grouping"),n=$(".dt-multilingual");t.length&&(e=t.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:""},{data:"id"},{data:"id"},{data:"full_name"},{data:"email"},{data:"start_date"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{className:"control",orderable:!1,searchable:!1,responsivePriority:2,targets:0,render:function(e,t,a,s){return""}},{targets:1,orderable:!1,searchable:!1,responsivePriority:3,checkboxes:!0,render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input">'},checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'}},{targets:2,searchable:!1,visible:!1},{targets:3,responsivePriority:4,render:function(e,t,a,s){var n=a.avatar,r=a.full_name,l=a.post;if(n)var i='<img src="'+assetsPath+"img/avatars/"+n+'" alt="Avatar" class="rounded-circle">';else{var o=["success","danger","warning","info","primary","secondary"][Math.floor(6*Math.random())],d=(r=a.full_name).match(/\b\w/g)||[];i='<span class="avatar-initial rounded-circle bg-label-'+o+'">'+(d=((d.shift()||"")+(d.pop()||"")).toUpperCase())+"</span>"}return'<div class="d-flex justify-content-start align-items-center user-name"><div class="avatar-wrapper"><div class="avatar me-2">'+i+'</div></div><div class="d-flex flex-column"><span class="emp_name text-truncate">'+r+'</span><small class="emp_post text-truncate text-muted">'+l+"</small></div></div>"}},{responsivePriority:1,targets:4},{targets:-2,render:function(e,t,a,s){var n=a.status,r={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return void 0===r[n]?e:'<span class="badge '+r[n].class+'">'+r[n].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,searchable:!1,render:function(e,t,a,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="text-primary ti ti-dots-vertical"></i></a><ul class="dropdown-menu dropdown-menu-end m-0"><li><a href="javascript:;" class="dropdown-item">Details</a></li><li><a href="javascript:;" class="dropdown-item">Archive</a></li><div class="dropdown-divider"></div><li><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></li></ul></div><a href="javascript:;" class="btn btn-sm btn-icon item-edit"><i class="text-primary ti ti-pencil"></i></a>'}}],order:[[2,"desc"]],dom:'<"card-header flex-column flex-md-row"<"head-label text-center"><"dt-action-buttons text-end pt-3 pt-md-0"B>><"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:7,lengthMenu:[7,10,25,50,75,100],buttons:[{extend:"collection",className:"btn btn-label-primary dropdown-toggle me-2",text:'<i class="ti ti-file-export me-sm-1"></i> <span class="d-none d-sm-inline-block">Export</span>',buttons:[{extend:"print",text:'<i class="ti ti-printer me-1" ></i>Print',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(e,t,a){if(e.length<=0)return e;var s=$.parseHTML(e),n="";return $.each(s,(function(e,t){void 0!==t.classList&&t.classList.contains("user-name")?n+=t.lastChild.firstChild.textContent:void 0===t.innerText?n+=t.textContent:n+=t.innerText})),n}}},customize:function(e){$(e.document.body).css("color",config.colors.headingColor).css("border-color",config.colors.borderColor).css("background-color",config.colors.bodyBg),$(e.document.body).find("table").addClass("compact").css("color","inherit").css("border-color","inherit").css("background-color","inherit")}},{extend:"csv",text:'<i class="ti ti-file-text me-1" ></i>Csv',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(e,t,a){if(e.length<=0)return e;var s=$.parseHTML(e),n="";return $.each(s,(function(e,t){void 0!==t.classList&&t.classList.contains("user-name")?n+=t.lastChild.firstChild.textContent:void 0===t.innerText?n+=t.textContent:n+=t.innerText})),n}}}},{extend:"excel",text:'<i class="ti ti-file-spreadsheet me-1"></i>Excel',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(e,t,a){if(e.length<=0)return e;var s=$.parseHTML(e),n="";return $.each(s,(function(e,t){void 0!==t.classList&&t.classList.contains("user-name")?n+=t.lastChild.firstChild.textContent:void 0===t.innerText?n+=t.textContent:n+=t.innerText})),n}}}},{extend:"pdf",text:'<i class="ti ti-file-description me-1"></i>Pdf',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(e,t,a){if(e.length<=0)return e;var s=$.parseHTML(e),n="";return $.each(s,(function(e,t){void 0!==t.classList&&t.classList.contains("user-name")?n+=t.lastChild.firstChild.textContent:void 0===t.innerText?n+=t.textContent:n+=t.innerText})),n}}}},{extend:"copy",text:'<i class="ti ti-copy me-1" ></i>Copy',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(e,t,a){if(e.length<=0)return e;var s=$.parseHTML(e),n="";return $.each(s,(function(e,t){void 0!==t.classList&&t.classList.contains("user-name")?n+=t.lastChild.firstChild.textContent:void 0===t.innerText?n+=t.textContent:n+=t.innerText})),n}}}}]},{text:'<i class="ti ti-plus me-sm-1"></i> <span class="d-none d-sm-inline-block">Add New Record</span>',className:"create-new btn btn-primary"}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(e){return"Details of "+e.data().full_name}}),type:"column",renderer:function(e,t,a){var s=$.map(a,(function(e,t){return""!==e.title?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""})).join("");return!!s&&$('<table class="table"/><tbody />').append(s)}}}}),$("div.head-label").html('<h5 class="card-title mb-0">DataTable with Buttons</h5>'));var r=101;if(fv.on("core.form.valid",(function(){var t=$(".add-new-record .dt-full-name").val(),a=$(".add-new-record .dt-post").val(),s=$(".add-new-record .dt-email").val(),n=$(".add-new-record .dt-date").val(),l=$(".add-new-record .dt-salary").val();""!=t&&(e.row.add({id:r,full_name:t,post:a,email:s,start_date:n,salary:"$"+l,status:5}).draw(),r++,offCanvasEl.hide())})),$(".datatables-basic tbody").on("click",".delete-record",(function(){e.row($(this).parents("tr")).remove().draw()})),a.length)a.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:"full_name"},{data:"email"},{data:"city"},{data:"post"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{targets:-2,render:function(e,t,a,s){var n=a.status,r={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return void 0===r[n]?e:'<span class="badge '+r[n].class+'">'+r[n].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,render:function(e,t,a,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="text-primary ti ti-dots-vertical"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">Details</a><a href="javascript:;" class="dropdown-item">Archive</a><div class="dropdown-divider"></div><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></div></div><a href="javascript:;" class="btn btn-sm btn-icon item-edit"><i class="text-primary ti ti-pencil"></i></a>'}}],dom:'<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>><"table-responsive"t><"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:7,lengthMenu:[7,10,25,50,75,100]});if(s.length){var l=s.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:""},{data:"full_name"},{data:"post"},{data:"email"},{data:"city"},{data:"start_date"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{className:"control",orderable:!1,targets:0,searchable:!1,render:function(e,t,a,s){return""}},{visible:!1,targets:2},{targets:-2,render:function(e,t,a,s){var n=a.status,r={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return void 0===r[n]?e:'<span class="badge '+r[n].class+'">'+r[n].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,searchable:!1,render:function(e,t,a,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="text-primary ti ti-dots-vertical"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">Details</a><a href="javascript:;" class="dropdown-item">Archive</a><div class="dropdown-divider"></div><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></div></div><a href="javascript:;" class="btn btn-sm btn-icon item-edit"><i class="text-primary ti ti-pencil"></i></a>'}}],order:[[2,"asc"]],dom:'<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:7,lengthMenu:[7,10,25,50,75,100],drawCallback:function(e){var t=this.api(),a=t.rows({page:"current"}).nodes(),s=null;t.column(2,{page:"current"}).data().each((function(e,t){s!==e&&($(a).eq(t).before('<tr class="group"><td colspan="8">'+e+"</td></tr>"),s=e)}))},responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(e){return"Details of "+e.data().full_name}}),type:"column",renderer:function(e,t,a){var s=$.map(a,(function(e,t){return""!==e.title?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""})).join("");return!!s&&$('<table class="table"/><tbody />').append(s)}}}});$(".dt-row-grouping tbody").on("click","tr.group",(function(){var e=l.order()[0];2===e[0]&&"asc"===e[1]?l.order([2,"desc"]).draw():l.order([2,"asc"]).draw()}))}if(n.length)n.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:""},{data:"full_name"},{data:"post"},{data:"email"},{data:"start_date"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{className:"control",orderable:!1,targets:0,searchable:!1,render:function(e,t,a,s){return""}},{targets:-2,render:function(e,t,a,s){var n=a.status,r={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return void 0===r[n]?e:'<span class="badge '+r[n].class+'">'+r[n].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,searchable:!1,render:function(e,t,a,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="text-primary ti ti-dots-vertical"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">Details</a><a href="javascript:;" class="dropdown-item">Archive</a><div class="dropdown-divider"></div><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></div></div><a href="javascript:;" class="btn btn-sm btn-icon item-edit"><i class="text-primary ti ti-pencil"></i></a>'}}],language:{url:"//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/German.json"},displayLength:7,dom:'<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',lengthMenu:[7,10,25,50,75,100],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(e){return"Details of "+e.data().full_name}}),type:"column",renderer:function(e,t,a){var s=$.map(a,(function(e,t){return""!==e.title?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""})).join("");return!!s&&$('<table class="table"/><tbody />').append(s)}}}});setTimeout((()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")}),300)}));
