"use strict";$((function(){var e=$(".read-only-ratings");e&&e.rateYo({rtl:isRtl,rating:4,starWidth:"20px"})})),function(){window.Helpers.initCustomOptionCheck();const e=document.querySelector(".credit-card-mask"),t=document.querySelector(".expiry-date-mask"),o=document.querySelector(".cvv-code-mask");e&&new Cleave(e,{creditCard:!0,onCreditCardTypeChanged:function(e){document.querySelector(".card-type").innerHTML=""!=e&&"unknown"!=e?'<img src="'+assetsPath+"img/icons/payments/"+e+'-cc.png" height="28"/>':""}}),t&&new Cleave(t,{date:!0,delimiter:"/",datePattern:["m","y"]}),o&&new Cleave(o,{numeral:!0,numeralPositiveOnly:!0});const i=document.querySelector("#wizard-checkout");if(void 0!==typeof i&&null!==i){const e=i.querySelector("#wizard-checkout-form"),t=e.querySelector("#checkout-cart"),o=e.querySelector("#checkout-address"),n=e.querySelector("#checkout-payment"),a=e.querySelector("#checkout-confirmation"),r=[].slice.call(e.querySelectorAll(".btn-next")),l=[].slice.call(e.querySelectorAll(".btn-prev"));let u=new Stepper(i,{linear:!1});const c=FormValidation.formValidation(t,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:""}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){u.next()})),s=FormValidation.formValidation(o,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:""}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){u.next()})),d=FormValidation.formValidation(n,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:""}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){u.next()})),m=FormValidation.formValidation(a,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-md-12"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",(function(){alert("Submitted..!!")}));r.forEach((e=>{e.addEventListener("click",(e=>{switch(u._currentIndex){case 0:c.validate();break;case 1:s.validate();break;case 2:d.validate();break;case 3:m.validate()}}))})),l.forEach((e=>{e.addEventListener("click",(e=>{switch(u._currentIndex){case 3:case 2:case 1:u.previous()}}))}))}}();
