.app-kanban .kanban-wrapper{height:calc(100vh - 12rem);overflow-x:auto;overflow-y:auto;width:100%}@media(min-width:1200px){.layout-horizontal .app-kanban .kanban-wrapper{height:calc(100vh - 15.5rem)}}.app-kanban .kanban-wrapper .kanban-container{display:-ms-flexbox;display:flex;width:-webkit-max-content!important;width:-moz-max-content!important;width:max-content!important}.app-kanban .kanban-wrapper .kanban-container .kanban-board{background:transparent;height:100%;width:auto!important}.app-kanban .kanban-wrapper .kanban-container .kanban-board:focus{outline:0}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-board-header{-ms-flex-pack:justify;-ms-flex-align:center;align-items:center;display:-ms-flexbox;display:flex;justify-content:space-between}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-board-header .kanban-title-board{font-size:1.125rem;font-weight:600;max-width:13rem;overflow:hidden;white-space:nowrap}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-board-header .kanban-title-board:focus{outline:0}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-board-header .btn-default.btn:active{border-color:transparent}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-board-header .dropdown .dropdown-toggle:after{display:none}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-drag{min-height:1rem;min-width:16.25rem;padding:0}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-title-button{bottom:0;font-size:.8125rem;left:-8px;margin:-1.5rem 0;position:absolute}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-title-button:focus{box-shadow:none}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-item{border-radius:.375rem;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin-bottom:1rem;padding:1rem;position:relative;width:16.25rem}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-item .kanban-tasks-item-dropdown{cursor:pointer;display:none;position:absolute;right:.75rem}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-item .kanban-tasks-item-dropdown .dropdown-toggle:after{display:none}.app-kanban .kanban-wrapper .kanban-container .kanban-board .kanban-item:hover .kanban-tasks-item-dropdown{display:block}.app-kanban .kanban-add-new-board{float:left;margin-top:.9375rem;padding:0 .9375rem}.app-kanban .kanban-add-new-board .kanban-add-board-btn{padding-bottom:.9375rem}.app-kanban .kanban-add-new-board label{cursor:pointer;font-size:1.125rem;font-weight:600;margin-bottom:0}.app-kanban .kanban-update-item-sidebar{text-align:left}.app-kanban .kanban-update-item-sidebar .comment-editor.ql-container{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.app-kanban .kanban-update-item-sidebar .comment-editor .ql-editor{background:unset;min-height:7rem}.app-kanban .kanban-update-item-sidebar .comment-toolbar.ql-toolbar{border-bottom-left-radius:.375rem;border-bottom-right-radius:.375rem;border-top:0;text-align:right;width:100%}.kanban-board.gu-mirror .kanban-board-header .dropdown,.kanban-board.gu-mirror .kanban-item .kanban-tasks-item-dropdown .dropdown-toggle:after,.kanban-item.gu-mirror .kanban-tasks-item-dropdown .dropdown-toggle:after{display:none}.kanban-board.is-moving.gu-mirror .kanban-drag{padding-right:20px;width:100%}.light-style .app-kanban .kanban-board .kanban-board-header{color:#5d596c}.light-style .app-kanban .kanban-board .kanban-item{background-color:#fff;box-shadow:0 .25rem 1.125rem rgba(75,70,92,.1)}.light-style .app-kanban .kanban-add-new-board label{color:#5d596c}.dark-style .app-kanban .kanban-board .kanban-board-header{color:#cfd3ec}.dark-style .app-kanban .kanban-board .kanban-item{background-color:#2f3349;box-shadow:0 .25rem 1.25rem rgba(15,20,34,.4)}.dark-style .app-kanban .kanban-add-new-board label{color:#cfd3ec}.dark-style .kanban-item.gu-mirror{background-color:#2f3349}[dir=rtl] .app-kanban .kanban-add-new-btn,[dir=rtl] .app-kanban .kanban-board{float:right}[dir=rtl] .app-kanban .kanban-board .kanban-board-header .kanban-title-button{left:auto!important;right:-8px}[dir=rtl] .app-kanban .kanban-board .kanban-tasks-item-dropdown{left:1.2rem;right:auto!important}[dir=rtl] .app-kanban .kanban-update-item-sidebar,[dir=rtl] .app-kanban .kanban-update-item-sidebar .comment-toolbar.ql-toolbar{text-align:right}
