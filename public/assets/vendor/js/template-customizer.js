!function(){var t={8978:function(t,e,s){"use strict";var i=s(23645),o=s.n(i)()((function(t){return t[1]}));o.push([t.id,'#template-customizer{background:#fff;box-shadow:0 0 20px 0 rgba(0,0,0,.2);display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol!important;font-size:inherit!important;height:100%;position:fixed;right:0;top:0;-webkit-transform:translateX(380px);transform:translateX(380px);transition:all .2s ease-in;width:360px;z-index:99999999}#template-customizer h5{font-size:11px;font-weight:600;position:relative}#template-customizer>h5{-ms-flex:0 0 auto;flex:0 0 auto}#template-customizer .disabled{color:#d1d2d3!important}#template-customizer.template-customizer-open{-webkit-transform:none!important;transform:none!important;transition-delay:.1s}#template-customizer .template-customizer-open-btn{background:#333;border-bottom-left-radius:15%;border-top-left-radius:15%;color:#fff!important;display:block;font-size:18px!important;height:42px;left:0;line-height:42px;opacity:1;position:absolute;text-align:center;top:180px;-webkit-transform:translateX(-62px);transform:translateX(-62px);transition:all .1s linear .2s;width:42px;z-index:-1}@media (max-width:991.98px){#template-customizer .template-customizer-open-btn{top:145px}}.dark-style #template-customizer .template-customizer-open-btn{background:#555}#template-customizer .template-customizer-open-btn:before{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABClJREFUaEPtmY1RFEEQhbsjUCIQIhAiUCNQIxAiECIQIxAiECIAIpAMhAiECIQI2vquZqnZvp6fhb3SK5mqq6Ju92b69bzXf6is+dI1t1+eAfztG5z1BsxsU0S+ici2iPB3vm5E5EpEDlSVv2dZswFIxv8UkZcNy+5EZGcuEHMCOBeR951uvVDVD53vVl+bE8DvDu8Pxtyo6ta/BsByg1R15Bwzqz5/LJgn34CZwfnPInI4BUB6/1hV0cSjVxcAM4PbcBZjL0XklIPN7Is3fLCkdQPpPYw/VNXj5IhPIvJWRIhSl6p60ULWBGBm30Vk123EwRxCuIzWkkjNrCZywith10ewE1Xdq4GoAjCz/RTXW44Ynt+LyBEfT43kYfbj86J3w5Q32DNcRQDpwF+dkQXDMey8xem0L3TEqB4g3PZWad8agBMRgZPeu96D1/C2Zbh3X0p80Op1xxloztN48bMQQNoc7+eLEuAoPSPiIDY4Ooo+E6ixeNXM+D3GERz2U3CIqMstLJUgJQDe+7eq6mub0NYEkLAKwEHkiBQDCZtddZCZ8d6r7JDwFkoARklHRPZUFVDVZWbwGuNrC4EfdOzFrRABh3Wnqhv+d70AEBLGFROPmeHlnM81G69UdSd6IUuM0GgUVn1uqWmg5EmMfBeEyB7Pe3txBkY+rGT8j0J+WXq/BgDkUCaqLgEAnwcRog0veMIqFAAwCy2wnw+bI2GaGboBgF9k5N0o0rUSGUb4eO0BeO9j/GYhkSHMHMTIqwGARX6p6a+nlPBl8kZuXMD9j6pKfF9aZuaFOdJCEL5D4eYb9wCYVCanrBmGyii/tIq+SLj/HQBCaM5bLzwfPqdQ6FpVHyra4IbuVbXaY7dETC2ESPNNWiIOi69CcdgSMXsh4tNSUiklMgwmC0aNd08Y5WAES6HHehM4gu97wyhBgWpgqXsrASglprDy7CwhehMZOSbK6JMSma+Fio1KltCmlBIj7gfZOGx8ppQSXrhzFnOhJ/31BDkjFHRvOd09x0mRBA9SFgxUgHpQg0q0t5ymPMlL+EnldFTfDA0NAmf+OTQ0X0sRouf7NNkYGhrOYNrxtIaGg83MNzVDSe3LXLhP7O/yrCsCz1zlWTpjWkuZAOBpX3yVnLqI1yLCOKU6qMrmP7SSrUEw54XF4WBIK5FxCMOr3lVsfGqNSmPzBXUnJTIX1jyVBq9wO6UObOpgC5GjO98vFKnTdQMZXxEsWZlDiCZMIxAbNxQOqlpVZtobejBaZNoBnRDzMFpkxvTQOD36BlrcySZuI6p1ACB6LU3wWuf5581+oHfD1vi89bz3nFUC8Nm7ZlP3nKkFbM4bWPt/MSFwklprYItwt6cmvpWJ2IVcQBCz6bLysSCv3SaANCiTsnaNRrNRqMXVVT1/BrAqz/buu/Y38Ad3KC5PARej0QAAAABJRU5ErkJggg==");background-size:100% 100%;content:"";display:block;height:22px;margin:10px;position:absolute;width:22px}.customizer-hide #template-customizer .template-customizer-open-btn{display:none}[dir=rtl] #template-customizer .template-customizer-open-btn{border-radius:0;border-bottom-right-radius:15%;border-top-right-radius:15%}[dir=rtl] #template-customizer .template-customizer-open-btn:before{margin-left:-2px}#template-customizer.template-customizer-open .template-customizer-open-btn{opacity:0;-webkit-transform:none!important;transform:none!important;transition-delay:0s}#template-customizer .template-customizer-close-btn{display:block;font-size:20px;position:absolute;right:0;top:32px;-webkit-transform:translateY(-50%);transform:translateY(-50%)}#template-customizer .template-customizer-inner{-ms-flex:0 1 auto;flex:0 1 auto;opacity:1;overflow:auto;position:relative;transition:opacity .2s}#template-customizer .template-customizer-inner>div:first-child>hr:first-of-type{display:none!important}#template-customizer .template-customizer-inner>div:first-child>h5:first-of-type{padding-top:0!important}#template-customizer .template-customizer-themes-inner{opacity:1;position:relative;transition:opacity .2s}#template-customizer .template-customizer-theme-item{-ms-flex-align:center;-ms-flex-pack:justify;align-items:center;cursor:pointer;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;justify-content:space-between;margin-bottom:10px;padding:0 24px;width:100%}#template-customizer .template-customizer-theme-item input{opacity:0;position:absolute;z-index:-1}#template-customizer .template-customizer-theme-item input~span{opacity:.25;transition:all .2s}#template-customizer .template-customizer-theme-item .template-customizer-theme-checkmark{border-bottom:1px solid;border-right:1px solid;display:inline-block;height:12px;opacity:0;-webkit-transform:rotate(45deg);transform:rotate(45deg);transition:all .2s;width:6px}[dir=rtl] #template-customizer .template-customizer-theme-item .template-customizer-theme-checkmark{border-left:1px solid;border-right:none;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}#template-customizer .template-customizer-theme-item input:checked:not([disabled])~span,#template-customizer .template-customizer-theme-item input:checked:not([disabled])~span .template-customizer-theme-checkmark,#template-customizer .template-customizer-theme-item:hover input:not([disabled])~span{opacity:1}#template-customizer .template-customizer-theme-colors span{border-radius:50%;box-shadow:inset 0 0 0 1px rgba(0,0,0,.1);display:block;height:10px;margin:0 1px;width:10px}#template-customizer.template-customizer-loading .template-customizer-inner,#template-customizer.template-customizer-loading-theme .template-customizer-themes-inner{opacity:.2}#template-customizer.template-customizer-loading .template-customizer-inner:after,#template-customizer.template-customizer-loading-theme .template-customizer-themes-inner:after{bottom:0;content:"";display:block;left:0;position:absolute;right:0;top:0;z-index:999}.layout-menu-100vh #template-customizer{height:100vh}[dir=rtl] #template-customizer{left:0;right:auto;-webkit-transform:translateX(-380px);transform:translateX(-380px)}[dir=rtl] #template-customizer .template-customizer-open-btn{left:auto;right:0;-webkit-transform:translateX(62px);transform:translateX(62px)}[dir=rtl] #template-customizer .template-customizer-close-btn{left:0;right:auto}#template-customizer .template-customizer-layouts-options[disabled]{opacity:.5;pointer-events:none}[dir=rtl] .template-customizer-t-style_switch_light{padding-right:0!important}',""]),e.Z=o},23645:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var s=t(e);return e[2]?"@media ".concat(e[2]," {").concat(s,"}"):s})).join("")},e.i=function(t,s,i){"string"==typeof t&&(t=[[null,t,""]]);var o={};if(i)for(var a=0;a<this.length;a++){var n=this[a][0];null!=n&&(o[n]=!0)}for(var l=0;l<t.length;l++){var r=[].concat(t[l]);i&&o[r[0]]||(s&&(r[2]?r[2]="".concat(s," and ").concat(r[2]):r[2]=s),e.push(r))}},e}},32950:function(t){t.exports='<div id="template-customizer" class="invert-bg-white"> <a href="javascript:void(0)" class="template-customizer-open-btn" tabindex="-1"></a> <div class="p-4 m-0 lh-1 border-bottom template-customizer-header"> <h4 class="template-customizer-t-panel_header mb-2"></h4> <p class="template-customizer-t-panel_sub_header mb-0"></p> <a href="javascript:void(0)" class="btn-close template-customizer-close-btn fw-light px-4 py-2 text-body" tabindex="-1"></a> </div> <div class="template-customizer-inner pt-4"> <div class="template-customizer-theming"> <h5 class="m-0 px-4 py-4 lh-1 text-light d-block"> <span class="template-customizer-t-theming_header"></span> </h5> <div class="m-0 px-4 pb-3 template-customizer-themes w-100"> <label for="customizerTheme" class="form-label template-customizer-t-theme_label"></label> <div class="row row-cols-lg-auto g-3 align-items-center template-customizer-themes-options"></div> </div> <div class="m-0 px-4 pb-3 pt-1 template-customizer-style w-100"> <label for="customizerStyle" class="form-label d-block template-customizer-t-style_label"></label> <label class="switch switch-sm"> <span class="switch-label template-customizer-t-style_switch_light"></span> <input type="checkbox" class="switch-input"/> <span class="switch-toggle-slider"> <span class="switch-on"></span> <span class="switch-off"></span> </span> <span class="switch-label template-customizer-t-style_switch_dark"></span> </label> </div> </div> <div class="template-customizer-layout"> <hr class="m-0"> <h5 class="m-0 px-4 py-4 lh-1 text-light d-block"> <span class="template-customizer-t-layout_header"></span> </h5> <div class="m-0 px-4 pb-3 d-block template-customizer-layoutType"> <label for="customizerStyle" class="form-label d-block template-customizer-t-layout_label"></label> <div class="row row-cols-lg-auto g-3 align-items-center template-customizer-layouts-options"> <div class="col-12"> <div class="form-check"> <input class="form-check-input" type="radio" name="layoutRadios" id="layoutRadios-static" value="static"> <label class="form-check-label template-customizer-t-layout_static" for="layoutRadios-static"></label> </div> </div> <div class="col-12"> <div class="form-check"> <input class="form-check-input" type="radio" name="layoutRadios" id="layoutRadios-fixed" value="fixed"> <label class="form-check-label template-customizer-t-layout_fixed" for="layoutRadios-fixed"></label> </div> </div> </div> </div> <label class="m-0 px-4 pb-3 d-flex media align-items-middle justify-content-between template-customizer-layoutNavbarFixed"> <span class="template-customizer-t-layout_navbar_label"></span> <label class="switch switch-sm pe-4"> <input type="checkbox" class="switch-input"/> <span class="switch-toggle-slider"> <span class="switch-on"></span> <span class="switch-off"></span> </span> </label> </label> <label class="m-0 px-4 pb-3 d-flex media align-items-middle justify-content-between template-customizer-layoutFooterFixed"> <span class="template-customizer-t-layout_footer_label"></span> <label class="switch switch-sm pe-4"> <input type="checkbox" class="switch-input"/> <span class="switch-toggle-slider"> <span class="switch-on"></span> <span class="switch-off"></span> </span> </label> </label> <label class="m-0 px-4 pb-3 d-flex media align-items-middle justify-content-between template-customizer-showDropdownOnHover"> <span class="template-customizer-t-layout_dd_open_label"></span> <label class="switch switch-sm pe-4"> <input type="checkbox" class="switch-input"/> <span class="switch-toggle-slider"> <span class="switch-on"></span> <span class="switch-off"></span> </span> </label> </label> </div> <div class="template-customizer-misc"> <hr class="m-0"> <h5 class="m-0 px-4 py-4 lh-1 text-light d-block"> <span class="template-customizer-t-misc_header"></span> </h5> <label class="m-0 px-4 pb-3 d-flex media align-items-middle justify-content-between template-customizer-rtl"> <span class="template-customizer-t-rtl_label"></span> <label class="switch switch-sm pe-4"> <input type="checkbox" class="switch-input"/> <span class="switch-toggle-slider"> <span class="switch-on"></span> <span class="switch-off"></span> </span> </label> </label> </div> </div> </div>'},93379:function(t,e,s){"use strict";var i,o=function(){return void 0===i&&(i=Boolean(window&&document&&document.all&&!window.atob)),i},a=function(){var t={};return function(e){if(void 0===t[e]){var s=document.querySelector(e);if(window.HTMLIFrameElement&&s instanceof window.HTMLIFrameElement)try{s=s.contentDocument.head}catch(t){s=null}t[e]=s}return t[e]}}(),n=[];function l(t){for(var e=-1,s=0;s<n.length;s++)if(n[s].identifier===t){e=s;break}return e}function r(t,e){for(var s={},i=[],o=0;o<t.length;o++){var a=t[o],r=e.base?a[0]+e.base:a[0],c=s[r]||0,u="".concat(r," ").concat(c);s[r]=c+1;var d=l(u),h={css:a[1],media:a[2],sourceMap:a[3]};-1!==d?(n[d].references++,n[d].updater(h)):n.push({identifier:u,updater:f(h,e),references:1}),i.push(u)}return i}function c(t){var e=document.createElement("style"),i=t.attributes||{};if(void 0===i.nonce){var o=s.nc;o&&(i.nonce=o)}if(Object.keys(i).forEach((function(t){e.setAttribute(t,i[t])})),"function"==typeof t.insert)t.insert(e);else{var n=a(t.insert||"head");if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(e)}return e}var u,d=(u=[],function(t,e){return u[t]=e,u.filter(Boolean).join("\n")});function h(t,e,s,i){var o=s?"":i.media?"@media ".concat(i.media," {").concat(i.css,"}"):i.css;if(t.styleSheet)t.styleSheet.cssText=d(e,o);else{var a=document.createTextNode(o),n=t.childNodes;n[e]&&t.removeChild(n[e]),n.length?t.insertBefore(a,n[e]):t.appendChild(a)}}function m(t,e,s){var i=s.css,o=s.media,a=s.sourceMap;if(o?t.setAttribute("media",o):t.removeAttribute("media"),a&&"undefined"!=typeof btoa&&(i+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),t.styleSheet)t.styleSheet.cssText=i;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(i))}}var p=null,y=0;function f(t,e){var s,i,o;if(e.singleton){var a=y++;s=p||(p=c(e)),i=h.bind(null,s,a,!1),o=h.bind(null,s,a,!0)}else s=c(e),i=m.bind(null,s,e),o=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(s)};return i(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;i(t=e)}else o()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=o());var s=r(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var i=0;i<s.length;i++){var o=l(s[i]);n[o].references--}for(var a=r(t,e),c=0;c<s.length;c++){var u=l(s[c]);0===n[u].references&&(n[u].updater(),n.splice(u,1))}s=a}}}}},e={};function s(i){var o=e[i];if(void 0!==o)return o.exports;var a=e[i]={id:i,exports:{}};return t[i](a,a.exports,s),a.exports}s.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return s.d(e,{a:e}),e},s.d=function(t,e){for(var i in e)s.o(e,i)&&!s.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};!function(){"use strict";s.r(i),s.d(i,{TemplateCustomizer:function(){return S}});var t=s(93379),e=s.n(t),o=s(8978),a={insert:"head",singleton:!1},n=(e()(o.Z,a),o.Z.locals,s(32950)),l=s.n(n);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function c(t,e,s){return(e=d(e))in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}function u(t,e){for(var s=0;s<e.length;s++){var i=e[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,d(i.key),i)}}function d(t){var e=function(t,e){if("object"!==r(t)||null===t)return t;var s=t[Symbol.toPrimitive];if(void 0!==s){var i=s.call(t,e||"default");if("object"!==r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r(e)?e:String(e)}var h=["rtl","style","layoutType","layoutMenuFlipped","showDropdownOnHover","layoutNavbarFixed","layoutFooterFixed","themes"],m=["light","dark"],p=document.documentElement.classList,y=document.getElementsByTagName("HTML")[0].getAttribute("data-theme")||0,f=p.contains("dark-style")?"dark":"light",g="rtl"===document.documentElement.getAttribute("dir"),v=!!p.contains("layout-menu-collapsed"),b=!!p.contains("layout-menu-flipped"),_=!!p.contains("layout-navbar-fixed"),x=!!p.contains("layout-footer-fixed"),w=p.contains("layout-menu-offcanvas")?"static-offcanvas":p.contains("layout-menu-fixed")?"fixed":p.contains("layout-menu-fixed-offcanvas")?"fixed-offcanvas":"static",S=function(){function t(e){var s=e.cssPath,i=e.themesPath,o=e.cssFilenamePattern,a=e.displayCustomizer,n=e.controls,l=e.defaultTextDir,r=e.defaultLayoutType,c=e.defaultMenuCollapsed,u=e.defaultMenuFlipped,d=e.defaultShowDropdownOnHover,p=e.defaultNavbarFixed,S=e.defaultFooterFixed,z=e.styles,F=e.defaultStyle,k=e.availableThemes,C=e.defaultTheme,A=e.pathResolver,N=e.onSettingsChange,T=e.lang;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!this._ssr){if(!window.Helpers)throw new Error("window.Helpers required.");if(this.settings={},this.settings.cssPath=s,this.settings.themesPath=i,this.settings.cssFilenamePattern=o||"%name%.css",this.settings.displayCustomizer=void 0===a||a,this.settings.controls=n||h,this.settings.defaultTextDir="rtl"===l||g,this.settings.defaultLayoutType=r||w,this.settings.defaultMenuCollapsed=void 0!==c?c:v,this.settings.defaultMenuFlipped=void 0!==u?u:b,this.settings.defaultShowDropdownOnHover=void 0!==d?d:undefined,this.settings.defaultNavbarFixed=void 0!==p?p:_,this.settings.defaultFooterFixed=void 0!==S?S:x,this.settings.availableThemes=k||t.THEMES,this.settings.defaultTheme=this._getDefaultTheme(void 0!==C?C:y),this.settings.styles=z||m,this.settings.defaultStyle=F||f,this.settings.lang=T||"en",this.pathResolver=A||function(t){return t},this.settings.styles.length<2){var E=this.settings.controls.indexOf("style");-1!==E&&(this.settings.controls=this.settings.controls.slice(0,E).concat(this.settings.controls.slice(E+1)))}this.settings.onSettingsChange="function"==typeof N?N:function(){},this._loadSettings(),this._listeners=[],this._controls={},this._initDirection(),this._initStyle(),this._initTheme(),this.setLayoutType(this.settings.layoutType,!1),this.setLayoutMenuFlipped(this.settings.layoutMenuFlipped,!1),this.setDropdownOnHover(this.settings.showDropdownOnHover,!1),this.setLayoutNavbarFixed(this.settings.layoutNavbarFixed,!1),this.setLayoutFooterFixed(this.settings.layoutFooterFixed,!1),this._setup()}}var e,s,i;return e=t,s=[{key:"setRtl",value:function(t){this._hasControls("rtl")&&(this._setSetting("Rtl",String(t)),window.location.reload())}},{key:"setStyle",value:function(t){this._hasControls("style")&&(this._setSetting("Style",-1===["dark"].indexOf(t)?"light":t),window.location.reload())}},{key:"setTheme",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(this._hasControls("themes")){var i=this._getThemeByName(t);if(i){this.settings.theme=i,e&&this._setSetting("Theme",t);var o=this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",t+("light"!==this.settings.style?"-".concat(this.settings.style):"")));this._loadStylesheets(c({},o,document.querySelector(".template-customizer-theme-css")),s||function(){}),e&&this.settings.onSettingsChange.call(this,this.settings)}}}},{key:"setLayoutType",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this._hasControls("layoutType")&&("static"===t||"static-offcanvas"===t||"fixed"===t||"fixed-offcanvas"===t)){this.settings.layoutType=t,e&&this._setSetting("LayoutType",t),window.Helpers.setPosition("fixed"===t||"fixed-offcanvas"===t,"static-offcanvas"===t||"fixed-offcanvas"===t),e&&this.settings.onSettingsChange.call(this,this.settings);var s=window.Helpers.menuPsScroll,i=window.PerfectScrollbar;"fixed"===this.settings.layoutType||"fixed-offcanvas"===this.settings.layoutType?i&&s&&(window.Helpers.menuPsScroll.destroy(),s=new i(document.querySelector(".menu-inner"),{suppressScrollX:!0,wheelPropagation:!1}),window.Helpers.menuPsScroll=s):s&&window.Helpers.menuPsScroll.destroy()}}},{key:"setLayoutMenuFlipped",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._hasControls("layoutMenuFlipped")&&(this.settings.layoutMenuFlipped=t,e&&this._setSetting("MenuFlipped",t),window.Helpers.setFlipped(t),e&&this.settings.onSettingsChange.call(this,this.settings))}},{key:"setDropdownOnHover",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this._hasControls("showDropdownOnHover")){if(this.settings.showDropdownOnHover=t,e&&this._setSetting("ShowDropdownOnHover",t),window.Helpers.mainMenu){window.Helpers.mainMenu.destroy(),config.showDropdownOnHover=t;var s=window,i=s.Menu;window.Helpers.mainMenu=new i(document.getElementById("layout-menu"),{orientation:"horizontal",closeChildren:!0,showDropdownOnHover:config.showDropdownOnHover})}e&&this.settings.onSettingsChange.call(this,this.settings)}}},{key:"setLayoutNavbarFixed",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._hasControls("layoutNavbarFixed")&&(this.settings.layoutNavbarFixed=t,e&&this._setSetting("FixedNavbar",t),window.Helpers.setNavbarFixed(t),e&&this.settings.onSettingsChange.call(this,this.settings))}},{key:"setLayoutFooterFixed",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._hasControls("layoutFooterFixed")&&(this.settings.layoutFooterFixed=t,e&&this._setSetting("FixedFooter",t),window.Helpers.setFooterFixed(t),e&&this.settings.onSettingsChange.call(this,this.settings))}},{key:"setLang",value:function(e){var s=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e!==this.settings.lang||i){if(!t.LANGUAGES[e])throw new Error('Language "'.concat(e,'" not found!'));var o=t.LANGUAGES[e];["panel_header","panel_sub_header","theming_header","theme_header","style_label","style_switch_light","style_switch_dark","layout_header","layout_label","layout_static","layout_offcanvas","layout_fixed","layout_fixed_offcanvas","layout_flipped_label","layout_dd_open_label","layout_navbar_label","layout_footer_label","misc_header","theme_label","rtl_label"].forEach((function(t){var e=s.container.querySelector(".template-customizer-t-".concat(t));e&&(e.textContent=o[t])}));for(var a=o.themes||{},n=this.container.querySelectorAll(".template-customizer-theme-item")||[],l=0,r=n.length;l<r;l++){var c=n[l].querySelector('input[type="radio"]').value;n[l].querySelector(".template-customizer-theme-name").textContent=a[c]||this._getThemeByName(c).title}this.settings.lang=e}}},{key:"update",value:function(){if(!this._ssr){var t=!!document.querySelector(".layout-navbar"),e=!!document.querySelector(".layout-menu"),s=!!document.querySelector(".layout-menu-horizontal.menu, .layout-menu-horizontal .menu"),i=(document.querySelector(".layout-wrapper.layout-navbar-full"),!!document.querySelector(".content-footer"));this._controls.layoutMenuFlipped&&(e?(this._controls.layoutMenuFlipped.removeAttribute("disabled"),this._controls.layoutMenuFlipped.classList.remove("disabled")):(this._controls.layoutMenuFlipped.setAttribute("disabled","disabled"),this._controls.layoutMenuFlipped.classList.add("disabled"))),this._controls.showDropdownOnHover&&(e?(this._controls.showDropdownOnHover.setAttribute("disabled","disabled"),this._controls.showDropdownOnHover.classList.add("disabled")):(this._controls.showDropdownOnHover.removeAttribute("disabled"),this._controls.showDropdownOnHover.classList.remove("disabled"))),this._controls.layoutNavbarFixed&&(t?(this._controls.layoutNavbarFixed.removeAttribute("disabled"),this._controls.layoutNavbarFixedW.classList.remove("disabled")):(this._controls.layoutNavbarFixed.setAttribute("disabled","disabled"),this._controls.layoutNavbarFixedW.classList.add("disabled")),s&&t&&"fixed"==this.settings.layoutType&&(this._controls.layoutNavbarFixed.setAttribute("disabled","disabled"),this._controls.layoutNavbarFixedW.classList.add("disabled"))),this._controls.layoutFooterFixed&&(i?(this._controls.layoutFooterFixed.removeAttribute("disabled"),this._controls.layoutFooterFixedW.classList.remove("disabled")):(this._controls.layoutFooterFixed.setAttribute("disabled","disabled"),this._controls.layoutFooterFixedW.classList.add("disabled"))),!e&&"fixed"==this.settings.layoutType&&this._hasControls("layoutNavbarFixed")&&(this._controls.layoutNavbarFixed.setAttribute("disabled","disabled"),this._controls.layoutNavbarFixedW.classList.add("disabled")),this._controls.layoutType&&(e||s?this._controls.layoutType.removeAttribute("disabled"):this._controls.layoutType.setAttribute("disabled","disabled"))}}},{key:"clearLocalStorage",value:function(){this._ssr||(this._setSetting("Theme",""),this._setSetting("Rtl",""),this._setSetting("Style",""),this._setSetting("MenuFlipped",""),this._setSetting("FixedNavbar",""),this._setSetting("FixedFooter",""),this._setSetting("LayoutType",""))}},{key:"destroy",value:function(){this._ssr||(this._cleanup(),this.settings=null,this.container.parentNode.removeChild(this.container),this.container=null)}},{key:"_loadSettings",value:function(){var t,e=this._getSetting("Rtl"),s=this._getSetting("Style"),i=this._getSetting("LayoutCollapsed"),o=this._getSetting("LayoutMenuFlipped"),a=this._getSetting("ShowDropdownOnHover"),n=this._getSetting("FixedNavbar"),l=this._getSetting("FixedFooter"),r=this._getSetting("LayoutType");t=""!==r&&-1!==["static","static-offcanvas","fixed","fixed-offcanvas"].indexOf(r)?r:this.settings.defaultLayoutType,this.settings.layoutType=t,this.settings.rtl=""!==e?"true"===e:this.settings.defaultTextDir,this.settings.style=-1!==this.settings.styles.indexOf(s)?s:this.settings.defaultStyle,-1===this.settings.styles.indexOf(this.settings.style)&&(this.settings.style=this.settings.styles[0]),this.settings.layoutMenu=""!==i?"true"===i:this.settings.defaultMenuCollapsed,this.settings.layoutMenuFlipped=""!==o?"true"===o:this.settings.defaultMenuFlipped,this.settings.showDropdownOnHover=""!==a?"true"===a:this.settings.defaultShowDropdownOnHover,this.settings.layoutNavbarFixed=""!==n?"true"===n:this.settings.defaultNavbarFixed,this.settings.layoutFooterFixed=""!==l?"true"===l:this.settings.defaultFooterFixed,this.settings.theme=this._getThemeByName(this._getSetting("Theme"),!0),this._hasControls("rtl")||(this.settings.rtl="rtl"===document.documentElement.getAttribute("dir")),this._hasControls("style")||(this.settings.style=p.contains("dark-style")?"dark":"light"),this._hasControls("layoutType")||(this.settings.layoutType=null),this._hasControls("layoutMenuFlipped")||(this.settings.layoutMenuFlipped=null),this._hasControls("showDropdownOnHover")||(this.settings.showDropdownOnHover=null),this._hasControls("layoutNavbarFixed")||(this.settings.layoutNavbarFixed=null),this._hasControls("layoutFooterFixed")||(this.settings.layoutFooterFixed=null),this._hasControls("themes")||(this.settings.theme=null)}},{key:"_setup",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;this._cleanup(),this.container=this._getElementFromString(l());var s=this.container;this.settings.displayCustomizer?s.setAttribute("style","visibility: visible"):s.setAttribute("style","visibility: hidden");var i=this.container.querySelector(".template-customizer-open-btn"),o=function(){t.container.classList.add("template-customizer-open"),t.update(),t._updateInterval&&clearInterval(t._updateInterval),t._updateInterval=setInterval((function(){t.update()}),500)};i.addEventListener("click",o),this._listeners.push([i,"click",o]);var a=this.container.querySelector(".template-customizer-close-btn"),n=function(){t.container.classList.remove("template-customizer-open"),t._updateInterval&&(clearInterval(t._updateInterval),t._updateInterval=null)};a.addEventListener("click",n),this._listeners.push([a,"click",n]);var r=this.container.querySelector(".template-customizer-misc");if(this._hasControls("rtl")&&rtlSupport){var c=r.querySelector("input");this.settings.rtl&&c.setAttribute("checked","checked");var u=function(e){t._loadingState(!0),t.setRtl(e.target.checked)};c.addEventListener("change",u),this._listeners.push([c,"change",u])}else r.parentNode.removeChild(r);var d=this.container.querySelector(".template-customizer-style");if(this._hasControls("style")){var h=d.querySelector("input");"dark"===this.settings.style&&h.setAttribute("checked","checked");var m=function(e){t._loadingState(!0),e.target.checked?t.setStyle("dark"):t.setStyle("light")};h.addEventListener("change",m),this._listeners.push([h,"change",m])}else d.parentNode.removeChild(d);var p=this.container.querySelector(".template-customizer-themes");if(this._hasControls("themes")){var y=p.querySelector(".template-customizer-themes-options");this.settings.availableThemes.forEach((function(e){var s=t._getElementFromString('<div class="col-12"><div class="form-check"><input class="form-check-input" type="radio" name="themeRadios" id="themeRadios'.concat(e.name,'" value="').concat(e.name,'"><label class="form-check-label" for="themeRadios').concat(e.name,'">').concat(e.title,"</label></div></div>"));y.appendChild(s)})),y.querySelector('input[value="'.concat(this.settings.theme.name,'"]')).setAttribute("checked","checked");var f=function(e){t._loading||(t._loading=!0,t._loadingState(!0,!0),t.setTheme(e.target.value,!0,(function(){t._loading=!1,t._loadingState(!1,!0)})))};y.addEventListener("change",f),this._listeners.push([y,"change",f])}else p.parentNode.removeChild(p);var g=this.container.querySelector(".template-customizer-theming");this._hasControls("style")||this._hasControls("themes")||g.parentNode.removeChild(g);var v=this.container.querySelector(".template-customizer-layout");if(this._hasControls("layoutType layoutNavbarFixed layoutFooterFixed layoutMenuFlipped showDropdownOnHover",!0)){var b=this.container.querySelector(".template-customizer-layoutType");if(this._hasControls("layoutType")){this._controls.layoutType=b.querySelector(".template-customizer-layouts-options"),this._controls.layoutType.querySelector('input[value="'.concat(this.settings.layoutType,'"]')).setAttribute("checked","checked");var _=function(e){return t.setLayoutType(e.target.value)};this._controls.layoutType.addEventListener("change",_),this._listeners.push([this._controls.layoutType,"change",_])}else b.parentNode.removeChild(b);if(this._controls.showDropdownOnHover=this.container.querySelector(".template-customizer-showDropdownOnHover"),this._hasControls("showDropdownOnHover")){this._controls.showDropdownOnHover=this._controls.showDropdownOnHover.querySelector("input"),this.settings.showDropdownOnHover&&this._controls.showDropdownOnHover.setAttribute("checked","checked");var x=function(e){return t.setDropdownOnHover(e.target.checked)};this._controls.showDropdownOnHover.addEventListener("change",x),this._listeners.push([this._controls.showDropdownOnHover,"change",x])}else this._controls.showDropdownOnHover.parentNode.removeChild(this._controls.showDropdownOnHover);if(this._controls.layoutNavbarFixedW=this.container.querySelector(".template-customizer-layoutNavbarFixed"),this._hasControls("layoutNavbarFixed")){this._controls.layoutNavbarFixed=this._controls.layoutNavbarFixedW.querySelector("input"),this.settings.layoutNavbarFixed&&this._controls.layoutNavbarFixed.setAttribute("checked","checked");var w=function(e){return t.setLayoutNavbarFixed(e.target.checked)};this._controls.layoutNavbarFixed.addEventListener("change",w),this._listeners.push([this._controls.layoutNavbarFixed,"change",w])}else this._controls.layoutNavbarFixedW.parentNode.removeChild(this._controls.layoutNavbarFixedW);if(this._controls.layoutFooterFixedW=this.container.querySelector(".template-customizer-layoutFooterFixed"),this._hasControls("layoutFooterFixed")){this._controls.layoutFooterFixed=this._controls.layoutFooterFixedW.querySelector("input"),this.settings.layoutFooterFixed&&this._controls.layoutFooterFixed.setAttribute("checked","checked");var S=function(e){return t.setLayoutFooterFixed(e.target.checked)};this._controls.layoutFooterFixed.addEventListener("change",S),this._listeners.push([this._controls.layoutFooterFixed,"change",S])}else this._controls.layoutFooterFixedW.parentNode.removeChild(this._controls.layoutFooterFixedW)}else v.parentNode.removeChild(v);this.setLang(this.settings.lang,!0),e===document?e.body?e.body.appendChild(this.container):window.addEventListener("DOMContentLoaded",(function(){return e.body.appendChild(t.container)})):e.appendChild(this.container)}},{key:"_initDirection",value:function(){this._hasControls("rtl")&&document.documentElement.setAttribute("dir",this.settings.rtl?"rtl":"ltr")}},{key:"_initStyle",value:function(){if(this._hasControls("style")){var t=this.settings.style;this._insertStylesheet("template-customizer-core-css",this.pathResolver(this.settings.cssPath+this.settings.cssFilenamePattern.replace("%name%","core".concat("light"!==t?"-".concat(t):"")))),("light"===t?["dark-style"]:["light-style"]).forEach((function(t){document.documentElement.classList.remove(t)})),document.documentElement.classList.add("".concat(t,"-style"))}}},{key:"_initTheme",value:function(){if(this._hasControls("themes"))this._insertStylesheet("template-customizer-theme-css",this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",this.settings.theme.name+("light"!==this.settings.style?"-".concat(this.settings.style):""))));else{var t=this._getSetting("Theme");this._insertStylesheet("template-customizer-theme-css",this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",t||"theme-default"+("light"!==this.settings.style?"-".concat(this.settings.style):""))))}}},{key:"_insertStylesheet",value:function(t,e){var s=document.querySelector(".".concat(t));if("number"==typeof document.documentMode&&document.documentMode<11){if(!s)return;if(e===s.getAttribute("href"))return;var i=document.createElement("link");i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.className=t,i.setAttribute("href",e),s.parentNode.insertBefore(i,s.nextSibling)}else document.write('<link rel="stylesheet" type="text/css" href="'.concat(e,'" class="').concat(t,'">'));s.parentNode.removeChild(s)}},{key:"_loadStylesheets",value:function(t,e){var s=Object.keys(t),i=s.length,o=0;function a(t,e,s){var i=document.createElement("link");i.setAttribute("href",t),i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.className=e.className;var o,a="sheet"in i?"sheet":"styleSheet",n="sheet"in i?"cssRules":"rules",l=setTimeout((function(){clearInterval(o),clearTimeout(l),e.parentNode.removeChild(i),s(!1,t)}),15e3);o=setInterval((function(){try{i[a]&&i[a][n].length&&(clearInterval(o),clearTimeout(l),e.parentNode.removeChild(e),s(!0))}catch(t){}}),10),e.parentNode.insertBefore(i,e.nextSibling)}for(var n=0;n<s.length;n++)a(s[n],t[s[n]],void((o+=1)>=i&&e()))}},{key:"_loadingState",value:function(t,e){this.container.classList[t?"add":"remove"]("template-customizer-loading".concat(e?"-theme":""))}},{key:"_getElementFromString",value:function(t){var e=document.createElement("div");return e.innerHTML=t,e.firstChild}},{key:"_getSetting",value:function(t){var e=null,s=this._getLayoutName();try{e=localStorage.getItem("templateCustomizer-".concat(s,"--").concat(t))}catch(t){}return String(e||"")}},{key:"_setSetting",value:function(t,e){var s=this._getLayoutName();try{localStorage.setItem("templateCustomizer-".concat(s,"--").concat(t),String(e))}catch(t){}}},{key:"_getLayoutName",value:function(){return document.getElementsByTagName("HTML")[0].getAttribute("data-template")}},{key:"_removeListeners",value:function(){for(var t=0,e=this._listeners.length;t<e;t++)this._listeners[t][0].removeEventListener(this._listeners[t][1],this._listeners[t][2])}},{key:"_cleanup",value:function(){this._removeListeners(),this._listeners=[],this._controls={},this._updateInterval&&(clearInterval(this._updateInterval),this._updateInterval=null)}},{key:"_ssr",get:function(){return"undefined"==typeof window}},{key:"_hasControls",value:function(t){var e=this,s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t.split(" ").reduce((function(t,i){return-1!==e.settings.controls.indexOf(i)?(s||!1!==t)&&(t=!0):s&&!0===t||(t=!1),t}),null)}},{key:"_getDefaultTheme",value:function(t){var e;if(!(e="string"==typeof t?this._getThemeByName(t,!1):this.settings.availableThemes[t]))throw new Error('Theme ID "'.concat(t,'" not found!'));return e}},{key:"_getThemeByName",value:function(t){for(var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=this.settings.availableThemes,i=0,o=s.length;i<o;i++)if(s[i].name===t)return s[i];return e?this.settings.defaultTheme:null}}],s&&u(e.prototype,s),i&&u(e,i),Object.defineProperty(e,"prototype",{writable:!1}),t}();S.THEMES=[{name:"theme-default",title:"Default"},{name:"theme-semi-dark",title:"Semi Dark"},{name:"theme-bordered",title:"Bordered"}],S.LANGUAGES={en:{panel_header:"TEMPLATE CUSTOMIZER",panel_sub_header:"Customize and preview in real time",theming_header:"THEMING",theme_header:"THEME",theme_label:"Themes",style_label:"Style (Mode)",style_switch_light:"Light",style_switch_dark:"Dark",layout_header:"LAYOUT",layout_label:"Layout (Menu)",layout_static:"Static",layout_offcanvas:"Offcanvas",layout_fixed:"Fixed",layout_fixed_offcanvas:"Fixed offcanvas",layout_flipped_label:"Menu flipped",layout_dd_open_label:"Dropdown on hover",layout_navbar_label:"Fixed navbar",layout_footer_label:"Fixed footer",misc_header:"MISC",rtl_label:"RTL direction"},fr:{panel_header:"MODÈLE DE PERSONNALISATION",panel_sub_header:"Personnalisez et prévisualisez en temps réel",theming_header:"THÉMATISATION",theme_header:"THÈME",theme_label:"Thèmes",style_label:"Style (Mode)",style_switch_light:"Léger",style_switch_dark:"Sombre",layout_header:"DISPOSITION",layout_label:"Mise en page (Menu)",layout_static:"Statique",layout_offcanvas:"Hors toile",layout_fixed:"Fixé",layout_fixed_offcanvas:"Fixe hors toile",layout_flipped_label:"Menu inversé",layout_dd_open_label:"Liste déroulante au survol",layout_navbar_label:"Barre de navigation fixe",layout_footer_label:"Pied de page fixe",misc_header:"DIVERS",rtl_label:"Sens RTL"},de:{panel_header:"VORLAGEN-ANPASSER",panel_sub_header:"Anpassen und Vorschau in Echtzeit",theming_header:"THEMEN",theme_header:"THEMA",theme_label:"Themen",style_label:"Stil (Modus)",style_switch_light:"Hell",style_switch_dark:"Dunkel",layout_header:"LAYOUT",layout_label:"Layout (Speisekarte)",layout_static:"Statisch",layout_offcanvas:"Leinwand",layout_fixed:"Fest",layout_fixed_offcanvas:"Außerhalb der Leinwand behoben",layout_flipped_label:"Menü umgedreht",layout_dd_open_label:"Dropdown beim Hover",layout_navbar_label:"Navigationsleiste behoben",layout_footer_label:"Feste Fußzeile",misc_header:"VERSCHIEDENES",rtl_label:"RTL-Regie"},pt:{panel_header:"PERSONALIZADOR DE MODELO",panel_sub_header:"Personalize e visualize em tempo real",theming_header:"TEMAS",theme_header:"TEMA",theme_label:"Temas",style_label:"Estilo (Modo)",style_switch_light:"Luz",style_switch_dark:"Escuro",layout_header:"ESQUEMA",layout_label:"Esquema (Cardápio)",layout_static:"Estático",layout_offcanvas:"Offcanvas",layout_fixed:"Fixo",layout_fixed_offcanvas:"Offscreen fixo",layout_flipped_label:"Menu invertido",layout_dd_open_label:"Suspensão ao passar o mouse",layout_navbar_label:"Barra de navegação fixa",layout_footer_label:"Rodapé fixo",misc_header:"DIVERSOS",rtl_label:"Direção RTL"}}}();var o=window;for(var a in i)o[a]=i[a];i.__esModule&&Object.defineProperty(o,"__esModule",{value:!0})}();