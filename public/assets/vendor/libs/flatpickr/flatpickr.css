.flatpickr-calendar{-webkit-animation:none;animation:none;border:0;box-sizing:border-box;max-height:0;opacity:0;outline:0;overflow:hidden;padding:0;position:absolute;text-align:center;-ms-touch-action:manipulation;touch-action:manipulation;visibility:hidden}.flatpickr-calendar.inline,.flatpickr-calendar.open{max-height:640px;opacity:1;overflow:visible;visibility:visible}.flatpickr-calendar.open{display:inline-block}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar:not(.inline):not(.open){display:none!important}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{display:block;z-index:999}.flatpickr-calendar.hasWeeks{width:auto}html:not([dir=rtl]) .flatpickr-calendar.hasWeeks .flatpickr-days{border-bottom-left-radius:0!important}[dir=rtl] .flatpickr-calendar.hasWeeks .flatpickr-days{border-bottom-right-radius:0!important}.flatpickr-calendar.hasTime .flatpickr-time{height:40px}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar input[type=number]{-moz-appearance:textfield}.flatpickr-calendar input[type=number]::-webkit-inner-spin-button,.flatpickr-calendar input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.flatpickr-wrapper{display:inline-block;position:relative}.flatpickr-month{height:3.15rem;line-height:1;overflow:hidden;position:relative;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.flatpickr-next-month,.flatpickr-prev-month{-ms-flex-align:center;-ms-flex-pack:center;align-items:center;border-radius:50rem;cursor:pointer;display:-ms-flexbox;display:flex;height:1.75rem;justify-content:center;padding:0 .41rem;position:absolute;text-decoration:none;top:.75rem;width:1.75rem;z-index:3}.flatpickr-next-month svg,.flatpickr-prev-month svg{vertical-align:middle}.flatpickr-next-month i,.flatpickr-prev-month i{position:relative}.flatpickr-prev-month.flatpickr-prev-month{right:3.5rem}[dir=rtl] .flatpickr-prev-month{left:3.5rem;right:auto;-webkit-transform:scaleX(-1);transform:scaleX(-1)}.flatpickr-next-month.flatpickr-prev-month{left:0;right:0}.flatpickr-next-month.flatpickr-next-month{right:1rem}[dir=rtl] .flatpickr-next-month{left:1rem;right:auto;-webkit-transform:scaleX(-1);transform:scaleX(-1)}.flatpickr-next-month:hover,.flatpickr-prev-month:hover{opacity:1}.flatpickr-next-month svg,.flatpickr-prev-month svg{width:.6rem}.flatpickr-next-month svg path,.flatpickr-prev-month svg path{fill:inherit;transition:fill .1s}.numInputWrapper{height:auto;position:relative}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper span{-ms-flex-align:center;-ms-flex-pack:center;align-items:center;box-sizing:border-box;cursor:pointer;display:-ms-flexbox;display:flex;height:50%;justify-content:center;line-height:1;opacity:0;position:absolute;right:0;width:14px}[dir=rtl] .numInputWrapper span{left:0;right:auto}.numInputWrapper span:hover{background:rgba(0,0,0,.1)}.numInputWrapper span:active{background:rgba(0,0,0,.2)}.numInputWrapper span:after{content:"";display:block;height:0;width:0}.numInputWrapper span.arrowUp{top:0}.numInputWrapper span.arrowUp:after{border-bottom:4px solid rgba(72,72,72,.6);border-left:4px solid transparent;border-right:4px solid transparent}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(72,72,72,.6)}.numInputWrapper span svg{height:auto;width:inherit}.numInputWrapper span svg path{fill:hsla(0,0%,100%,.5)}.numInputWrapper:hover{background:rgba(0,0,0,.05)}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{-ms-flex-align:center;align-items:center;display:inline-block;display:-ms-flexbox;display:flex;font-weight:600;left:1rem;line-height:1;position:absolute;text-align:left;top:1.15rem;-webkit-transform:translateZ(0);transform:translateZ(0)}[dir=rtl] .flatpickr-current-month{left:auto;right:1rem}.flatpickr-current-month .flatpickr-monthDropdown-months,.flatpickr-current-month input.cur-year{background:transparent;border:0;border-radius:0;box-sizing:border-box;color:inherit;display:inline-block;font-family:inherit;font-size:inherit;font-weight:600;line-height:inherit;outline:none;padding:0 0 0 .5ch;vertical-align:middle!important}.flatpickr-current-month .numInputWrapper{display:inline-block;width:6ch}.flatpickr-current-month .flatpickr-monthDropdown-months{-webkit-appearance:menulist;-moz-appearance:menulist;appearance:menulist;cursor:pointer;padding:0;position:relative}.flatpickr-current-month input.cur-year{cursor:default;height:1rem;margin:0}[dir=rtl] .flatpickr-current-month input.cur-year{padding-left:0;padding-right:.5ch}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{background:transparent;pointer-events:none}.flatpickr-current-month input.cur-year[disabled]{opacity:.5}.flatpickr-weekdaycontainer{display:-ms-flexbox;display:flex;padding:1rem .875rem 0;width:100%}.flatpickr-weekdays{-ms-flex-align:center;align-items:center;display:-ms-flexbox;display:flex;height:1.75rem;overflow:hidden;text-align:center;width:100%}span.flatpickr-weekday{cursor:default;display:block;-ms-flex:1;flex:1;line-height:1;margin:0;text-align:center}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{display:-ms-flexbox;display:flex;overflow:hidden;position:relative;width:auto!important}.flatpickr-days:focus{outline:0}.flatpickr-calendar.hasTime .flatpickr-days{border-bottom:0!important;border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}.dayContainer{-ms-flex-pack:distribute;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;justify-content:space-around;max-width:14.875rem;min-width:14.875rem;opacity:1;outline:0;padding:0;-webkit-transform:translateZ(0);transform:translateZ(0);width:14.875rem}.flatpickr-day{-ms-flex-preferred-size:14.2857143%;-ms-flex-pack:center;background:none;border:1px solid transparent;box-sizing:border-box;cursor:pointer;display:inline-block;flex-basis:14.2857143%;font-weight:400;height:2.125rem;justify-content:center;line-height:calc(2.125rem - 2px);margin:0;max-width:2.125rem;position:relative;text-align:center;width:14.2857143%}.flatpickr-day.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day.nextMonthDay:focus,.flatpickr-day.nextMonthDay:hover,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.today.inRange,.flatpickr-day:focus,.flatpickr-day:hover{cursor:pointer;outline:0}.flatpickr-day.inRange:not(.startRange):not(.endRange){border:transparent;border-radius:0!important}.flatpickr-day.disabled,.flatpickr-day.disabled:hover{pointer-events:none}.flatpickr-day.disabled,.flatpickr-day.disabled:hover,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.nextMonthDay,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.prevMonthDay{background:transparent;border-color:transparent;cursor:default}.flatpickr-day.week.selected{border-radius:0}html:not([dir=rtl]) .flatpickr-day.endRange.startRange,html:not([dir=rtl]) .flatpickr-day.selected.startRange,html:not([dir=rtl]) .flatpickr-day.startRange.startRange{border-bottom-right-radius:0;border-top-right-radius:0}[dir=rtl] .flatpickr-day.endRange.startRange,[dir=rtl] .flatpickr-day.selected.startRange,[dir=rtl] .flatpickr-day.startRange.startRange,html:not([dir=rtl]) .flatpickr-day.endRange.endRange,html:not([dir=rtl]) .flatpickr-day.selected.endRange,html:not([dir=rtl]) .flatpickr-day.startRange.endRange{border-bottom-left-radius:0;border-top-left-radius:0}[dir=rtl] .flatpickr-day.endRange.endRange,[dir=rtl] .flatpickr-day.selected.endRange,[dir=rtl] .flatpickr-day.startRange.endRange{border-bottom-right-radius:0;border-top-right-radius:0}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{display:inline-block;float:left}.flatpickr-weekwrapper .flatpickr-weeks{background-clip:padding-box!important;padding:.75rem 0}html:not([dir=rtl]) .flatpickr-weekwrapper .flatpickr-weeks .flatpickr-weeks{border-bottom-right-radius:0!important}[dir=rtl] .flatpickr-weekwrapper .flatpickr-weeks .flatpickr-weeks{border-bottom-left-radius:0!important}.flatpickr-weekwrapper .flatpickr-calendar.hasTime .flatpickr-weeks{border-bottom:0!important;border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}.flatpickr-weekwrapper .flatpickr-weekday{float:none;line-height:1.75rem;position:relative;top:.5652rem;width:100%}.flatpickr-weekwrapper span.flatpickr-day{background:none!important;display:block;max-width:none;width:2.125rem}.flatpickr-calendar.hasTime .flatpickr-weeks{border-bottom:0!important;border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}.flatpickr-innerContainer{box-sizing:border-box;display:block;display:-ms-flexbox;display:flex;overflow:hidden}.flatpickr-rContainer{box-sizing:border-box;display:inline-block;padding:0}.flatpickr-time{background-clip:padding-box!important;box-sizing:border-box;display:block;display:-ms-flexbox;display:flex;height:0;line-height:40px;max-height:40px;outline:0;overflow:hidden;text-align:center}.flatpickr-time:after{clear:both;content:"";display:table}.flatpickr-time .numInputWrapper{-ms-flex:1;flex:1;float:left;height:40px;width:40%}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;border:0;border-radius:0;box-shadow:none;box-sizing:border-box;cursor:pointer;height:inherit;line-height:inherit;margin:0;padding:0;position:relative;text-align:center}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{border:0;outline:0}.flatpickr-time .flatpickr-am-pm,.flatpickr-time .flatpickr-time-separator{-ms-flex-item-align:center;align-self:center;display:inline-block;float:left;height:inherit;line-height:inherit;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:2%}.flatpickr-time .flatpickr-am-pm{cursor:pointer;font-weight:400;outline:0;text-align:center;width:18%}.flatpickr-time .flatpickr-am-pm:hover{background:rgba(0,0,0,.05)}.flatpickr-calendar.noCalendar .flatpickr-time{box-shadow:none!important}.flatpickr-calendar:not(.noCalendar) .flatpickr-time{border-top:0;border-top-left-radius:0!important;border-top-right-radius:0!important}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}.light-style .flatpickr-calendar{background:#fff}.light-style .flatpickr-next-month,.light-style .flatpickr-prev-month{background-color:#f1f0f2}.light-style .flatpickr-next-month svg,.light-style .flatpickr-prev-month svg{fill:#6f6b7d;stroke:#6f6b7d}.light-style .flatpickr-calendar,.light-style .flatpickr-days{width:16.625rem!important}.light-style .flatpickr-calendar.hasWeeks{width:18.75rem!important}.light-style .flatpickr-calendar.open{z-index:1091}.light-style .flatpickr-input[readonly],.light-style .flatpickr-input~.form-control[readonly]{background:#fff}.light-style .flatpickr-days{background:#fff;background-clip:padding-box;border:0 solid #dbdade;border-bottom-left-radius:.375rem;border-bottom-right-radius:.375rem;border-top:0;padding:.75rem .875rem}.light-style:not([dir=rtl]) .flatpickr-calendar.hasWeeks .flatpickr-days{border-left:0;box-shadow:inset 0 0 0 #dbdade;padding-left:.875rem}.light-style[dir=rtl] .flatpickr-calendar.hasWeeks .flatpickr-days{border-right:0;box-shadow:inset 0 0 0 #dbdade;padding-right:.875rem}.light-style .flatpickr-calendar{border-radius:.375rem;box-shadow:0 .25rem 1rem hsla(251,6%,66%,.45);line-height:1.47}.light-style .flatpickr-calendar.hasTime:not(.noCalendar):not(.hasTime) .flatpickr-time{display:none!important}.light-style .flatpickr-calendar.hasTime .flatpickr-time{box-shadow:inset 0 1px 0 #dbdade}.light-style .flatpickr-month{border-bottom:1px solid #dbdade;border-top-left-radius:.375rem;border-top-right-radius:.375rem}.light-style .flatpickr-month option.flatpickr-monthDropdown-month{background:#fff;color:#6f6b7d}.light-style span.flatpickr-weekday{background:#fff;color:#5d596c;font-size:.8125rem;font-weight:600}.light-style .flatpickr-day{border-radius:50rem;color:#6f6b7d}.light-style .flatpickr-day.nextMonthDay:focus,.light-style .flatpickr-day.nextMonthDay:hover,.light-style .flatpickr-day.prevMonthDay:focus,.light-style .flatpickr-day.prevMonthDay:hover,.light-style .flatpickr-day.today:focus,.light-style .flatpickr-day.today:hover,.light-style .flatpickr-day:focus,.light-style .flatpickr-day:hover{background:#f1f0f2}.light-style .flatpickr-day.nextMonthDay:focus:not(.today),.light-style .flatpickr-day.nextMonthDay:hover:not(.today),.light-style .flatpickr-day.prevMonthDay:focus:not(.today),.light-style .flatpickr-day.prevMonthDay:hover:not(.today),.light-style .flatpickr-day.today:focus:not(.today),.light-style .flatpickr-day.today:hover:not(.today),.light-style .flatpickr-day:focus:not(.today),.light-style .flatpickr-day:hover:not(.today){border-color:transparent}.light-style .flatpickr-day.flatpickr-disabled,.light-style .flatpickr-day.nextMonthDay,.light-style .flatpickr-day.prevMonthDay{color:#a5a3ae}.light-style .flatpickr-day.flatpickr-disabled.today,.light-style .flatpickr-day.nextMonthDay.today,.light-style .flatpickr-day.prevMonthDay.today{border:none}.light-style .flatpickr-day.disabled{color:#b7b5be!important}.light-style .flatpickr-day.selected.startRange.endRange{border-radius:.375rem!important}.light-style .flatpickr-day.selected{box-shadow:0 .125rem .25rem hsla(251,6%,66%,.3)}.light-style .flatpickr-weeks{background:#fff;border-bottom:0 solid #dbdade;border-bottom-left-radius:.375rem;border-bottom-right-radius:.375rem;border-bottom-right-radius:0;border-left:0 solid #dbdade}.light-style[dir=rtl] .flatpickr-weeks{border-bottom-left-radius:.375rem;border-bottom-left-radius:0;border-bottom-right-radius:.375rem;border-left:0;border-right:0 solid #dbdade}.light-style .flatpickr-time{background:#fff;border:0 solid #dbdade;border-radius:.375rem}.light-style .flatpickr-time input{color:#6f6b7d}.light-style .flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#a5a3ae}.light-style .flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#a5a3ae}.light-style .flatpickr-time .flatpickr-am-pm,.light-style .flatpickr-time .flatpickr-time-separator{color:#6f6b7d}.dark-style .flatpickr-calendar{background:#2f3349}.dark-style .flatpickr-next-month,.dark-style .flatpickr-prev-month{background-color:#363b54}.dark-style .flatpickr-next-month svg,.dark-style .flatpickr-prev-month svg{fill:#b6bee3;stroke:#b6bee3}.dark-style .flatpickr-calendar,.dark-style .flatpickr-days{width:16.625rem!important}.dark-style .flatpickr-calendar.hasWeeks,.dark-style .flatpickr-calendar.hasWeeks .flatpickr-days{width:18.75rem!important}.dark-style .flatpickr-calendar.open{z-index:1091}.dark-style .flatpickr-input:not(.is-invalid):not(.is-valid)[readonly],.dark-style .flatpickr-input:not(.is-invalid):not(.is-valid)~.form-control:disabled,.dark-style .flatpickr-input:not(.is-invalid):not(.is-valid)~.form-control[readonly]{background-color:#2f3349}.dark-style .flatpickr-days{background:#2f3349;background-clip:padding-box;border:0 solid #434968;border-bottom-left-radius:.375rem;border-bottom-right-radius:.375rem;border-top:0;padding:.75rem .875rem}.dark-style:not([dir=rtl]) .flatpickr-calendar.hasWeeks .flatpickr-days{border-left:0;box-shadow:inset 0 0 0 #434968;padding-left:.875rem}.dark-style[dir=rtl] .flatpickr-calendar.hasWeeks .flatpickr-days{border-right:0;box-shadow:inset 0 0 0 #434968;padding-right:.875rem}.dark-style .flatpickr-calendar{border-radius:.375rem;box-shadow:0 .25rem 1rem rgba(15,20,34,.55);line-height:1.47}.dark-style .flatpickr-calendar.hasTime:not(.noCalendar):not(.hasTime) .flatpickr-time{display:none!important}.dark-style .flatpickr-calendar.hasTime .flatpickr-time{box-shadow:inset 0 1px 0 #434968}.dark-style .flatpickr-month{border-bottom:1px solid #434968;border-top-left-radius:.375rem;border-top-right-radius:.375rem}.dark-style .flatpickr-month option.flatpickr-monthDropdown-month{background:#2f3349;color:#b6bee3}.dark-style span.flatpickr-weekday{background:#2f3349;color:#cfd3ec;font-size:.8125rem;font-weight:600}.dark-style .flatpickr-day{border-radius:50rem;color:#b6bee3}.dark-style .flatpickr-day.nextMonthDay:focus,.dark-style .flatpickr-day.nextMonthDay:hover,.dark-style .flatpickr-day.prevMonthDay:focus,.dark-style .flatpickr-day.prevMonthDay:hover,.dark-style .flatpickr-day.today:focus,.dark-style .flatpickr-day.today:hover,.dark-style .flatpickr-day:focus,.dark-style .flatpickr-day:hover{background:#363b54;border-color:transparent}.dark-style .flatpickr-day.flatpickr-disabled,.dark-style .flatpickr-day.nextMonthDay,.dark-style .flatpickr-day.prevMonthDay{color:#7983bb}.dark-style .flatpickr-day.flatpickr-disabled.today,.dark-style .flatpickr-day.nextMonthDay.today,.dark-style .flatpickr-day.prevMonthDay.today{border:0}.dark-style .flatpickr-day.selected.startRange.endRange{border-radius:.375rem!important}.dark-style .flatpickr-day.disabled{color:#354288!important}.dark-style .flatpickr-day.selected{box-shadow:0 .125rem .25rem rgba(15,20,34,.4)}.dark-style .flatpickr-weeks{background:#2f3349;border-bottom:0 solid #434968;border-bottom-left-radius:.375rem;border-bottom-right-radius:.375rem;border-bottom-right-radius:0;border-left:0 solid #434968}.dark-style[dir=rtl] .flatpickr-weeks{border-left:0;border-right:0 solid #434968}.dark-style .flatpickr-time{background:#2f3349;border:0 solid #434968;border-radius:.375rem}.dark-style .flatpickr-time input{color:#b6bee3}.dark-style .flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#7983bb}.dark-style .flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#7983bb}.dark-style .flatpickr-time .flatpickr-am-pm,.dark-style .flatpickr-time .flatpickr-time-separator{color:#b6bee3}
