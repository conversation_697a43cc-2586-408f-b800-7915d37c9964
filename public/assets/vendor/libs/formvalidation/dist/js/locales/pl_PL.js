(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, (global.FormValidation = global.FormValidation || {}, global.FormValidation.locales = global.FormValidation.locales || {}, global.FormValidation.locales.pl_PL = factory()));
})(this, (function () { 'use strict';

    /**
     * Polish language package
     * Translated by @grzesiek
     */

    var pl_PL = {
        base64: {
            default: 'Wpisz poprawny ciąg znaków zakodowany w base 64',
        },
        between: {
            default: 'Wprowadź wartość pomiędzy %s i %s',
            notInclusive: 'Wprowadź wartość pomiędzy %s i %s (zbiór otwarty)',
        },
        bic: {
            default: 'Wprowadź poprawny numer BIC',
        },
        callback: {
            default: 'Wprowadź poprawną wartość',
        },
        choice: {
            between: 'Wybierz przynajmniej %s i maksymalnie %s opcji',
            default: 'Wprowadź poprawną wartość',
            less: 'Wybierz przynajmniej %s opcji',
            more: 'Wybierz maksymalnie %s opcji',
        },
        color: {
            default: 'Wprowadź poprawny kolor w formacie',
        },
        creditCard: {
            default: 'Wprowadź poprawny numer karty kredytowej',
        },
        cusip: {
            default: 'Wprowadź poprawny numer CUSIP',
        },
        date: {
            default: 'Wprowadź poprawną datę',
            max: 'Wprowadź datę przed  %s',
            min: 'Wprowadź datę po %s',
            range: 'Wprowadź datę pomiędzy %s i %s',
        },
        different: {
            default: 'Wprowadź inną wartość',
        },
        digits: {
            default: 'Wprowadź tylko cyfry',
        },
        ean: {
            default: 'Wprowadź poprawny numer EAN',
        },
        ein: {
            default: 'Wprowadź poprawny numer EIN',
        },
        emailAddress: {
            default: 'Wprowadź poprawny adres e-mail',
        },
        file: {
            default: 'Wybierz prawidłowy plik',
        },
        greaterThan: {
            default: 'Wprowadź wartość większą bądź równą %s',
            notInclusive: 'Wprowadź wartość większą niż %s',
        },
        grid: {
            default: 'Wprowadź poprawny numer GRId',
        },
        hex: {
            default: 'Wprowadź poprawną liczbę w formacie heksadecymalnym',
        },
        iban: {
            countries: {
                AD: 'Andora',
                AE: 'Zjednoczone Emiraty Arabskie',
                AL: 'Albania',
                AO: 'Angola',
                AT: 'Austria',
                AZ: 'Azerbejdżan',
                BA: 'Bośnia i Hercegowina',
                BE: 'Belgia',
                BF: 'Burkina Faso',
                BG: 'Bułgaria',
                BH: 'Bahrajn',
                BI: 'Burundi',
                BJ: 'Benin',
                BR: 'Brazylia',
                CH: 'Szwajcaria',
                CI: 'Wybrzeże Kości Słoniowej',
                CM: 'Kamerun',
                CR: 'Kostaryka',
                CV: 'Republika Zielonego Przylądka',
                CY: 'Cypr',
                CZ: 'Czechy',
                DE: 'Niemcy',
                DK: 'Dania',
                DO: 'Dominikana',
                DZ: 'Algeria',
                EE: 'Estonia',
                ES: 'Hiszpania',
                FI: 'Finlandia',
                FO: 'Wyspy Owcze',
                FR: 'Francja',
                GB: 'Wielka Brytania',
                GE: 'Gruzja',
                GI: 'Gibraltar',
                GL: 'Grenlandia',
                GR: 'Grecja',
                GT: 'Gwatemala',
                HR: 'Chorwacja',
                HU: 'Węgry',
                IE: 'Irlandia',
                IL: 'Izrael',
                IR: 'Iran',
                IS: 'Islandia',
                IT: 'Włochy',
                JO: 'Jordania',
                KW: 'Kuwejt',
                KZ: 'Kazahstan',
                LB: 'Liban',
                LI: 'Liechtenstein',
                LT: 'Litwa',
                LU: 'Luksemburg',
                LV: 'Łotwa',
                MC: 'Monako',
                MD: 'Mołdawia',
                ME: 'Czarnogóra',
                MG: 'Madagaskar',
                MK: 'Macedonia',
                ML: 'Mali',
                MR: 'Mauretania',
                MT: 'Malta',
                MU: 'Mauritius',
                MZ: 'Mozambik',
                NL: 'Holandia',
                NO: 'Norwegia',
                PK: 'Pakistan',
                PL: 'Polska',
                PS: 'Palestyna',
                PT: 'Portugalia',
                QA: 'Katar',
                RO: 'Rumunia',
                RS: 'Serbia',
                SA: 'Arabia Saudyjska',
                SE: 'Szwecja',
                SI: 'Słowenia',
                SK: 'Słowacja',
                SM: 'San Marino',
                SN: 'Senegal',
                TL: 'Timor Wschodni',
                TN: 'Tunezja',
                TR: 'Turcja',
                VG: 'Brytyjskie Wyspy Dziewicze',
                XK: 'Republika Kosowa',
            },
            country: 'Wprowadź poprawny numer IBAN w kraju %s',
            default: 'Wprowadź poprawny numer IBAN',
        },
        id: {
            countries: {
                BA: 'Bośnia i Hercegowina',
                BG: 'Bułgaria',
                BR: 'Brazylia',
                CH: 'Szwajcaria',
                CL: 'Chile',
                CN: 'Chiny',
                CZ: 'Czechy',
                DK: 'Dania',
                EE: 'Estonia',
                ES: 'Hiszpania',
                FI: 'Finlandia',
                HR: 'Chorwacja',
                IE: 'Irlandia',
                IS: 'Islandia',
                LT: 'Litwa',
                LV: 'Łotwa',
                ME: 'Czarnogóra',
                MK: 'Macedonia',
                NL: 'Holandia',
                PL: 'Polska',
                RO: 'Rumunia',
                RS: 'Serbia',
                SE: 'Szwecja',
                SI: 'Słowenia',
                SK: 'Słowacja',
                SM: 'San Marino',
                TH: 'Tajlandia',
                TR: 'Turcja',
                ZA: 'Republika Południowej Afryki',
            },
            country: 'Wprowadź poprawny numer identyfikacyjny w kraju %s',
            default: 'Wprowadź poprawny numer identyfikacyjny',
        },
        identical: {
            default: 'Wprowadź taką samą wartość',
        },
        imei: {
            default: 'Wprowadź poprawny numer IMEI',
        },
        imo: {
            default: 'Wprowadź poprawny numer IMO',
        },
        integer: {
            default: 'Wprowadź poprawną liczbę całkowitą',
        },
        ip: {
            default: 'Wprowadź poprawny adres IP',
            ipv4: 'Wprowadź poprawny adres IPv4',
            ipv6: 'Wprowadź poprawny adres IPv6',
        },
        isbn: {
            default: 'Wprowadź poprawny numer ISBN',
        },
        isin: {
            default: 'Wprowadź poprawny numer ISIN',
        },
        ismn: {
            default: 'Wprowadź poprawny numer ISMN',
        },
        issn: {
            default: 'Wprowadź poprawny numer ISSN',
        },
        lessThan: {
            default: 'Wprowadź wartość mniejszą bądź równą %s',
            notInclusive: 'Wprowadź wartość mniejszą niż %s',
        },
        mac: {
            default: 'Wprowadź poprawny adres MAC',
        },
        meid: {
            default: 'Wprowadź poprawny numer MEID',
        },
        notEmpty: {
            default: 'Wprowadź wartość, pole nie może być puste',
        },
        numeric: {
            default: 'Wprowadź poprawną liczbę zmiennoprzecinkową',
        },
        phone: {
            countries: {
                AE: 'Zjednoczone Emiraty Arabskie',
                BG: 'Bułgaria',
                BR: 'Brazylia',
                CN: 'Chiny',
                CZ: 'Czechy',
                DE: 'Niemcy',
                DK: 'Dania',
                ES: 'Hiszpania',
                FR: 'Francja',
                GB: 'Wielka Brytania',
                IN: 'Indie',
                MA: 'Maroko',
                NL: 'Holandia',
                PK: 'Pakistan',
                RO: 'Rumunia',
                RU: 'Rosja',
                SK: 'Słowacja',
                TH: 'Tajlandia',
                US: 'USA',
                VE: 'Wenezuela',
            },
            country: 'Wprowadź poprawny numer telefonu w kraju %s',
            default: 'Wprowadź poprawny numer telefonu',
        },
        promise: {
            default: 'Wprowadź poprawną wartość',
        },
        regexp: {
            default: 'Wprowadź wartość pasującą do wzoru',
        },
        remote: {
            default: 'Wprowadź poprawną wartość',
        },
        rtn: {
            default: 'Wprowadź poprawny numer RTN',
        },
        sedol: {
            default: 'Wprowadź poprawny numer SEDOL',
        },
        siren: {
            default: 'Wprowadź poprawny numer SIREN',
        },
        siret: {
            default: 'Wprowadź poprawny numer SIRET',
        },
        step: {
            default: 'Wprowadź wielokrotność %s',
        },
        stringCase: {
            default: 'Wprowadź tekst składającą się tylko z małych liter',
            upper: 'Wprowadź tekst składający się tylko z dużych liter',
        },
        stringLength: {
            between: 'Wprowadź wartość składająca się z min %s i max %s znaków',
            default: 'Wprowadź wartość o poprawnej długości',
            less: 'Wprowadź mniej niż %s znaków',
            more: 'Wprowadź więcej niż %s znaków',
        },
        uri: {
            default: 'Wprowadź poprawny URI',
        },
        uuid: {
            default: 'Wprowadź poprawny numer UUID',
            version: 'Wprowadź poprawny numer UUID w wersji %s',
        },
        vat: {
            countries: {
                AT: 'Austria',
                BE: 'Belgia',
                BG: 'Bułgaria',
                BR: 'Brazylia',
                CH: 'Szwajcaria',
                CY: 'Cypr',
                CZ: 'Czechy',
                DE: 'Niemcy',
                DK: 'Dania',
                EE: 'Estonia',
                EL: 'Grecja',
                ES: 'Hiszpania',
                FI: 'Finlandia',
                FR: 'Francja',
                GB: 'Wielka Brytania',
                GR: 'Grecja',
                HR: 'Chorwacja',
                HU: 'Węgry',
                IE: 'Irlandia',
                IS: 'Islandia',
                IT: 'Włochy',
                LT: 'Litwa',
                LU: 'Luksemburg',
                LV: 'Łotwa',
                MT: 'Malta',
                NL: 'Holandia',
                NO: 'Norwegia',
                PL: 'Polska',
                PT: 'Portugalia',
                RO: 'Rumunia',
                RS: 'Serbia',
                RU: 'Rosja',
                SE: 'Szwecja',
                SI: 'Słowenia',
                SK: 'Słowacja',
                VE: 'Wenezuela',
                ZA: 'Republika Południowej Afryki',
            },
            country: 'Wprowadź poprawny numer VAT w kraju %s',
            default: 'Wprowadź poprawny numer VAT',
        },
        vin: {
            default: 'Wprowadź poprawny numer VIN',
        },
        zipCode: {
            countries: {
                AT: 'Austria',
                BG: 'Bułgaria',
                BR: 'Brazylia',
                CA: 'Kanada',
                CH: 'Szwajcaria',
                CZ: 'Czechy',
                DE: 'Niemcy',
                DK: 'Dania',
                ES: 'Hiszpania',
                FR: 'Francja',
                GB: 'Wielka Brytania',
                IE: 'Irlandia',
                IN: 'Indie',
                IT: 'Włochy',
                MA: 'Maroko',
                NL: 'Holandia',
                PL: 'Polska',
                PT: 'Portugalia',
                RO: 'Rumunia',
                RU: 'Rosja',
                SE: 'Szwecja',
                SG: 'Singapur',
                SK: 'Słowacja',
                US: 'USA',
            },
            country: 'Wprowadź poprawny kod pocztowy w kraju %s',
            default: 'Wprowadź poprawny kod pocztowy',
        },
    };

    return pl_PL;

}));
