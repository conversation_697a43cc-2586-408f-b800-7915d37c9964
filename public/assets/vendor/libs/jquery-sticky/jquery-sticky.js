!function(){var t={26658:function(t,e,n){var r,i,o;i=[n(19567)],r=function(t){var e=Array.prototype.slice,n=Array.prototype.splice,r={topSpacing:0,bottomSpacing:0,className:"is-sticky",wrapperClassName:"sticky-wrapper",center:!1,getWidthFrom:"",widthFromWrapper:!0,responsiveWidth:!1,zIndex:"auto"},i=t(window),o=t(document),s=[],c=i.height(),a=function(){for(var e=i.scrollTop(),n=o.height(),r=n-c,a=e>r?r-e:0,p=0,d=s.length;p<d;p++){var u=s[p],l=u.stickyWrapper.offset().top-u.topSpacing-a;if(u.stickyWrapper.css("height",u.stickyElement.outerHeight()),e<=l)null!==u.currentTop&&(u.stickyElement.css({width:"",position:"",top:"","z-index":""}),u.stickyElement.parent().removeClass(u.className),u.stickyElement.trigger("sticky-end",[u]),u.currentTop=null);else{var h,f=n-u.stickyElement.outerHeight()-u.topSpacing-u.bottomSpacing-e-a;f<0?f+=u.topSpacing:f=u.topSpacing,u.currentTop!==f&&(u.getWidthFrom?h=t(u.getWidthFrom).width()||null:u.widthFromWrapper&&(h=u.stickyWrapper.width()),null==h&&(h=u.stickyElement.width()),u.stickyElement.css("width",h).css("position","fixed").css("top",f).css("z-index",u.zIndex),u.stickyElement.parent().addClass(u.className),null===u.currentTop?u.stickyElement.trigger("sticky-start",[u]):u.stickyElement.trigger("sticky-update",[u]),u.currentTop===u.topSpacing&&u.currentTop>f||null===u.currentTop&&f<u.topSpacing?u.stickyElement.trigger("sticky-bottom-reached",[u]):null!==u.currentTop&&f===u.topSpacing&&u.currentTop<f&&u.stickyElement.trigger("sticky-bottom-unreached",[u]),u.currentTop=f);var y=u.stickyWrapper.parent();u.stickyElement.offset().top+u.stickyElement.outerHeight()>=y.offset().top+y.outerHeight()&&u.stickyElement.offset().top<=u.topSpacing?u.stickyElement.css("position","absolute").css("top","").css("bottom",0).css("z-index",""):u.stickyElement.css("position","fixed").css("top",f).css("bottom","").css("z-index",u.zIndex)}}},p=function(){c=i.height();for(var e=0,n=s.length;e<n;e++){var r=s[e],o=null;r.getWidthFrom?r.responsiveWidth&&(o=t(r.getWidthFrom).width()):r.widthFromWrapper&&(o=r.stickyWrapper.width()),null!=o&&r.stickyElement.css("width",o)}},d={init:function(e){var n=t.extend({},r,e);return this.each((function(){var e=t(this),i=e.attr("id"),o=i?i+"-"+r.wrapperClassName:r.wrapperClassName,c=t("<div></div>").attr("id",o).addClass(n.wrapperClassName);e.wrapAll(c);var a=e.parent();n.center&&a.css({width:e.outerWidth(),marginLeft:"auto",marginRight:"auto"}),"right"===e.css("float")&&e.css({float:"none"}).parent().css({float:"right"}),n.stickyElement=e,n.stickyWrapper=a,n.currentTop=null,s.push(n),d.setWrapperHeight(this),d.setupChangeListeners(this)}))},setWrapperHeight:function(e){var n=t(e),r=n.parent();r&&r.css("height",n.outerHeight())},setupChangeListeners:function(t){window.MutationObserver?new window.MutationObserver((function(e){(e[0].addedNodes.length||e[0].removedNodes.length)&&d.setWrapperHeight(t)})).observe(t,{subtree:!0,childList:!0}):(t.addEventListener("DOMNodeInserted",(function(){d.setWrapperHeight(t)}),!1),t.addEventListener("DOMNodeRemoved",(function(){d.setWrapperHeight(t)}),!1))},update:a,unstick:function(e){return this.each((function(){for(var e=this,r=t(e),i=-1,o=s.length;o-- >0;)s[o].stickyElement.get(0)===e&&(n.call(s,o,1),i=o);-1!==i&&(r.unwrap(),r.css({width:"",position:"",top:"",float:"","z-index":""}))}))}};window.addEventListener?(window.addEventListener("scroll",a,!1),window.addEventListener("resize",p,!1)):window.attachEvent&&(window.attachEvent("onscroll",a),window.attachEvent("onresize",p)),t.fn.sticky=function(n){return d[n]?d[n].apply(this,e.call(arguments,1)):"object"!=typeof n&&n?void t.error("Method "+n+" does not exist on jQuery.sticky"):d.init.apply(this,arguments)},t.fn.unstick=function(n){return d[n]?d[n].apply(this,e.call(arguments,1)):"object"!=typeof n&&n?void t.error("Method "+n+" does not exist on jQuery.sticky"):d.unstick.apply(this,arguments)},t((function(){setTimeout(a,0)}))},void 0===(o="function"==typeof r?r.apply(e,i):r)||(t.exports=o)},19567:function(t){"use strict";t.exports=window.jQuery}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,n),o.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};!function(){"use strict";n.r(r);n(26658)}();var i=window;for(var o in r)i[o]=r[o];r.__esModule&&Object.defineProperty(i,"__esModule",{value:!0})}();