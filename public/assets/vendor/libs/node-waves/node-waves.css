/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 <PERSON><PERSON><PERSON> and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */.waves-effect{-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;display:inline-block;overflow:hidden;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.waves-effect .waves-ripple{background:rgba(0,0,0,.2);background:radial-gradient(rgba(0,0,0,.2) 0,rgba(0,0,0,.3) 40%,rgba(0,0,0,.4) 50%,rgba(0,0,0,.5) 60%,hsla(0,0%,100%,0) 70%);border-radius:50%;height:100px;margin-left:-50px;margin-top:-50px;opacity:0;pointer-events:none;position:absolute;-webkit-transform:scale(0) translate(0);transform:scale(0) translate(0);transition:all .5s ease-out;transition-property:opacity,-webkit-transform;transition-property:transform,opacity;transition-property:transform,opacity,-webkit-transform;width:100px}.waves-effect.waves-light .waves-ripple{background:hsla(0,0%,100%,.4);background:radial-gradient(hsla(0,0%,100%,.2) 0,hsla(0,0%,100%,.3) 40%,hsla(0,0%,100%,.4) 50%,hsla(0,0%,100%,.5) 60%,hsla(0,0%,100%,0) 70%)}.waves-effect.waves-classic .waves-ripple{background:rgba(0,0,0,.2)}.waves-effect.waves-classic.waves-light .waves-ripple{background:hsla(0,0%,100%,.4)}.waves-notransition{transition:none!important}.waves-button,.waves-circle{-webkit-mask-image:-webkit-radial-gradient(circle,#fff 100%,#000 0);-webkit-transform:translateZ(0);transform:translateZ(0)}.waves-button,.waves-button-input,.waves-button:hover,.waves-button:visited{background-color:transparent;border:none;color:inherit;cursor:pointer;font-size:1em;line-height:1em;outline:none;text-align:center;text-decoration:none;vertical-align:middle;white-space:nowrap;z-index:1}.waves-button{border-radius:.2em;padding:.85em 1.1em}.waves-button-input{margin:0;padding:.85em 1.1em}.waves-input-wrapper{border-radius:.2em;vertical-align:bottom}.waves-input-wrapper.waves-button{padding:0}.waves-input-wrapper .waves-button-input{left:0;position:relative;top:0;z-index:1}.waves-circle{border-radius:50%;height:2.5em;line-height:2.5em;text-align:center;width:2.5em}.waves-float{box-shadow:0 1px 1.5px 1px rgba(0,0,0,.12);-webkit-mask-image:none;transition:all .3s}.waves-float:active{box-shadow:0 8px 20px 1px rgba(0,0,0,.3)}.waves-block{display:block}
