.ql-container{display:block;margin:0;position:relative}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-clipboard{height:.0625rem;left:-6250rem;overflow-y:hidden;position:absolute;top:50%}[dir=rtl] .ql-clipboard{left:auto;right:-6250rem}.ql-editor{word-wrap:break-word;box-sizing:border-box;display:block;height:100%;outline:none;overflow-y:auto;-o-tab-size:4;tab-size:4;-moz-tab-size:4;white-space:pre-wrap}.ql-editor>*{cursor:text}.ql-editor.ql-blank:before{content:attr(data-placeholder);font-style:italic;left:0;pointer-events:none;position:absolute;right:0}.ql-bubble,.ql-bubble *,.ql-snow,.ql-snow *{box-sizing:border-box}.ql-bubble .ql-out-bottom,.ql-bubble .ql-out-top,.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-bubble .ql-hidden,.ql-snow .ql-hidden{display:none!important}.ql-bubble .ql-even,.ql-snow .ql-even{fill-rule:evenodd}.ql-bubble .ql-empty,.ql-snow .ql-empty{fill:none}.ql-bubble .ql-transparent,.ql-snow .ql-transparent{opacity:.4}.ql-bubble .ql-stroke.ql-thin,.ql-bubble .ql-thin,.ql-snow .ql-stroke.ql-thin,.ql-snow .ql-thin{stroke-width:1}.ql-bubble .ql-editor a,.ql-snow .ql-editor a{text-decoration:underline}.ql-bubble .ql-direction.ql-active svg:last-child,.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-bubble .ql-direction svg:last-child,.ql-bubble .ql-direction.ql-active svg:first-child,.ql-snow .ql-direction svg:last-child,.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-bubble .ql-toolbar,.ql-bubble.ql-toolbar,.ql-snow .ql-toolbar,.ql-snow.ql-toolbar{box-sizing:border-box;padding:.5rem}.ql-bubble .ql-toolbar:after,.ql-bubble.ql-toolbar:after,.ql-snow .ql-toolbar:after,.ql-snow.ql-toolbar:after{clear:both;content:"";display:table}.ql-bubble .ql-toolbar button,.ql-bubble.ql-toolbar button,.ql-snow .ql-toolbar button,.ql-snow.ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:1.5rem;padding:.1875rem .3125rem;width:1.75rem}.ql-bubble .ql-toolbar button:active:hover,.ql-bubble.ql-toolbar button:active:hover,.ql-snow .ql-toolbar button:active:hover,.ql-snow.ql-toolbar button:active:hover{outline:none}[dir=rtl] .ql-bubble .ql-toolbar button,[dir=rtl] .ql-bubble.ql-toolbar button,[dir=rtl] .ql-snow .ql-toolbar button,[dir=rtl] .ql-snow.ql-toolbar button{float:right}.ql-bubble .ql-toolbar button svg,.ql-bubble.ql-toolbar button svg,.ql-snow .ql-toolbar button svg,.ql-snow.ql-toolbar button svg{float:left;height:100%}[dir=rtl] .ql-bubble .ql-toolbar button svg,[dir=rtl] .ql-bubble.ql-toolbar button svg,[dir=rtl] .ql-snow .ql-toolbar button svg,[dir=rtl] .ql-snow.ql-toolbar button svg{float:right}.ql-bubble .ql-toolbar input.ql-image[type=file],.ql-bubble.ql-toolbar input.ql-image[type=file],.ql-snow .ql-toolbar input.ql-image[type=file],.ql-snow.ql-toolbar input.ql-image[type=file]{display:none}.ql-bubble .ql-tooltip,.ql-snow .ql-tooltip{position:absolute;-webkit-transform:translateY(.625rem);transform:translateY(.625rem)}.ql-bubble .ql-tooltip.ql-flip,.ql-snow .ql-tooltip.ql-flip{-webkit-transform:translateY(-.625rem);transform:translateY(-.625rem)}.ql-bubble .ql-tooltip a,.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-bubble .ql-formats,.ql-snow .ql-formats{display:inline-block;margin-right:.9375rem;vertical-align:middle}[dir=rtl] .ql-bubble .ql-formats,[dir=rtl] .ql-snow .ql-formats{margin-left:.9375rem;margin-right:0}.ql-bubble .ql-formats:after,.ql-snow .ql-formats:after{clear:both;content:"";display:table}.ql-bubble .ql-picker,.ql-snow .ql-picker{display:inline-block;float:left;height:1.5rem;position:relative;vertical-align:middle}[dir=rtl] .ql-bubble .ql-picker,[dir=rtl] .ql-snow .ql-picker{float:right}.ql-bubble .ql-picker.ql-expanded .ql-picker-options,.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-.0625rem;top:100%;z-index:1}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before{content:attr(data-label)}.ql-bubble .ql-picker.ql-header,.ql-snow .ql-picker.ql-header{width:6.125rem}.ql-bubble .ql-picker.ql-header .ql-picker-item:before,.ql-bubble .ql-picker.ql-header .ql-picker-label:before,.ql-snow .ql-picker.ql-header .ql-picker-item:before,.ql-snow .ql-picker.ql-header .ql-picker-label:before{content:"Normal"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="1"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]:before{content:"Heading 1"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="2"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]:before{content:"Heading 2"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="3"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]:before{content:"Heading 3"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="4"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]:before{content:"Heading 4"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="5"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]:before{content:"Heading 5"}.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value="6"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]:before{content:"Heading 6"}.ql-bubble .ql-picker.ql-font,.ql-snow .ql-picker.ql-font{width:6.75rem}.ql-bubble .ql-picker.ql-font .ql-picker-item:before,.ql-bubble .ql-picker.ql-font .ql-picker-label:before,.ql-snow .ql-picker.ql-font .ql-picker-item:before,.ql-snow .ql-picker.ql-font .ql-picker-label:before{content:"Sans Serif"}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:"Serif"}.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:"Monospace"}.ql-bubble .ql-picker.ql-size,.ql-snow .ql-picker.ql-size{width:6.125rem}.ql-bubble .ql-picker.ql-size .ql-picker-item:before,.ql-bubble .ql-picker.ql-size .ql-picker-label:before,.ql-snow .ql-picker.ql-size .ql-picker-item:before,.ql-snow .ql-picker.ql-size .ql-picker-label:before{content:"Normal"}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:"Small"}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:"Large"}.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:"Huge"}.ql-bubble .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg,.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{margin-top:-.5625rem;position:absolute;right:0;top:50%;width:1.125rem}[dir=rtl] .ql-bubble .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg,[dir=rtl] .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{left:0;right:auto}.ql-bubble .ql-picker-label,.ql-snow .ql-picker-label{border:.0625rem solid transparent;cursor:pointer;display:inline-block;height:100%;padding-left:.5rem;padding-right:.125rem;position:relative;width:100%}.ql-bubble .ql-picker-label:before,.ql-snow .ql-picker-label:before{display:inline-block;line-height:1.375rem}.ql-bubble .ql-picker-options,.ql-snow .ql-picker-options{display:none;min-width:100%;padding:.25rem .5rem;position:absolute;white-space:nowrap}.ql-bubble .ql-picker-options .ql-picker-item,.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:.3125rem;padding-top:.3125rem}.ql-bubble .ql-color-picker,.ql-bubble .ql-icon-picker,.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:1.75rem}.ql-bubble .ql-color-picker .ql-picker-label,.ql-bubble .ql-icon-picker .ql-picker-label,.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:.125rem .25rem}.ql-bubble .ql-icon-picker .ql-picker-options,.ql-snow .ql-icon-picker .ql-picker-options{padding:.25rem 0}.ql-bubble .ql-icon-picker .ql-picker-item,.ql-snow .ql-icon-picker .ql-picker-item{height:1.5rem;padding:.125rem .25rem;width:1.5rem}.ql-bubble .ql-color-picker .ql-picker-options,.ql-snow .ql-color-picker .ql-picker-options{padding:.1875rem .3125rem;width:9.5rem}.ql-bubble .ql-color-picker .ql-picker-item,.ql-snow .ql-color-picker .ql-picker-item{border:.0625rem solid transparent;float:left;height:1rem;margin:.125rem;padding:0;width:1rem}[dir=rtl] .ql-bubble .ql-color-picker .ql-picker-item,[dir=rtl] .ql-snow .ql-color-picker .ql-picker-item{float:right}.ql-bubble .ql-color-picker.ql-background .ql-picker-item,.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-bubble .ql-color-picker.ql-color .ql-picker-item,.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}[dir=rtl] .ql-bubble .ql-align svg,[dir=rtl] .ql-bubble .ql-clean svg,[dir=rtl] .ql-bubble .ql-direction svg,[dir=rtl] .ql-bubble .ql-image svg,[dir=rtl] .ql-bubble .ql-indent svg,[dir=rtl] .ql-bubble .ql-italic svg,[dir=rtl] .ql-bubble .ql-link svg,[dir=rtl] .ql-bubble .ql-list svg,[dir=rtl] .ql-snow .ql-align svg,[dir=rtl] .ql-snow .ql-clean svg,[dir=rtl] .ql-snow .ql-direction svg,[dir=rtl] .ql-snow .ql-image svg,[dir=rtl] .ql-snow .ql-indent svg,[dir=rtl] .ql-snow .ql-italic svg,[dir=rtl] .ql-snow .ql-link svg,[dir=rtl] .ql-snow .ql-list svg{-webkit-transform:scaleX(-1);transform:scaleX(-1)}.ql-snow .ql-toolbar,.ql-snow.ql-toolbar{background:#fff;background-clip:padding-box}.ql-snow .ql-editor{background:#fff;min-height:15rem}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc!important;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc!important}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc!important}.ql-snow .ql-stroke{fill:none;stroke-width:2;stroke-linejoin:round;stroke-linecap:round}.ql-snow .ql-stroke-miter{fill:none;stroke-width:2;stroke-miterlimit:10}.ql-snow .ql-picker-label,.ql-snow .ql-picker-options{border:.0625rem solid transparent}.ql-snow .ql-picker-options{background-clip:padding-box;background-color:#fff}.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-snow .ql-tooltip{background-clip:padding-box;background-color:#fff;display:-ms-flexbox;display:flex;padding:.3125rem .75rem;white-space:nowrap}.ql-snow .ql-tooltip:before{content:"Visit URL:";line-height:1.625rem;margin-right:.5rem}[dir=rtl] .ql-snow .ql-tooltip:before{margin-left:.5rem;margin-right:0}.ql-snow .ql-tooltip input[type=text]{display:none;font-size:.8125rem;height:1.625rem;margin:0;padding:.1875rem .3125rem;width:10.625rem}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:12.5rem;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:.0625rem solid #ccc;content:"Edit";margin-left:1rem;padding-right:.5rem}[dir=rtl] .ql-snow .ql-tooltip a.ql-action:after{border-left:.0625rem solid #ccc;border-right:0;margin-left:0;margin-right:1rem;padding-left:.5rem;padding-right:0}.ql-snow .ql-tooltip a.ql-remove:before{content:"Remove";margin-left:.5rem}[dir=rtl] .ql-snow .ql-tooltip a.ql-remove:before{margin-left:0;margin-right:.5rem}.ql-snow .ql-tooltip a{line-height:1.625rem}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:"Save";padding-right:0}[dir=rtl] .ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-left:0;padding-left:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:"Enter link:"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:"Enter formula:"}.ql-snow .ql-tooltip[data-mode=video]:before{content:"Enter video:"}.ql-bubble .ql-toolbar .ql-picker-item.ql-selected,.ql-bubble .ql-toolbar .ql-picker-item:hover,.ql-bubble .ql-toolbar .ql-picker-label.ql-active,.ql-bubble .ql-toolbar .ql-picker-label:hover,.ql-bubble .ql-toolbar button.ql-active,.ql-bubble .ql-toolbar button:focus,.ql-bubble .ql-toolbar button:hover,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected,.ql-bubble.ql-toolbar .ql-picker-item:hover,.ql-bubble.ql-toolbar .ql-picker-label.ql-active,.ql-bubble.ql-toolbar .ql-picker-label:hover,.ql-bubble.ql-toolbar button.ql-active,.ql-bubble.ql-toolbar button:focus,.ql-bubble.ql-toolbar button:hover{color:#fff}.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-bubble .ql-toolbar button.ql-active .ql-stroke,.ql-bubble .ql-toolbar button.ql-active .ql-stroke-miter,.ql-bubble .ql-toolbar button:focus .ql-stroke,.ql-bubble .ql-toolbar button:focus .ql-stroke-miter,.ql-bubble .ql-toolbar button:hover .ql-stroke,.ql-bubble .ql-toolbar button:hover .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-bubble.ql-toolbar button.ql-active .ql-stroke,.ql-bubble.ql-toolbar button.ql-active .ql-stroke-miter,.ql-bubble.ql-toolbar button:focus .ql-stroke,.ql-bubble.ql-toolbar button:focus .ql-stroke-miter,.ql-bubble.ql-toolbar button:hover .ql-stroke,.ql-bubble.ql-toolbar button:hover .ql-stroke-miter{stroke:#fff}.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-bubble .ql-toolbar button.ql-active .ql-fill,.ql-bubble .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-bubble .ql-toolbar button:focus .ql-fill,.ql-bubble .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-bubble .ql-toolbar button:hover .ql-fill,.ql-bubble .ql-toolbar button:hover .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button.ql-active .ql-fill,.ql-bubble.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button:focus .ql-fill,.ql-bubble.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button:hover .ql-fill,.ql-bubble.ql-toolbar button:hover .ql-stroke.ql-fill{fill:#fff}@media(pointer:coarse){.ql-bubble .ql-toolbar button:hover:not(.ql-active),.ql-bubble.ql-toolbar button:hover:not(.ql-active){color:#ccc}.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#ccc}.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#ccc}}.ql-bubble .ql-stroke{fill:none;stroke:#ccc;stroke-linejoin:round;stroke-linecap:round;stroke-width:2}.ql-bubble .ql-stroke-miter{fill:none;stroke:#ccc;stroke-miterlimit:10;stroke-width:2}.ql-bubble .ql-fill,.ql-bubble .ql-stroke.ql-fill{fill:#ccc}.ql-bubble .ql-picker{color:#ccc}.ql-bubble .ql-picker.ql-expanded .ql-picker-label{color:#777;z-index:2}.ql-bubble .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#777}.ql-bubble .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#777}.ql-bubble .ql-picker-options{background-color:#444}.ql-bubble .ql-color-picker .ql-picker-label svg,.ql-bubble .ql-icon-picker .ql-picker-label svg{right:.25rem}[dir=rtl] .ql-bubble .ql-color-picker .ql-picker-label svg,[dir=rtl] .ql-bubble .ql-icon-picker .ql-picker-label svg{left:.25rem;right:auto}.ql-bubble .ql-color-picker .ql-color-picker svg{margin:.0625rem}.ql-bubble .ql-color-picker .ql-picker-item.ql-selected,.ql-bubble .ql-color-picker .ql-picker-item:hover{border-color:#fff}.ql-bubble .ql-toolbar .ql-formats{margin:.5rem .75rem .5rem 0}[dir=rtl] .ql-bubble .ql-toolbar .ql-formats{margin:.5rem 0 .5rem .75rem}.ql-bubble .ql-toolbar .ql-formats:first-child{margin-left:.75rem}[dir=rtl] .ql-bubble .ql-toolbar .ql-formats:first-child{margin-right:.75rem}.ql-bubble .ql-tooltip-arrow{border-left:.375rem solid transparent;border-right:.375rem solid transparent;content:" ";display:block;left:50%;margin-left:-.375rem;position:absolute}.ql-bubble .ql-tooltip{background-color:#444;color:#fff}.ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow{border-bottom:.375rem solid #444;top:-.375rem}.ql-bubble .ql-tooltip.ql-flip .ql-tooltip-arrow{border-top:.375rem solid #444;bottom:-.375rem}.ql-bubble .ql-tooltip.ql-editing .ql-tooltip-editor{display:block}.ql-bubble .ql-tooltip.ql-editing .ql-formats{visibility:hidden}.ql-bubble .ql-tooltip-editor{display:none}.ql-bubble .ql-tooltip-editor input[type=text]{background:transparent;border:none;color:#fff;font-size:.8125rem;height:100%;outline:none;padding:.625rem 1.25rem;position:absolute;width:100%}.ql-bubble .ql-tooltip-editor a{position:absolute;right:1.25rem;top:.625rem}[dir=rtl] .ql-bubble .ql-tooltip-editor a{left:1.25rem;right:auto}.ql-bubble .ql-tooltip-editor a:before{color:#ccc;content:"×";font-size:1rem;font-weight:700}.ql-bubble.ql-container:not(.ql-disabled) a{position:relative;white-space:nowrap}.ql-bubble.ql-container:not(.ql-disabled) a:after,.ql-bubble.ql-container:not(.ql-disabled) a:before{left:0;margin-left:50%;position:absolute;-webkit-transform:translate(-50%,-100%);transform:translate(-50%,-100%);transition:visibility 0s ease .2s;visibility:hidden}.ql-bubble.ql-container:not(.ql-disabled) a:hover:after,.ql-bubble.ql-container:not(.ql-disabled) a:hover:before{visibility:visible}.ql-bubble.ql-container:not(.ql-disabled) a:before{background-color:#444;border-radius:.9375rem;color:#fff;content:attr(href);font-size:.75rem;font-weight:400;overflow:hidden;padding:.3125rem .9375rem;text-decoration:none;top:-.3125rem;z-index:1}.ql-bubble.ql-container:not(.ql-disabled) a:after{border-left:.375rem solid transparent;border-right:.375rem solid transparent;border-top:.375rem solid #444;content:" ";height:0;top:0;width:0}.light-style .ql-editor.ql-blank:before{color:#b7b5be}.light-style .ql-bubble .ql-toolbar .ql-picker-options,.light-style .ql-bubble.ql-toolbar .ql-picker-options,.light-style .ql-snow .ql-toolbar .ql-picker-options,.light-style .ql-snow.ql-toolbar .ql-picker-options{box-shadow:0 .25rem 1rem hsla(251,6%,66%,.45)}.light-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.light-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2.375rem}.light-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.light-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:2rem}.light-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.light-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.625rem}.light-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.light-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1.375rem}.light-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.light-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:1.125rem}.light-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.light-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.9375rem}.light-style .ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.light-style .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.light-style .ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.light-style .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.light-style .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.light-style .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:.75rem}.light-style .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.light-style .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:1rem}.light-style .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.light-style .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:1.25rem}.light-style .ql-snow .ql-editor.ql-blank:before{padding:0 1.25rem}.light-style .ql-snow.ql-container{border:.0625rem solid #dbdade}.light-style .ql-snow .ql-editor{padding:1.25rem}.light-style .ql-snow .ql-toolbar,.light-style .ql-snow.ql-toolbar{border:.0625rem solid #dbdade}@media(pointer:coarse){.light-style .ql-snow .ql-toolbar button:hover:not(.ql-active),.light-style .ql-snow.ql-toolbar button:hover:not(.ql-active){color:#6f6b7d}.light-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.light-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.light-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.light-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#6f6b7d}.light-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.light-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.light-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.light-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#6f6b7d}}.light-style .ql-snow.ql-toolbar+.ql-container.ql-snow{border-top:0}.light-style .ql-snow .ql-stroke{stroke:#6f6b7d}.light-style .ql-snow .ql-fill,.light-style .ql-snow .ql-stroke.ql-fill{fill:#6f6b7d}.light-style .ql-snow .ql-stroke-miter{stroke:#6f6b7d}.light-style .ql-snow .ql-picker{color:#6f6b7d}.light-style .ql-snow .ql-picker.ql-expanded .ql-picker-label,.light-style .ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#dbdade}.light-style .ql-snow .ql-tooltip{border:0 solid #dbdade;box-shadow:0 .25rem 1rem hsla(251,6%,66%,.45);color:#6f6b7d}.light-style .ql-snow .ql-tooltip input[type=text]{border:.0625rem solid #dbdade}.light-style .ql-bubble .ql-tooltip{border-radius:.375rem;z-index:1090}.dark-style .ql-editor.ql-blank:before{color:#5e6692}.dark-style .ql-bubble .ql-tooltip,.dark-style .ql-snow .ql-tooltip{background:#25293c}.dark-style .ql-bubble .ql-toolbar .ql-picker-options,.dark-style .ql-bubble.ql-toolbar .ql-picker-options,.dark-style .ql-snow .ql-toolbar .ql-picker-options,.dark-style .ql-snow.ql-toolbar .ql-picker-options{box-shadow:0 .25rem 1rem rgba(15,20,34,.55)}.dark-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.dark-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2.375rem}.dark-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.dark-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:2rem}.dark-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.dark-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.625rem}.dark-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.dark-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1.375rem}.dark-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.dark-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:1.125rem}.dark-style .ql-bubble .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.dark-style .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.9375rem}.dark-style .ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.dark-style .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.dark-style .ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.dark-style .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.dark-style .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.dark-style .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:.75rem}.dark-style .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.dark-style .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:1rem}.dark-style .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.dark-style .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:1.25rem}.dark-style .ql-snow .ql-editor.ql-blank:before{padding:0 1.25rem}.dark-style .ql-snow.ql-container{border:.0625rem solid #434968}.dark-style .ql-snow .ql-editor{background:#2f3349;padding:1.25rem}.dark-style .ql-snow .ql-picker-options{background:#2f3349}.dark-style .ql-snow .ql-toolbar,.dark-style .ql-snow.ql-toolbar{background:#2f3349;border:.0625rem solid #434968}@media(pointer:coarse){.dark-style .ql-snow .ql-toolbar button:hover:not(.ql-active),.dark-style .ql-snow.ql-toolbar button:hover:not(.ql-active){color:#b6bee3}.dark-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.dark-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.dark-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.dark-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#b6bee3}.dark-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.dark-style .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.dark-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.dark-style .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#b6bee3}}.dark-style .ql-snow.ql-toolbar+.ql-container.ql-snow{border-top:0}.dark-style .ql-snow .ql-stroke,.dark-style .ql-snow .ql-stroke-miter{stroke:#b6bee3}.dark-style .ql-snow .ql-fill,.dark-style .ql-snow .ql-stroke.ql-fill{fill:#b6bee3}.dark-style .ql-snow .ql-picker{color:#b6bee3}.dark-style .ql-snow .ql-picker.ql-expanded .ql-picker-label,.dark-style .ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#434968}.dark-style .ql-snow .ql-tooltip{border:0 solid #434968;box-shadow:0 .25rem 1rem rgba(15,20,34,.55);color:#b6bee3}.dark-style .ql-snow .ql-tooltip input[type=text]{border:.0625rem solid #434968}.dark-style .ql-bubble .ql-tooltip{border-radius:.375rem;z-index:1090}
