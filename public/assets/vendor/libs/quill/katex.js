!function(){"use strict";var e={d:function(t,r){for(var a in r)e.o(r,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:r[a]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{katex:function(){return Ka}});class r{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(e,t){return t?e&&e.loc&&t.loc&&e.loc.lexer===t.loc.lexer?new r(e.loc.lexer,e.loc.start,t.loc.end):null:e&&e.loc}}class a{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new a(t,r.range(this,e))}}class n{constructor(e,t){this.position=void 0;var r,a="KaTeX parse error: "+e,i=t&&t.loc;if(i&&i.start<=i.end){var o=i.lexer.input;r=i.start;var s=i.end;r===o.length?a+=" at end of input: ":a+=" at position "+(r+1)+": ";var l=o.slice(r,s).replace(/[^]/g,"$&̲");a+=(r>15?"…"+o.slice(r-15,r):o.slice(0,r))+l+(s+15<o.length?o.slice(s,s+15)+"…":o.slice(s))}var h=new Error(a);return h.name="ParseError",h.__proto__=n.prototype,h.position=r,h}}n.prototype.__proto__=Error.prototype;var i=/([A-Z])/g,o={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},s=/[&><"']/g;var l=function e(t){return"ordgroup"===t.type||"color"===t.type?1===t.body.length?e(t.body[0]):t:"font"===t.type?e(t.body):t},h={contains:function(e,t){return-1!==e.indexOf(t)},deflt:function(e,t){return void 0===e?t:e},escape:function(e){return String(e).replace(s,(e=>o[e]))},hyphenate:function(e){return e.replace(i,"-$1").toLowerCase()},getBaseElem:l,isCharacterBox:function(e){var t=l(e);return"mathord"===t.type||"textord"===t.type||"atom"===t.type},protocolFromUrl:function(e){var t=/^\s*([^\\/#]*?)(?::|&#0*58|&#x0*3a)/i.exec(e);return null!=t?t[1]:"_relative"}},m={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:e=>"#"+e},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(e,t)=>(t.push(e),t)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:e=>Math.max(0,e),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:e=>Math.max(0,e),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:e=>Math.max(0,e),cli:"-e, --max-expand <n>",cliProcessor:e=>"Infinity"===e?1/0:parseInt(e)},globalGroup:{type:"boolean",cli:!1}};function c(e){if(e.default)return e.default;var t=e.type,r=Array.isArray(t)?t[0]:t;if("string"!=typeof r)return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class p{constructor(e){for(var t in this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{},m)if(m.hasOwnProperty(t)){var r=m[t];this[t]=void 0!==e[t]?r.processor?r.processor(e[t]):e[t]:c(r)}}reportNonstrict(e,t,r){var a=this.strict;if("function"==typeof a&&(a=a(e,t,r)),a&&"ignore"!==a){if(!0===a||"error"===a)throw new n("LaTeX-incompatible input and strict mode is set to 'error': "+t+" ["+e+"]",r);"warn"===a?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+t+" ["+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+a+"': "+t+" ["+e+"]")}}useStrictBehavior(e,t,r){var a=this.strict;if("function"==typeof a)try{a=a(e,t,r)}catch(e){a="error"}return!(!a||"ignore"===a)&&(!0===a||"error"===a||("warn"===a?("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+t+" ["+e+"]"),!1):("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+a+"': "+t+" ["+e+"]"),!1)))}isTrusted(e){e.url&&!e.protocol&&(e.protocol=h.protocolFromUrl(e.url));var t="function"==typeof this.trust?this.trust(e):this.trust;return Boolean(t)}}class u{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return d[g[this.id]]}sub(){return d[f[this.id]]}fracNum(){return d[v[this.id]]}fracDen(){return d[b[this.id]]}cramp(){return d[y[this.id]]}text(){return d[x[this.id]]}isTight(){return this.size>=2}}var d=[new u(0,0,!1),new u(1,0,!0),new u(2,1,!1),new u(3,1,!0),new u(4,2,!1),new u(5,2,!0),new u(6,3,!1),new u(7,3,!0)],g=[4,5,4,5,6,7,6,7],f=[5,5,5,5,7,7,7,7],v=[2,3,4,5,6,7,6,7],b=[3,3,5,5,7,7,7,7],y=[1,1,3,3,5,5,7,7],x=[0,1,2,3,2,3,2,3],w={DISPLAY:d[0],TEXT:d[2],SCRIPT:d[4],SCRIPTSCRIPT:d[6]},k=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];var S=[];function M(e){for(var t=0;t<S.length;t+=2)if(e>=S[t]&&e<=S[t+1])return!0;return!1}k.forEach((e=>e.blocks.forEach((e=>S.push(...e)))));var z=80,A={doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"};class T{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return h.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){return this.children.map((e=>e.toText())).join("")}}var B={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},C={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},N={"Å":"A","Ð":"D","Þ":"o","å":"a","ð":"d","þ":"o","А":"A","Б":"B","В":"B","Г":"F","Д":"A","Е":"E","Ж":"K","З":"3","И":"N","Й":"N","К":"K","Л":"N","М":"M","Н":"H","О":"O","П":"N","Р":"P","С":"C","Т":"T","У":"y","Ф":"O","Х":"X","Ц":"U","Ч":"h","Ш":"W","Щ":"W","Ъ":"B","Ы":"X","Ь":"B","Э":"3","Ю":"X","Я":"R","а":"a","б":"b","в":"a","г":"r","д":"y","е":"e","ж":"m","з":"e","и":"n","й":"n","к":"n","л":"n","м":"m","н":"n","о":"o","п":"n","р":"p","с":"c","т":"o","у":"y","ф":"b","х":"x","ц":"n","ч":"n","ш":"w","щ":"w","ъ":"a","ы":"m","ь":"a","э":"e","ю":"m","я":"r"};function q(e,t,r){if(!B[t])throw new Error("Font metrics not found for font: "+t+".");var a=e.charCodeAt(0),n=B[t][a];if(!n&&e[0]in N&&(a=N[e[0]].charCodeAt(0),n=B[t][a]),n||"text"!==r||M(a)&&(n=B[t][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}var I={};var R=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],H=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],O=function(e,t){return t.size<2?e:R[e-1][t.size-1]};class E{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||E.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=H[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new E(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:O(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:H[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=O(E.BASESIZE,e);return this.size===t&&this.textSize===E.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==E.BASESIZE?["sizing","reset-size"+this.size,"size"+E.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=function(e){var t;if(!I[t=e>=5?0:e>=3?1:2]){var r=I[t]={cssEmPerMu:C.quad[t]/18};for(var a in C)C.hasOwnProperty(a)&&(r[a]=C[a][t])}return I[t]}(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}E.BASESIZE=6;var L={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},D={ex:!0,em:!0,mu:!0},P=function(e){return"string"!=typeof e&&(e=e.unit),e in L||e in D||"ex"===e},V=function(e,t){var r;if(e.unit in L)r=L[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if("mu"===e.unit)r=t.fontMetrics().cssEmPerMu;else{var a;if(a=t.style.isTight()?t.havingStyle(t.style.text()):t,"ex"===e.unit)r=a.fontMetrics().xHeight;else{if("em"!==e.unit)throw new n("Invalid unit: '"+e.unit+"'");r=a.fontMetrics().quad}a!==t&&(r*=a.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},F=function(e){return+e.toFixed(4)+"em"},G=function(e){return e.filter((e=>e)).join(" ")},U=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var a=t.getColor();a&&(this.style.color=a)}},Y=function(e){var t=document.createElement(e);for(var r in t.className=G(this.classes),this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var a in this.attributes)this.attributes.hasOwnProperty(a)&&t.setAttribute(a,this.attributes[a]);for(var n=0;n<this.children.length;n++)t.appendChild(this.children[n].toNode());return t},X=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+h.escape(G(this.classes))+'"');var r="";for(var a in this.style)this.style.hasOwnProperty(a)&&(r+=h.hyphenate(a)+":"+this.style[a]+";");for(var n in r&&(t+=' style="'+h.escape(r)+'"'),this.attributes)this.attributes.hasOwnProperty(n)&&(t+=" "+n+'="'+h.escape(this.attributes[n])+'"');t+=">";for(var i=0;i<this.children.length;i++)t+=this.children[i].toMarkup();return t+="</"+e+">"};class W{constructor(e,t,r,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,U.call(this,e,r,a),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return h.contains(this.classes,e)}toNode(){return Y.call(this,"span")}toMarkup(){return X.call(this,"span")}}class _{constructor(e,t,r,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,U.call(this,t,a),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return h.contains(this.classes,e)}toNode(){return Y.call(this,"a")}toMarkup(){return X.call(this,"a")}}class j{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return h.contains(this.classes,e)}toNode(){var e=document.createElement("img");for(var t in e.src=this.src,e.alt=this.alt,e.className="mord",this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e="<img  src='"+this.src+" 'alt='"+this.alt+"' ",t="";for(var r in this.style)this.style.hasOwnProperty(r)&&(t+=h.hyphenate(r)+":"+this.style[r]+";");return t&&(e+=' style="'+h.escape(t)+'"'),e+="'/>"}}var $={"î":"ı̂","ï":"ı̈","í":"ı́","ì":"ı̀"};class Z{constructor(e,t,r,a,n,i,o,s){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=a||0,this.skew=n||0,this.width=i||0,this.classes=o||[],this.style=s||{},this.maxFontSize=0;var l=function(e){for(var t=0;t<k.length;t++)for(var r=k[t],a=0;a<r.blocks.length;a++){var n=r.blocks[a];if(e>=n[0]&&e<=n[1])return r.name}return null}(this.text.charCodeAt(0));l&&this.classes.push(l+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=$[this.text])}hasClass(e){return h.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;for(var r in this.italic>0&&((t=document.createElement("span")).style.marginRight=F(this.italic)),this.classes.length>0&&((t=t||document.createElement("span")).className=G(this.classes)),this.style)this.style.hasOwnProperty(r)&&((t=t||document.createElement("span")).style[r]=this.style[r]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=h.escape(G(this.classes)),t+='"');var r="";for(var a in this.italic>0&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(a)&&(r+=h.hyphenate(a)+":"+this.style[a]+";");r&&(e=!0,t+=' style="'+h.escape(r)+'"');var n=h.escape(this.text);return e?(t+=">",t+=n,t+="</span>"):n}}class K{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</svg>"}}class J{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",A[this.pathName]),e}toMarkup(){return this.alternate?"<path d='"+this.alternate+"'/>":"<path d='"+A[this.pathName]+"'/>"}}class Q{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","line");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);return e}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");return e+="/>"}}function ee(e){if(e instanceof Z)return e;throw new Error("Expected symbolNode but got "+String(e)+".")}var te={bin:1,close:1,inner:1,open:1,punct:1,rel:1},re={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},ae={math:{},text:{}};function ne(e,t,r,a,n,i){ae[e][n]={font:t,group:r,replace:a},i&&a&&(ae[e][a]=ae[e][n])}var ie="math",oe="text",se="main",le="ams",he="accent-token",me="bin",ce="close",pe="inner",ue="mathord",de="op-token",ge="open",fe="punct",ve="rel",be="spacing",ye="textord";ne(ie,se,ve,"≡","\\equiv",!0),ne(ie,se,ve,"≺","\\prec",!0),ne(ie,se,ve,"≻","\\succ",!0),ne(ie,se,ve,"∼","\\sim",!0),ne(ie,se,ve,"⊥","\\perp"),ne(ie,se,ve,"⪯","\\preceq",!0),ne(ie,se,ve,"⪰","\\succeq",!0),ne(ie,se,ve,"≃","\\simeq",!0),ne(ie,se,ve,"∣","\\mid",!0),ne(ie,se,ve,"≪","\\ll",!0),ne(ie,se,ve,"≫","\\gg",!0),ne(ie,se,ve,"≍","\\asymp",!0),ne(ie,se,ve,"∥","\\parallel"),ne(ie,se,ve,"⋈","\\bowtie",!0),ne(ie,se,ve,"⌣","\\smile",!0),ne(ie,se,ve,"⊑","\\sqsubseteq",!0),ne(ie,se,ve,"⊒","\\sqsupseteq",!0),ne(ie,se,ve,"≐","\\doteq",!0),ne(ie,se,ve,"⌢","\\frown",!0),ne(ie,se,ve,"∋","\\ni",!0),ne(ie,se,ve,"∝","\\propto",!0),ne(ie,se,ve,"⊢","\\vdash",!0),ne(ie,se,ve,"⊣","\\dashv",!0),ne(ie,se,ve,"∋","\\owns"),ne(ie,se,fe,".","\\ldotp"),ne(ie,se,fe,"⋅","\\cdotp"),ne(ie,se,ye,"#","\\#"),ne(oe,se,ye,"#","\\#"),ne(ie,se,ye,"&","\\&"),ne(oe,se,ye,"&","\\&"),ne(ie,se,ye,"ℵ","\\aleph",!0),ne(ie,se,ye,"∀","\\forall",!0),ne(ie,se,ye,"ℏ","\\hbar",!0),ne(ie,se,ye,"∃","\\exists",!0),ne(ie,se,ye,"∇","\\nabla",!0),ne(ie,se,ye,"♭","\\flat",!0),ne(ie,se,ye,"ℓ","\\ell",!0),ne(ie,se,ye,"♮","\\natural",!0),ne(ie,se,ye,"♣","\\clubsuit",!0),ne(ie,se,ye,"℘","\\wp",!0),ne(ie,se,ye,"♯","\\sharp",!0),ne(ie,se,ye,"♢","\\diamondsuit",!0),ne(ie,se,ye,"ℜ","\\Re",!0),ne(ie,se,ye,"♡","\\heartsuit",!0),ne(ie,se,ye,"ℑ","\\Im",!0),ne(ie,se,ye,"♠","\\spadesuit",!0),ne(ie,se,ye,"§","\\S",!0),ne(oe,se,ye,"§","\\S"),ne(ie,se,ye,"¶","\\P",!0),ne(oe,se,ye,"¶","\\P"),ne(ie,se,ye,"†","\\dag"),ne(oe,se,ye,"†","\\dag"),ne(oe,se,ye,"†","\\textdagger"),ne(ie,se,ye,"‡","\\ddag"),ne(oe,se,ye,"‡","\\ddag"),ne(oe,se,ye,"‡","\\textdaggerdbl"),ne(ie,se,ce,"⎱","\\rmoustache",!0),ne(ie,se,ge,"⎰","\\lmoustache",!0),ne(ie,se,ce,"⟯","\\rgroup",!0),ne(ie,se,ge,"⟮","\\lgroup",!0),ne(ie,se,me,"∓","\\mp",!0),ne(ie,se,me,"⊖","\\ominus",!0),ne(ie,se,me,"⊎","\\uplus",!0),ne(ie,se,me,"⊓","\\sqcap",!0),ne(ie,se,me,"∗","\\ast"),ne(ie,se,me,"⊔","\\sqcup",!0),ne(ie,se,me,"◯","\\bigcirc",!0),ne(ie,se,me,"∙","\\bullet",!0),ne(ie,se,me,"‡","\\ddagger"),ne(ie,se,me,"≀","\\wr",!0),ne(ie,se,me,"⨿","\\amalg"),ne(ie,se,me,"&","\\And"),ne(ie,se,ve,"⟵","\\longleftarrow",!0),ne(ie,se,ve,"⇐","\\Leftarrow",!0),ne(ie,se,ve,"⟸","\\Longleftarrow",!0),ne(ie,se,ve,"⟶","\\longrightarrow",!0),ne(ie,se,ve,"⇒","\\Rightarrow",!0),ne(ie,se,ve,"⟹","\\Longrightarrow",!0),ne(ie,se,ve,"↔","\\leftrightarrow",!0),ne(ie,se,ve,"⟷","\\longleftrightarrow",!0),ne(ie,se,ve,"⇔","\\Leftrightarrow",!0),ne(ie,se,ve,"⟺","\\Longleftrightarrow",!0),ne(ie,se,ve,"↦","\\mapsto",!0),ne(ie,se,ve,"⟼","\\longmapsto",!0),ne(ie,se,ve,"↗","\\nearrow",!0),ne(ie,se,ve,"↩","\\hookleftarrow",!0),ne(ie,se,ve,"↪","\\hookrightarrow",!0),ne(ie,se,ve,"↘","\\searrow",!0),ne(ie,se,ve,"↼","\\leftharpoonup",!0),ne(ie,se,ve,"⇀","\\rightharpoonup",!0),ne(ie,se,ve,"↙","\\swarrow",!0),ne(ie,se,ve,"↽","\\leftharpoondown",!0),ne(ie,se,ve,"⇁","\\rightharpoondown",!0),ne(ie,se,ve,"↖","\\nwarrow",!0),ne(ie,se,ve,"⇌","\\rightleftharpoons",!0),ne(ie,le,ve,"≮","\\nless",!0),ne(ie,le,ve,"","\\@nleqslant"),ne(ie,le,ve,"","\\@nleqq"),ne(ie,le,ve,"⪇","\\lneq",!0),ne(ie,le,ve,"≨","\\lneqq",!0),ne(ie,le,ve,"","\\@lvertneqq"),ne(ie,le,ve,"⋦","\\lnsim",!0),ne(ie,le,ve,"⪉","\\lnapprox",!0),ne(ie,le,ve,"⊀","\\nprec",!0),ne(ie,le,ve,"⋠","\\npreceq",!0),ne(ie,le,ve,"⋨","\\precnsim",!0),ne(ie,le,ve,"⪹","\\precnapprox",!0),ne(ie,le,ve,"≁","\\nsim",!0),ne(ie,le,ve,"","\\@nshortmid"),ne(ie,le,ve,"∤","\\nmid",!0),ne(ie,le,ve,"⊬","\\nvdash",!0),ne(ie,le,ve,"⊭","\\nvDash",!0),ne(ie,le,ve,"⋪","\\ntriangleleft"),ne(ie,le,ve,"⋬","\\ntrianglelefteq",!0),ne(ie,le,ve,"⊊","\\subsetneq",!0),ne(ie,le,ve,"","\\@varsubsetneq"),ne(ie,le,ve,"⫋","\\subsetneqq",!0),ne(ie,le,ve,"","\\@varsubsetneqq"),ne(ie,le,ve,"≯","\\ngtr",!0),ne(ie,le,ve,"","\\@ngeqslant"),ne(ie,le,ve,"","\\@ngeqq"),ne(ie,le,ve,"⪈","\\gneq",!0),ne(ie,le,ve,"≩","\\gneqq",!0),ne(ie,le,ve,"","\\@gvertneqq"),ne(ie,le,ve,"⋧","\\gnsim",!0),ne(ie,le,ve,"⪊","\\gnapprox",!0),ne(ie,le,ve,"⊁","\\nsucc",!0),ne(ie,le,ve,"⋡","\\nsucceq",!0),ne(ie,le,ve,"⋩","\\succnsim",!0),ne(ie,le,ve,"⪺","\\succnapprox",!0),ne(ie,le,ve,"≆","\\ncong",!0),ne(ie,le,ve,"","\\@nshortparallel"),ne(ie,le,ve,"∦","\\nparallel",!0),ne(ie,le,ve,"⊯","\\nVDash",!0),ne(ie,le,ve,"⋫","\\ntriangleright"),ne(ie,le,ve,"⋭","\\ntrianglerighteq",!0),ne(ie,le,ve,"","\\@nsupseteqq"),ne(ie,le,ve,"⊋","\\supsetneq",!0),ne(ie,le,ve,"","\\@varsupsetneq"),ne(ie,le,ve,"⫌","\\supsetneqq",!0),ne(ie,le,ve,"","\\@varsupsetneqq"),ne(ie,le,ve,"⊮","\\nVdash",!0),ne(ie,le,ve,"⪵","\\precneqq",!0),ne(ie,le,ve,"⪶","\\succneqq",!0),ne(ie,le,ve,"","\\@nsubseteqq"),ne(ie,le,me,"⊴","\\unlhd"),ne(ie,le,me,"⊵","\\unrhd"),ne(ie,le,ve,"↚","\\nleftarrow",!0),ne(ie,le,ve,"↛","\\nrightarrow",!0),ne(ie,le,ve,"⇍","\\nLeftarrow",!0),ne(ie,le,ve,"⇏","\\nRightarrow",!0),ne(ie,le,ve,"↮","\\nleftrightarrow",!0),ne(ie,le,ve,"⇎","\\nLeftrightarrow",!0),ne(ie,le,ve,"△","\\vartriangle"),ne(ie,le,ye,"ℏ","\\hslash"),ne(ie,le,ye,"▽","\\triangledown"),ne(ie,le,ye,"◊","\\lozenge"),ne(ie,le,ye,"Ⓢ","\\circledS"),ne(ie,le,ye,"®","\\circledR"),ne(oe,le,ye,"®","\\circledR"),ne(ie,le,ye,"∡","\\measuredangle",!0),ne(ie,le,ye,"∄","\\nexists"),ne(ie,le,ye,"℧","\\mho"),ne(ie,le,ye,"Ⅎ","\\Finv",!0),ne(ie,le,ye,"⅁","\\Game",!0),ne(ie,le,ye,"‵","\\backprime"),ne(ie,le,ye,"▲","\\blacktriangle"),ne(ie,le,ye,"▼","\\blacktriangledown"),ne(ie,le,ye,"■","\\blacksquare"),ne(ie,le,ye,"⧫","\\blacklozenge"),ne(ie,le,ye,"★","\\bigstar"),ne(ie,le,ye,"∢","\\sphericalangle",!0),ne(ie,le,ye,"∁","\\complement",!0),ne(ie,le,ye,"ð","\\eth",!0),ne(oe,se,ye,"ð","ð"),ne(ie,le,ye,"╱","\\diagup"),ne(ie,le,ye,"╲","\\diagdown"),ne(ie,le,ye,"□","\\square"),ne(ie,le,ye,"□","\\Box"),ne(ie,le,ye,"◊","\\Diamond"),ne(ie,le,ye,"¥","\\yen",!0),ne(oe,le,ye,"¥","\\yen",!0),ne(ie,le,ye,"✓","\\checkmark",!0),ne(oe,le,ye,"✓","\\checkmark"),ne(ie,le,ye,"ℶ","\\beth",!0),ne(ie,le,ye,"ℸ","\\daleth",!0),ne(ie,le,ye,"ℷ","\\gimel",!0),ne(ie,le,ye,"ϝ","\\digamma",!0),ne(ie,le,ye,"ϰ","\\varkappa"),ne(ie,le,ge,"┌","\\@ulcorner",!0),ne(ie,le,ce,"┐","\\@urcorner",!0),ne(ie,le,ge,"└","\\@llcorner",!0),ne(ie,le,ce,"┘","\\@lrcorner",!0),ne(ie,le,ve,"≦","\\leqq",!0),ne(ie,le,ve,"⩽","\\leqslant",!0),ne(ie,le,ve,"⪕","\\eqslantless",!0),ne(ie,le,ve,"≲","\\lesssim",!0),ne(ie,le,ve,"⪅","\\lessapprox",!0),ne(ie,le,ve,"≊","\\approxeq",!0),ne(ie,le,me,"⋖","\\lessdot"),ne(ie,le,ve,"⋘","\\lll",!0),ne(ie,le,ve,"≶","\\lessgtr",!0),ne(ie,le,ve,"⋚","\\lesseqgtr",!0),ne(ie,le,ve,"⪋","\\lesseqqgtr",!0),ne(ie,le,ve,"≑","\\doteqdot"),ne(ie,le,ve,"≓","\\risingdotseq",!0),ne(ie,le,ve,"≒","\\fallingdotseq",!0),ne(ie,le,ve,"∽","\\backsim",!0),ne(ie,le,ve,"⋍","\\backsimeq",!0),ne(ie,le,ve,"⫅","\\subseteqq",!0),ne(ie,le,ve,"⋐","\\Subset",!0),ne(ie,le,ve,"⊏","\\sqsubset",!0),ne(ie,le,ve,"≼","\\preccurlyeq",!0),ne(ie,le,ve,"⋞","\\curlyeqprec",!0),ne(ie,le,ve,"≾","\\precsim",!0),ne(ie,le,ve,"⪷","\\precapprox",!0),ne(ie,le,ve,"⊲","\\vartriangleleft"),ne(ie,le,ve,"⊴","\\trianglelefteq"),ne(ie,le,ve,"⊨","\\vDash",!0),ne(ie,le,ve,"⊪","\\Vvdash",!0),ne(ie,le,ve,"⌣","\\smallsmile"),ne(ie,le,ve,"⌢","\\smallfrown"),ne(ie,le,ve,"≏","\\bumpeq",!0),ne(ie,le,ve,"≎","\\Bumpeq",!0),ne(ie,le,ve,"≧","\\geqq",!0),ne(ie,le,ve,"⩾","\\geqslant",!0),ne(ie,le,ve,"⪖","\\eqslantgtr",!0),ne(ie,le,ve,"≳","\\gtrsim",!0),ne(ie,le,ve,"⪆","\\gtrapprox",!0),ne(ie,le,me,"⋗","\\gtrdot"),ne(ie,le,ve,"⋙","\\ggg",!0),ne(ie,le,ve,"≷","\\gtrless",!0),ne(ie,le,ve,"⋛","\\gtreqless",!0),ne(ie,le,ve,"⪌","\\gtreqqless",!0),ne(ie,le,ve,"≖","\\eqcirc",!0),ne(ie,le,ve,"≗","\\circeq",!0),ne(ie,le,ve,"≜","\\triangleq",!0),ne(ie,le,ve,"∼","\\thicksim"),ne(ie,le,ve,"≈","\\thickapprox"),ne(ie,le,ve,"⫆","\\supseteqq",!0),ne(ie,le,ve,"⋑","\\Supset",!0),ne(ie,le,ve,"⊐","\\sqsupset",!0),ne(ie,le,ve,"≽","\\succcurlyeq",!0),ne(ie,le,ve,"⋟","\\curlyeqsucc",!0),ne(ie,le,ve,"≿","\\succsim",!0),ne(ie,le,ve,"⪸","\\succapprox",!0),ne(ie,le,ve,"⊳","\\vartriangleright"),ne(ie,le,ve,"⊵","\\trianglerighteq"),ne(ie,le,ve,"⊩","\\Vdash",!0),ne(ie,le,ve,"∣","\\shortmid"),ne(ie,le,ve,"∥","\\shortparallel"),ne(ie,le,ve,"≬","\\between",!0),ne(ie,le,ve,"⋔","\\pitchfork",!0),ne(ie,le,ve,"∝","\\varpropto"),ne(ie,le,ve,"◀","\\blacktriangleleft"),ne(ie,le,ve,"∴","\\therefore",!0),ne(ie,le,ve,"∍","\\backepsilon"),ne(ie,le,ve,"▶","\\blacktriangleright"),ne(ie,le,ve,"∵","\\because",!0),ne(ie,le,ve,"⋘","\\llless"),ne(ie,le,ve,"⋙","\\gggtr"),ne(ie,le,me,"⊲","\\lhd"),ne(ie,le,me,"⊳","\\rhd"),ne(ie,le,ve,"≂","\\eqsim",!0),ne(ie,se,ve,"⋈","\\Join"),ne(ie,le,ve,"≑","\\Doteq",!0),ne(ie,le,me,"∔","\\dotplus",!0),ne(ie,le,me,"∖","\\smallsetminus"),ne(ie,le,me,"⋒","\\Cap",!0),ne(ie,le,me,"⋓","\\Cup",!0),ne(ie,le,me,"⩞","\\doublebarwedge",!0),ne(ie,le,me,"⊟","\\boxminus",!0),ne(ie,le,me,"⊞","\\boxplus",!0),ne(ie,le,me,"⋇","\\divideontimes",!0),ne(ie,le,me,"⋉","\\ltimes",!0),ne(ie,le,me,"⋊","\\rtimes",!0),ne(ie,le,me,"⋋","\\leftthreetimes",!0),ne(ie,le,me,"⋌","\\rightthreetimes",!0),ne(ie,le,me,"⋏","\\curlywedge",!0),ne(ie,le,me,"⋎","\\curlyvee",!0),ne(ie,le,me,"⊝","\\circleddash",!0),ne(ie,le,me,"⊛","\\circledast",!0),ne(ie,le,me,"⋅","\\centerdot"),ne(ie,le,me,"⊺","\\intercal",!0),ne(ie,le,me,"⋒","\\doublecap"),ne(ie,le,me,"⋓","\\doublecup"),ne(ie,le,me,"⊠","\\boxtimes",!0),ne(ie,le,ve,"⇢","\\dashrightarrow",!0),ne(ie,le,ve,"⇠","\\dashleftarrow",!0),ne(ie,le,ve,"⇇","\\leftleftarrows",!0),ne(ie,le,ve,"⇆","\\leftrightarrows",!0),ne(ie,le,ve,"⇚","\\Lleftarrow",!0),ne(ie,le,ve,"↞","\\twoheadleftarrow",!0),ne(ie,le,ve,"↢","\\leftarrowtail",!0),ne(ie,le,ve,"↫","\\looparrowleft",!0),ne(ie,le,ve,"⇋","\\leftrightharpoons",!0),ne(ie,le,ve,"↶","\\curvearrowleft",!0),ne(ie,le,ve,"↺","\\circlearrowleft",!0),ne(ie,le,ve,"↰","\\Lsh",!0),ne(ie,le,ve,"⇈","\\upuparrows",!0),ne(ie,le,ve,"↿","\\upharpoonleft",!0),ne(ie,le,ve,"⇃","\\downharpoonleft",!0),ne(ie,se,ve,"⊶","\\origof",!0),ne(ie,se,ve,"⊷","\\imageof",!0),ne(ie,le,ve,"⊸","\\multimap",!0),ne(ie,le,ve,"↭","\\leftrightsquigarrow",!0),ne(ie,le,ve,"⇉","\\rightrightarrows",!0),ne(ie,le,ve,"⇄","\\rightleftarrows",!0),ne(ie,le,ve,"↠","\\twoheadrightarrow",!0),ne(ie,le,ve,"↣","\\rightarrowtail",!0),ne(ie,le,ve,"↬","\\looparrowright",!0),ne(ie,le,ve,"↷","\\curvearrowright",!0),ne(ie,le,ve,"↻","\\circlearrowright",!0),ne(ie,le,ve,"↱","\\Rsh",!0),ne(ie,le,ve,"⇊","\\downdownarrows",!0),ne(ie,le,ve,"↾","\\upharpoonright",!0),ne(ie,le,ve,"⇂","\\downharpoonright",!0),ne(ie,le,ve,"⇝","\\rightsquigarrow",!0),ne(ie,le,ve,"⇝","\\leadsto"),ne(ie,le,ve,"⇛","\\Rrightarrow",!0),ne(ie,le,ve,"↾","\\restriction"),ne(ie,se,ye,"‘","`"),ne(ie,se,ye,"$","\\$"),ne(oe,se,ye,"$","\\$"),ne(oe,se,ye,"$","\\textdollar"),ne(ie,se,ye,"%","\\%"),ne(oe,se,ye,"%","\\%"),ne(ie,se,ye,"_","\\_"),ne(oe,se,ye,"_","\\_"),ne(oe,se,ye,"_","\\textunderscore"),ne(ie,se,ye,"∠","\\angle",!0),ne(ie,se,ye,"∞","\\infty",!0),ne(ie,se,ye,"′","\\prime"),ne(ie,se,ye,"△","\\triangle"),ne(ie,se,ye,"Γ","\\Gamma",!0),ne(ie,se,ye,"Δ","\\Delta",!0),ne(ie,se,ye,"Θ","\\Theta",!0),ne(ie,se,ye,"Λ","\\Lambda",!0),ne(ie,se,ye,"Ξ","\\Xi",!0),ne(ie,se,ye,"Π","\\Pi",!0),ne(ie,se,ye,"Σ","\\Sigma",!0),ne(ie,se,ye,"Υ","\\Upsilon",!0),ne(ie,se,ye,"Φ","\\Phi",!0),ne(ie,se,ye,"Ψ","\\Psi",!0),ne(ie,se,ye,"Ω","\\Omega",!0),ne(ie,se,ye,"A","Α"),ne(ie,se,ye,"B","Β"),ne(ie,se,ye,"E","Ε"),ne(ie,se,ye,"Z","Ζ"),ne(ie,se,ye,"H","Η"),ne(ie,se,ye,"I","Ι"),ne(ie,se,ye,"K","Κ"),ne(ie,se,ye,"M","Μ"),ne(ie,se,ye,"N","Ν"),ne(ie,se,ye,"O","Ο"),ne(ie,se,ye,"P","Ρ"),ne(ie,se,ye,"T","Τ"),ne(ie,se,ye,"X","Χ"),ne(ie,se,ye,"¬","\\neg",!0),ne(ie,se,ye,"¬","\\lnot"),ne(ie,se,ye,"⊤","\\top"),ne(ie,se,ye,"⊥","\\bot"),ne(ie,se,ye,"∅","\\emptyset"),ne(ie,le,ye,"∅","\\varnothing"),ne(ie,se,ue,"α","\\alpha",!0),ne(ie,se,ue,"β","\\beta",!0),ne(ie,se,ue,"γ","\\gamma",!0),ne(ie,se,ue,"δ","\\delta",!0),ne(ie,se,ue,"ϵ","\\epsilon",!0),ne(ie,se,ue,"ζ","\\zeta",!0),ne(ie,se,ue,"η","\\eta",!0),ne(ie,se,ue,"θ","\\theta",!0),ne(ie,se,ue,"ι","\\iota",!0),ne(ie,se,ue,"κ","\\kappa",!0),ne(ie,se,ue,"λ","\\lambda",!0),ne(ie,se,ue,"μ","\\mu",!0),ne(ie,se,ue,"ν","\\nu",!0),ne(ie,se,ue,"ξ","\\xi",!0),ne(ie,se,ue,"ο","\\omicron",!0),ne(ie,se,ue,"π","\\pi",!0),ne(ie,se,ue,"ρ","\\rho",!0),ne(ie,se,ue,"σ","\\sigma",!0),ne(ie,se,ue,"τ","\\tau",!0),ne(ie,se,ue,"υ","\\upsilon",!0),ne(ie,se,ue,"ϕ","\\phi",!0),ne(ie,se,ue,"χ","\\chi",!0),ne(ie,se,ue,"ψ","\\psi",!0),ne(ie,se,ue,"ω","\\omega",!0),ne(ie,se,ue,"ε","\\varepsilon",!0),ne(ie,se,ue,"ϑ","\\vartheta",!0),ne(ie,se,ue,"ϖ","\\varpi",!0),ne(ie,se,ue,"ϱ","\\varrho",!0),ne(ie,se,ue,"ς","\\varsigma",!0),ne(ie,se,ue,"φ","\\varphi",!0),ne(ie,se,me,"∗","*",!0),ne(ie,se,me,"+","+"),ne(ie,se,me,"−","-",!0),ne(ie,se,me,"⋅","\\cdot",!0),ne(ie,se,me,"∘","\\circ",!0),ne(ie,se,me,"÷","\\div",!0),ne(ie,se,me,"±","\\pm",!0),ne(ie,se,me,"×","\\times",!0),ne(ie,se,me,"∩","\\cap",!0),ne(ie,se,me,"∪","\\cup",!0),ne(ie,se,me,"∖","\\setminus",!0),ne(ie,se,me,"∧","\\land"),ne(ie,se,me,"∨","\\lor"),ne(ie,se,me,"∧","\\wedge",!0),ne(ie,se,me,"∨","\\vee",!0),ne(ie,se,ye,"√","\\surd"),ne(ie,se,ge,"⟨","\\langle",!0),ne(ie,se,ge,"∣","\\lvert"),ne(ie,se,ge,"∥","\\lVert"),ne(ie,se,ce,"?","?"),ne(ie,se,ce,"!","!"),ne(ie,se,ce,"⟩","\\rangle",!0),ne(ie,se,ce,"∣","\\rvert"),ne(ie,se,ce,"∥","\\rVert"),ne(ie,se,ve,"=","="),ne(ie,se,ve,":",":"),ne(ie,se,ve,"≈","\\approx",!0),ne(ie,se,ve,"≅","\\cong",!0),ne(ie,se,ve,"≥","\\ge"),ne(ie,se,ve,"≥","\\geq",!0),ne(ie,se,ve,"←","\\gets"),ne(ie,se,ve,">","\\gt",!0),ne(ie,se,ve,"∈","\\in",!0),ne(ie,se,ve,"","\\@not"),ne(ie,se,ve,"⊂","\\subset",!0),ne(ie,se,ve,"⊃","\\supset",!0),ne(ie,se,ve,"⊆","\\subseteq",!0),ne(ie,se,ve,"⊇","\\supseteq",!0),ne(ie,le,ve,"⊈","\\nsubseteq",!0),ne(ie,le,ve,"⊉","\\nsupseteq",!0),ne(ie,se,ve,"⊨","\\models"),ne(ie,se,ve,"←","\\leftarrow",!0),ne(ie,se,ve,"≤","\\le"),ne(ie,se,ve,"≤","\\leq",!0),ne(ie,se,ve,"<","\\lt",!0),ne(ie,se,ve,"→","\\rightarrow",!0),ne(ie,se,ve,"→","\\to"),ne(ie,le,ve,"≱","\\ngeq",!0),ne(ie,le,ve,"≰","\\nleq",!0),ne(ie,se,be," ","\\ "),ne(ie,se,be," ","\\space"),ne(ie,se,be," ","\\nobreakspace"),ne(oe,se,be," ","\\ "),ne(oe,se,be," "," "),ne(oe,se,be," ","\\space"),ne(oe,se,be," ","\\nobreakspace"),ne(ie,se,be,null,"\\nobreak"),ne(ie,se,be,null,"\\allowbreak"),ne(ie,se,fe,",",","),ne(ie,se,fe,";",";"),ne(ie,le,me,"⊼","\\barwedge",!0),ne(ie,le,me,"⊻","\\veebar",!0),ne(ie,se,me,"⊙","\\odot",!0),ne(ie,se,me,"⊕","\\oplus",!0),ne(ie,se,me,"⊗","\\otimes",!0),ne(ie,se,ye,"∂","\\partial",!0),ne(ie,se,me,"⊘","\\oslash",!0),ne(ie,le,me,"⊚","\\circledcirc",!0),ne(ie,le,me,"⊡","\\boxdot",!0),ne(ie,se,me,"△","\\bigtriangleup"),ne(ie,se,me,"▽","\\bigtriangledown"),ne(ie,se,me,"†","\\dagger"),ne(ie,se,me,"⋄","\\diamond"),ne(ie,se,me,"⋆","\\star"),ne(ie,se,me,"◃","\\triangleleft"),ne(ie,se,me,"▹","\\triangleright"),ne(ie,se,ge,"{","\\{"),ne(oe,se,ye,"{","\\{"),ne(oe,se,ye,"{","\\textbraceleft"),ne(ie,se,ce,"}","\\}"),ne(oe,se,ye,"}","\\}"),ne(oe,se,ye,"}","\\textbraceright"),ne(ie,se,ge,"{","\\lbrace"),ne(ie,se,ce,"}","\\rbrace"),ne(ie,se,ge,"[","\\lbrack",!0),ne(oe,se,ye,"[","\\lbrack",!0),ne(ie,se,ce,"]","\\rbrack",!0),ne(oe,se,ye,"]","\\rbrack",!0),ne(ie,se,ge,"(","\\lparen",!0),ne(ie,se,ce,")","\\rparen",!0),ne(oe,se,ye,"<","\\textless",!0),ne(oe,se,ye,">","\\textgreater",!0),ne(ie,se,ge,"⌊","\\lfloor",!0),ne(ie,se,ce,"⌋","\\rfloor",!0),ne(ie,se,ge,"⌈","\\lceil",!0),ne(ie,se,ce,"⌉","\\rceil",!0),ne(ie,se,ye,"\\","\\backslash"),ne(ie,se,ye,"∣","|"),ne(ie,se,ye,"∣","\\vert"),ne(oe,se,ye,"|","\\textbar",!0),ne(ie,se,ye,"∥","\\|"),ne(ie,se,ye,"∥","\\Vert"),ne(oe,se,ye,"∥","\\textbardbl"),ne(oe,se,ye,"~","\\textasciitilde"),ne(oe,se,ye,"\\","\\textbackslash"),ne(oe,se,ye,"^","\\textasciicircum"),ne(ie,se,ve,"↑","\\uparrow",!0),ne(ie,se,ve,"⇑","\\Uparrow",!0),ne(ie,se,ve,"↓","\\downarrow",!0),ne(ie,se,ve,"⇓","\\Downarrow",!0),ne(ie,se,ve,"↕","\\updownarrow",!0),ne(ie,se,ve,"⇕","\\Updownarrow",!0),ne(ie,se,de,"∐","\\coprod"),ne(ie,se,de,"⋁","\\bigvee"),ne(ie,se,de,"⋀","\\bigwedge"),ne(ie,se,de,"⨄","\\biguplus"),ne(ie,se,de,"⋂","\\bigcap"),ne(ie,se,de,"⋃","\\bigcup"),ne(ie,se,de,"∫","\\int"),ne(ie,se,de,"∫","\\intop"),ne(ie,se,de,"∬","\\iint"),ne(ie,se,de,"∭","\\iiint"),ne(ie,se,de,"∏","\\prod"),ne(ie,se,de,"∑","\\sum"),ne(ie,se,de,"⨂","\\bigotimes"),ne(ie,se,de,"⨁","\\bigoplus"),ne(ie,se,de,"⨀","\\bigodot"),ne(ie,se,de,"∮","\\oint"),ne(ie,se,de,"∯","\\oiint"),ne(ie,se,de,"∰","\\oiiint"),ne(ie,se,de,"⨆","\\bigsqcup"),ne(ie,se,de,"∫","\\smallint"),ne(oe,se,pe,"…","\\textellipsis"),ne(ie,se,pe,"…","\\mathellipsis"),ne(oe,se,pe,"…","\\ldots",!0),ne(ie,se,pe,"…","\\ldots",!0),ne(ie,se,pe,"⋯","\\@cdots",!0),ne(ie,se,pe,"⋱","\\ddots",!0),ne(ie,se,ye,"⋮","\\varvdots"),ne(ie,se,he,"ˊ","\\acute"),ne(ie,se,he,"ˋ","\\grave"),ne(ie,se,he,"¨","\\ddot"),ne(ie,se,he,"~","\\tilde"),ne(ie,se,he,"ˉ","\\bar"),ne(ie,se,he,"˘","\\breve"),ne(ie,se,he,"ˇ","\\check"),ne(ie,se,he,"^","\\hat"),ne(ie,se,he,"⃗","\\vec"),ne(ie,se,he,"˙","\\dot"),ne(ie,se,he,"˚","\\mathring"),ne(ie,se,ue,"","\\@imath"),ne(ie,se,ue,"","\\@jmath"),ne(ie,se,ye,"ı","ı"),ne(ie,se,ye,"ȷ","ȷ"),ne(oe,se,ye,"ı","\\i",!0),ne(oe,se,ye,"ȷ","\\j",!0),ne(oe,se,ye,"ß","\\ss",!0),ne(oe,se,ye,"æ","\\ae",!0),ne(oe,se,ye,"œ","\\oe",!0),ne(oe,se,ye,"ø","\\o",!0),ne(oe,se,ye,"Æ","\\AE",!0),ne(oe,se,ye,"Œ","\\OE",!0),ne(oe,se,ye,"Ø","\\O",!0),ne(oe,se,he,"ˊ","\\'"),ne(oe,se,he,"ˋ","\\`"),ne(oe,se,he,"ˆ","\\^"),ne(oe,se,he,"˜","\\~"),ne(oe,se,he,"ˉ","\\="),ne(oe,se,he,"˘","\\u"),ne(oe,se,he,"˙","\\."),ne(oe,se,he,"¸","\\c"),ne(oe,se,he,"˚","\\r"),ne(oe,se,he,"ˇ","\\v"),ne(oe,se,he,"¨",'\\"'),ne(oe,se,he,"˝","\\H"),ne(oe,se,he,"◯","\\textcircled");var xe={"--":!0,"---":!0,"``":!0,"''":!0};ne(oe,se,ye,"–","--",!0),ne(oe,se,ye,"–","\\textendash"),ne(oe,se,ye,"—","---",!0),ne(oe,se,ye,"—","\\textemdash"),ne(oe,se,ye,"‘","`",!0),ne(oe,se,ye,"‘","\\textquoteleft"),ne(oe,se,ye,"’","'",!0),ne(oe,se,ye,"’","\\textquoteright"),ne(oe,se,ye,"“","``",!0),ne(oe,se,ye,"“","\\textquotedblleft"),ne(oe,se,ye,"”","''",!0),ne(oe,se,ye,"”","\\textquotedblright"),ne(ie,se,ye,"°","\\degree",!0),ne(oe,se,ye,"°","\\degree"),ne(oe,se,ye,"°","\\textdegree",!0),ne(ie,se,ye,"£","\\pounds"),ne(ie,se,ye,"£","\\mathsterling",!0),ne(oe,se,ye,"£","\\pounds"),ne(oe,se,ye,"£","\\textsterling",!0),ne(ie,le,ye,"✠","\\maltese"),ne(oe,le,ye,"✠","\\maltese");for(var we='0123456789/@."',ke=0;ke<we.length;ke++){var Se=we.charAt(ke);ne(ie,se,ye,Se,Se)}for(var Me='0123456789!@*()-=+";:?/.,',ze=0;ze<Me.length;ze++){var Ae=Me.charAt(ze);ne(oe,se,ye,Ae,Ae)}for(var Te="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",Be=0;Be<Te.length;Be++){var Ce=Te.charAt(Be);ne(ie,se,ue,Ce,Ce),ne(oe,se,ye,Ce,Ce)}ne(ie,le,ye,"C","ℂ"),ne(oe,le,ye,"C","ℂ"),ne(ie,le,ye,"H","ℍ"),ne(oe,le,ye,"H","ℍ"),ne(ie,le,ye,"N","ℕ"),ne(oe,le,ye,"N","ℕ"),ne(ie,le,ye,"P","ℙ"),ne(oe,le,ye,"P","ℙ"),ne(ie,le,ye,"Q","ℚ"),ne(oe,le,ye,"Q","ℚ"),ne(ie,le,ye,"R","ℝ"),ne(oe,le,ye,"R","ℝ"),ne(ie,le,ye,"Z","ℤ"),ne(oe,le,ye,"Z","ℤ"),ne(ie,se,ue,"h","ℎ"),ne(oe,se,ue,"h","ℎ");for(var Ne="",qe=0;qe<Te.length;qe++){var Ie=Te.charAt(qe);ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56320+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56372+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56424+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56580+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56736+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56788+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56840+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56944+qe)),ne(oe,se,ye,Ie,Ne),qe<26&&(ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56632+qe)),ne(oe,se,ye,Ie,Ne),ne(ie,se,ue,Ie,Ne=String.fromCharCode(55349,56476+qe)),ne(oe,se,ye,Ie,Ne))}ne(ie,se,ue,"k",Ne=String.fromCharCode(55349,56668)),ne(oe,se,ye,"k",Ne);for(var Re=0;Re<10;Re++){var He=Re.toString();ne(ie,se,ue,He,Ne=String.fromCharCode(55349,57294+Re)),ne(oe,se,ye,He,Ne),ne(ie,se,ue,He,Ne=String.fromCharCode(55349,57314+Re)),ne(oe,se,ye,He,Ne),ne(ie,se,ue,He,Ne=String.fromCharCode(55349,57324+Re)),ne(oe,se,ye,He,Ne),ne(ie,se,ue,He,Ne=String.fromCharCode(55349,57334+Re)),ne(oe,se,ye,He,Ne)}for(var Oe="ÐÞþ",Ee=0;Ee<Oe.length;Ee++){var Le=Oe.charAt(Ee);ne(ie,se,ue,Le,Le),ne(oe,se,ye,Le,Le)}var De=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["","",""],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Pe=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],Ve=function(e,t,r){return ae[r][e]&&ae[r][e].replace&&(e=ae[r][e].replace),{value:e,metrics:q(e,t,r)}},Fe=function(e,t,r,a,n){var i,o=Ve(e,t,r),s=o.metrics;if(e=o.value,s){var l=s.italic;("text"===r||a&&"mathit"===a.font)&&(l=0),i=new Z(e,s.height,s.depth,l,s.skew,s.width,n)}else"undefined"!=typeof console&&console.warn("No character metrics for '"+e+"' in style '"+t+"' and mode '"+r+"'"),i=new Z(e,0,0,0,0,0,n);if(a){i.maxFontSize=a.sizeMultiplier,a.style.isTight()&&i.classes.push("mtight");var h=a.getColor();h&&(i.style.color=h)}return i},Ge=(e,t)=>{if(G(e.classes)!==G(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;if(1===e.classes.length){var r=e.classes[0];if("mbin"===r||"mord"===r)return!1}for(var a in e.style)if(e.style.hasOwnProperty(a)&&e.style[a]!==t.style[a])return!1;for(var n in t.style)if(t.style.hasOwnProperty(n)&&e.style[n]!==t.style[n])return!1;return!0},Ue=function(e){for(var t=0,r=0,a=0,n=0;n<e.children.length;n++){var i=e.children[n];i.height>t&&(t=i.height),i.depth>r&&(r=i.depth),i.maxFontSize>a&&(a=i.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=a},Ye=function(e,t,r,a){var n=new W(e,t,r,a);return Ue(n),n},Xe=(e,t,r,a)=>new W(e,t,r,a),We=function(e){var t=new T(e);return Ue(t),t},_e=function(e,t,r){var a="";switch(e){case"amsrm":a="AMS";break;case"textrm":a="Main";break;case"textsf":a="SansSerif";break;case"texttt":a="Typewriter";break;default:a=e}return a+"-"+("textbf"===t&&"textit"===r?"BoldItalic":"textbf"===t?"Bold":"textit"===t?"Italic":"Regular")},je={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},$e={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},Ze={fontMap:je,makeSymbol:Fe,mathsym:function(e,t,r,a){return void 0===a&&(a=[]),"boldsymbol"===r.font&&Ve(e,"Main-Bold",t).metrics?Fe(e,"Main-Bold",t,r,a.concat(["mathbf"])):"\\"===e||"main"===ae[t][e].font?Fe(e,"Main-Regular",t,r,a):Fe(e,"AMS-Regular",t,r,a.concat(["amsrm"]))},makeSpan:Ye,makeSvgSpan:Xe,makeLineSpan:function(e,t,r){var a=Ye([e],[],t);return a.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),a.style.borderBottomWidth=F(a.height),a.maxFontSize=1,a},makeAnchor:function(e,t,r,a){var n=new _(e,t,r,a);return Ue(n),n},makeFragment:We,wrapFragment:function(e,t){return e instanceof T?Ye([],[e],t):e},makeVList:function(e,t){for(var{children:r,depth:a}=function(e){if("individualShift"===e.positionType){for(var t=e.children,r=[t[0]],a=-t[0].shift-t[0].elem.depth,n=a,i=1;i<t.length;i++){var o=-t[i].shift-n-t[i].elem.depth,s=o-(t[i-1].elem.height+t[i-1].elem.depth);n+=o,r.push({type:"kern",size:s}),r.push(t[i])}return{children:r,depth:a}}var l;if("top"===e.positionType){for(var h=e.positionData,m=0;m<e.children.length;m++){var c=e.children[m];h-="kern"===c.type?c.size:c.elem.height+c.elem.depth}l=h}else if("bottom"===e.positionType)l=-e.positionData;else{var p=e.children[0];if("elem"!==p.type)throw new Error('First child must have type "elem".');if("shift"===e.positionType)l=-p.elem.depth-e.positionData;else{if("firstBaseline"!==e.positionType)throw new Error("Invalid positionType "+e.positionType+".");l=-p.elem.depth}}return{children:e.children,depth:l}}(e),n=0,i=0;i<r.length;i++){var o=r[i];if("elem"===o.type){var s=o.elem;n=Math.max(n,s.maxFontSize,s.height)}}n+=2;var l=Ye(["pstrut"],[]);l.style.height=F(n);for(var h=[],m=a,c=a,p=a,u=0;u<r.length;u++){var d=r[u];if("kern"===d.type)p+=d.size;else{var g=d.elem,f=d.wrapperClasses||[],v=d.wrapperStyle||{},b=Ye(f,[l,g],void 0,v);b.style.top=F(-n-p-g.depth),d.marginLeft&&(b.style.marginLeft=d.marginLeft),d.marginRight&&(b.style.marginRight=d.marginRight),h.push(b),p+=g.height+g.depth}m=Math.min(m,p),c=Math.max(c,p)}var y,x=Ye(["vlist"],h);if(x.style.height=F(c),m<0){var w=Ye([],[]),k=Ye(["vlist"],[w]);k.style.height=F(-m);var S=Ye(["vlist-s"],[new Z("​")]);y=[Ye(["vlist-r"],[x,S]),Ye(["vlist-r"],[k])]}else y=[Ye(["vlist-r"],[x])];var M=Ye(["vlist-t"],y);return 2===y.length&&M.classes.push("vlist-t2"),M.height=c,M.depth=-m,M},makeOrd:function(e,t,r){var a=e.mode,i=e.text,o=["mord"],s="math"===a||"text"===a&&t.font,l=s?t.font:t.fontFamily;if(55349===i.charCodeAt(0)){var[h,m]=function(e,t){var r=1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536,a="math"===t?0:1;if(119808<=r&&r<120484){var i=Math.floor((r-119808)/26);return[De[i][2],De[i][a]]}if(120782<=r&&r<=120831){var o=Math.floor((r-120782)/10);return[Pe[o][2],Pe[o][a]]}if(120485===r||120486===r)return[De[0][2],De[0][a]];if(120486<r&&r<120782)return["",""];throw new n("Unsupported character: "+e)}(i,a);return Fe(i,h,a,t,o.concat(m))}if(l){var c,p;if("boldsymbol"===l){var u=function(e,t,r,a,n){return"textord"!==n&&Ve(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}}(i,a,0,0,r);c=u.fontName,p=[u.fontClass]}else s?(c=je[l].fontName,p=[l]):(c=_e(l,t.fontWeight,t.fontShape),p=[l,t.fontWeight,t.fontShape]);if(Ve(i,c,a).metrics)return Fe(i,c,a,t,o.concat(p));if(xe.hasOwnProperty(i)&&"Typewriter"===c.slice(0,10)){for(var d=[],g=0;g<i.length;g++)d.push(Fe(i[g],c,a,t,o.concat(p)));return We(d)}}if("mathord"===r)return Fe(i,"Math-Italic",a,t,o.concat(["mathnormal"]));if("textord"===r){var f=ae[a][i]&&ae[a][i].font;if("ams"===f){var v=_e("amsrm",t.fontWeight,t.fontShape);return Fe(i,v,a,t,o.concat("amsrm",t.fontWeight,t.fontShape))}if("main"!==f&&f){var b=_e(f,t.fontWeight,t.fontShape);return Fe(i,b,a,t,o.concat(b,t.fontWeight,t.fontShape))}var y=_e("textrm",t.fontWeight,t.fontShape);return Fe(i,y,a,t,o.concat(t.fontWeight,t.fontShape))}throw new Error("unexpected type: "+r+" in makeOrd")},makeGlue:(e,t)=>{var r=Ye(["mspace"],[],t),a=V(e,t);return r.style.marginRight=F(a),r},staticSvg:function(e,t){var[r,a,n]=$e[e],i=new J(r),o=new K([i],{width:F(a),height:F(n),style:"width:"+F(a),viewBox:"0 0 "+1e3*a+" "+1e3*n,preserveAspectRatio:"xMinYMin"}),s=Xe(["overlay"],[o],t);return s.height=n,s.style.height=F(n),s.style.width=F(a),s},svgData:$e,tryCombineChars:e=>{for(var t=0;t<e.length-1;t++){var r=e[t],a=e[t+1];r instanceof Z&&a instanceof Z&&Ge(r,a)&&(r.text+=a.text,r.height=Math.max(r.height,a.height),r.depth=Math.max(r.depth,a.depth),r.italic=a.italic,e.splice(t+1,1),t--)}return e}},Ke={number:3,unit:"mu"},Je={number:4,unit:"mu"},Qe={number:5,unit:"mu"},et={mord:{mop:Ke,mbin:Je,mrel:Qe,minner:Ke},mop:{mord:Ke,mop:Ke,mrel:Qe,minner:Ke},mbin:{mord:Je,mop:Je,mopen:Je,minner:Je},mrel:{mord:Qe,mop:Qe,mopen:Qe,minner:Qe},mopen:{},mclose:{mop:Ke,mbin:Je,mrel:Qe,minner:Ke},mpunct:{mord:Ke,mop:Ke,mrel:Qe,mopen:Ke,mclose:Ke,mpunct:Ke,minner:Ke},minner:{mord:Ke,mop:Ke,mbin:Je,mrel:Qe,mopen:Ke,mpunct:Ke,minner:Ke}},tt={mord:{mop:Ke},mop:{mord:Ke,mop:Ke},mbin:{},mrel:{},mopen:{},mclose:{mop:Ke},mpunct:{},minner:{mop:Ke}},rt={},at={},nt={};function it(e){for(var{type:t,names:r,props:a,handler:n,htmlBuilder:i,mathmlBuilder:o}=e,s={type:t,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:void 0===a.allowedInMath||a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:n},l=0;l<r.length;++l)rt[r[l]]=s;t&&(i&&(at[t]=i),o&&(nt[t]=o))}function ot(e){var{type:t,htmlBuilder:r,mathmlBuilder:a}=e;it({type:t,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:a})}var st=function(e){return"ordgroup"===e.type&&1===e.body.length?e.body[0]:e},lt=function(e){return"ordgroup"===e.type?e.body:[e]},ht=Ze.makeSpan,mt=["leftmost","mbin","mopen","mrel","mop","mpunct"],ct=["rightmost","mrel","mclose","mpunct"],pt={display:w.DISPLAY,text:w.TEXT,script:w.SCRIPT,scriptscript:w.SCRIPTSCRIPT},ut={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},dt=function(e,t,r,a){void 0===a&&(a=[null,null]);for(var n=[],i=0;i<e.length;i++){var o=xt(e[i],t);if(o instanceof T){var s=o.children;n.push(...s)}else n.push(o)}if(Ze.tryCombineChars(n),!r)return n;var l=t;if(1===e.length){var m=e[0];"sizing"===m.type?l=t.havingSize(m.size):"styling"===m.type&&(l=t.havingStyle(pt[m.style]))}var c=ht([a[0]||"leftmost"],[],t),p=ht([a[1]||"rightmost"],[],t),u="root"===r;return gt(n,((e,t)=>{var r=t.classes[0],a=e.classes[0];"mbin"===r&&h.contains(ct,a)?t.classes[0]="mord":"mbin"===a&&h.contains(mt,r)&&(e.classes[0]="mord")}),{node:c},p,u),gt(n,((e,t)=>{var r=bt(t),a=bt(e),n=r&&a?e.hasClass("mtight")?tt[r][a]:et[r][a]:null;if(n)return Ze.makeGlue(n,l)}),{node:c},p,u),n},gt=function e(t,r,a,n,i){n&&t.push(n);for(var o=0;o<t.length;o++){var s=t[o],l=ft(s);if(l)e(l.children,r,a,null,i);else{var h=!s.hasClass("mspace");if(h){var m=r(s,a.node);m&&(a.insertAfter?a.insertAfter(m):(t.unshift(m),o++))}h?a.node=s:i&&s.hasClass("newline")&&(a.node=ht(["leftmost"])),a.insertAfter=(e=>r=>{t.splice(e+1,0,r),o++})(o)}}n&&t.pop()},ft=function(e){return e instanceof T||e instanceof _||e instanceof W&&e.hasClass("enclosing")?e:null},vt=function e(t,r){var a=ft(t);if(a){var n=a.children;if(n.length){if("right"===r)return e(n[n.length-1],"right");if("left"===r)return e(n[0],"left")}}return t},bt=function(e,t){return e?(t&&(e=vt(e,t)),ut[e.classes[0]]||null):null},yt=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return ht(t.concat(r))},xt=function(e,t,r){if(!e)return ht();if(at[e.type]){var a=at[e.type](e,t);if(r&&t.size!==r.size){a=ht(t.sizingClasses(r),[a],t);var i=t.sizeMultiplier/r.sizeMultiplier;a.height*=i,a.depth*=i}return a}throw new n("Got group of unknown type: '"+e.type+"'")};function wt(e,t){var r=ht(["base"],e,t),a=ht(["strut"]);return a.style.height=F(r.height+r.depth),r.depth&&(a.style.verticalAlign=F(-r.depth)),r.children.unshift(a),r}function kt(e,t){var r=null;1===e.length&&"tag"===e[0].type&&(r=e[0].tag,e=e[0].body);var a,n=dt(e,t,"root");2===n.length&&n[1].hasClass("tag")&&(a=n.pop());for(var i,o=[],s=[],l=0;l<n.length;l++)if(s.push(n[l]),n[l].hasClass("mbin")||n[l].hasClass("mrel")||n[l].hasClass("allowbreak")){for(var h=!1;l<n.length-1&&n[l+1].hasClass("mspace")&&!n[l+1].hasClass("newline");)l++,s.push(n[l]),n[l].hasClass("nobreak")&&(h=!0);h||(o.push(wt(s,t)),s=[])}else n[l].hasClass("newline")&&(s.pop(),s.length>0&&(o.push(wt(s,t)),s=[]),o.push(n[l]));s.length>0&&o.push(wt(s,t)),r?((i=wt(dt(r,t,!0))).classes=["tag"],o.push(i)):a&&o.push(a);var m=ht(["katex-html"],o);if(m.setAttribute("aria-hidden","true"),i){var c=i.children[0];c.style.height=F(m.height+m.depth),m.depth&&(c.style.verticalAlign=F(-m.depth))}return m}function St(e){return new T(e)}class Mt{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=G(this.classes));for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=h.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+h.escape(G(this.classes))+'"'),e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</"+this.type+">"}toText(){return this.children.map((e=>e.toText())).join("")}}class zt{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return h.escape(this.toText())}toText(){return this.text}}var At={MathNode:Mt,TextNode:zt,SpaceNode:class{constructor(e){this.width=void 0,this.character=void 0,this.width=e,this.character=e>=.05555&&e<=.05556?" ":e>=.1666&&e<=.1667?" ":e>=.2222&&e<=.2223?" ":e>=.2777&&e<=.2778?"  ":e>=-.05556&&e<=-.05555?" ⁣":e>=-.1667&&e<=-.1666?" ⁣":e>=-.2223&&e<=-.2222?" ⁣":e>=-.2778&&e<=-.2777?" ⁣":null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",F(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+F(this.width)+'"/>'}toText(){return this.character?this.character:" "}},newDocumentFragment:St},Tt=function(e,t,r){return!ae[t][e]||!ae[t][e].replace||55349===e.charCodeAt(0)||xe.hasOwnProperty(e)&&r&&(r.fontFamily&&"tt"===r.fontFamily.slice(4,6)||r.font&&"tt"===r.font.slice(4,6))||(e=ae[t][e].replace),new At.TextNode(e)},Bt=function(e){return 1===e.length?e[0]:new At.MathNode("mrow",e)},Ct=function(e,t){if("texttt"===t.fontFamily)return"monospace";if("textsf"===t.fontFamily)return"textit"===t.fontShape&&"textbf"===t.fontWeight?"sans-serif-bold-italic":"textit"===t.fontShape?"sans-serif-italic":"textbf"===t.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===t.fontShape&&"textbf"===t.fontWeight)return"bold-italic";if("textit"===t.fontShape)return"italic";if("textbf"===t.fontWeight)return"bold";var r=t.font;if(!r||"mathnormal"===r)return null;var a=e.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"textord"===e.type?"bold":"bold-italic";if("mathbf"===r)return"bold";if("mathbb"===r)return"double-struck";if("mathfrak"===r)return"fraktur";if("mathscr"===r||"mathcal"===r)return"script";if("mathsf"===r)return"sans-serif";if("mathtt"===r)return"monospace";var n=e.text;return h.contains(["\\imath","\\jmath"],n)?null:(ae[a][n]&&ae[a][n].replace&&(n=ae[a][n].replace),q(n,Ze.fontMap[r].fontName,a)?Ze.fontMap[r].variant:null)},Nt=function(e,t,r){if(1===e.length){var a=It(e[0],t);return r&&a instanceof Mt&&"mo"===a.type&&(a.setAttribute("lspace","0em"),a.setAttribute("rspace","0em")),[a]}for(var n,i=[],o=0;o<e.length;o++){var s=It(e[o],t);if(s instanceof Mt&&n instanceof Mt){if("mtext"===s.type&&"mtext"===n.type&&s.getAttribute("mathvariant")===n.getAttribute("mathvariant")){n.children.push(...s.children);continue}if("mn"===s.type&&"mn"===n.type){n.children.push(...s.children);continue}if("mi"===s.type&&1===s.children.length&&"mn"===n.type){var l=s.children[0];if(l instanceof zt&&"."===l.text){n.children.push(...s.children);continue}}else if("mi"===n.type&&1===n.children.length){var h=n.children[0];if(h instanceof zt&&"̸"===h.text&&("mo"===s.type||"mi"===s.type||"mn"===s.type)){var m=s.children[0];m instanceof zt&&m.text.length>0&&(m.text=m.text.slice(0,1)+"̸"+m.text.slice(1),i.pop())}}}i.push(s),n=s}return i},qt=function(e,t,r){return Bt(Nt(e,t,r))},It=function(e,t){if(!e)return new At.MathNode("mrow");if(nt[e.type])return nt[e.type](e,t);throw new n("Got group of unknown type: '"+e.type+"'")};function Rt(e,t,r,a,n){var i,o=Nt(e,r);i=1===o.length&&o[0]instanceof Mt&&h.contains(["mrow","mtable"],o[0].type)?o[0]:new At.MathNode("mrow",o);var s=new At.MathNode("annotation",[new At.TextNode(t)]);s.setAttribute("encoding","application/x-tex");var l=new At.MathNode("semantics",[i,s]),m=new At.MathNode("math",[l]);m.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&m.setAttribute("display","block");var c=n?"katex":"katex-mathml";return Ze.makeSpan([c],[m])}var Ht=function(e){return new E({style:e.displayMode?w.DISPLAY:w.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},Ot=function(e,t){if(t.displayMode){var r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=Ze.makeSpan(r,[e])}return e},Et={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},Lt={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},Dt=function(e,t,r,a,n){var i,o=e.height+e.depth+r+a;if(/fbox|color|angl/.test(t)){if(i=Ze.makeSpan(["stretchy",t],[],n),"fbox"===t){var s=n.color&&n.getColor();s&&(i.style.borderColor=s)}}else{var l=[];/^[bx]cancel$/.test(t)&&l.push(new Q({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&l.push(new Q({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var h=new K(l,{width:"100%",height:F(o)});i=Ze.makeSvgSpan([],[h],n)}return i.height=o,i.style.height=F(o),i},Pt=function(e){var t=new At.MathNode("mo",[new At.TextNode(Et[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},Vt=function(e,t){var{span:r,minWidth:a,height:n}=function(){var r=4e5,a=e.label.slice(1);if(h.contains(["widehat","widecheck","widetilde","utilde"],a)){var n,i,o,s="ordgroup"===(d=e.base).type?d.body.length:1;if(s>5)"widehat"===a||"widecheck"===a?(n=420,r=2364,o=.42,i=a+"4"):(n=312,r=2340,o=.34,i="tilde4");else{var l=[1,1,2,2,3,3][s];"widehat"===a||"widecheck"===a?(r=[0,1062,2364,2364,2364][l],n=[0,239,300,360,420][l],o=[0,.24,.3,.3,.36,.42][l],i=a+l):(r=[0,600,1033,2339,2340][l],n=[0,260,286,306,312][l],o=[0,.26,.286,.3,.306,.34][l],i="tilde"+l)}var m=new J(i),c=new K([m],{width:"100%",height:F(o),viewBox:"0 0 "+r+" "+n,preserveAspectRatio:"none"});return{span:Ze.makeSvgSpan([],[c],t),minWidth:0,height:o}}var p,u,d,g=[],f=Lt[a],[v,b,y]=f,x=y/1e3,w=v.length;if(1===w)p=["hide-tail"],u=[f[3]];else if(2===w)p=["halfarrow-left","halfarrow-right"],u=["xMinYMin","xMaxYMin"];else{if(3!==w)throw new Error("Correct katexImagesData or update code here to support\n                    "+w+" children.");p=["brace-left","brace-center","brace-right"],u=["xMinYMin","xMidYMin","xMaxYMin"]}for(var k=0;k<w;k++){var S=new J(v[k]),M=new K([S],{width:"400em",height:F(x),viewBox:"0 0 "+r+" "+y,preserveAspectRatio:u[k]+" slice"}),z=Ze.makeSvgSpan([p[k]],[M],t);if(1===w)return{span:z,minWidth:b,height:x};z.style.height=F(x),g.push(z)}return{span:Ze.makeSpan(["stretchy"],g,t),minWidth:b,height:x}}();return r.height=n,r.style.height=F(n),a>0&&(r.style.minWidth=F(a)),r};function Ft(e,t){if(!e||e.type!==t)throw new Error("Expected node of type "+t+", but got "+(e?"node of type "+e.type:String(e)));return e}function Gt(e){var t=Ut(e);if(!t)throw new Error("Expected node of symbol group type, but got "+(e?"node of type "+e.type:String(e)));return t}function Ut(e){return e&&("atom"===e.type||re.hasOwnProperty(e.type))?e:null}var Yt=(e,t)=>{var r,a,n;e&&"supsub"===e.type?(r=(a=Ft(e.base,"accent")).base,e.base=r,n=function(e){if(e instanceof W)return e;throw new Error("Expected span<HtmlDomNode> but got "+String(e)+".")}(xt(e,t)),e.base=a):r=(a=Ft(e,"accent")).base;var i=xt(r,t.havingCrampedStyle()),o=0;if(a.isShifty&&h.isCharacterBox(r)){var s=h.getBaseElem(r);o=ee(xt(s,t.havingCrampedStyle())).skew}var l,m="\\c"===a.label,c=m?i.height+i.depth:Math.min(i.height,t.fontMetrics().xHeight);if(a.isStretchy)l=Vt(a,t),l=Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i},{type:"elem",elem:l,wrapperClasses:["svg-align"],wrapperStyle:o>0?{width:"calc(100% - "+F(2*o)+")",marginLeft:F(2*o)}:void 0}]},t);else{var p,u;"\\vec"===a.label?(p=Ze.staticSvg("vec",t),u=Ze.svgData.vec[1]):((p=ee(p=Ze.makeOrd({mode:a.mode,text:a.label},t,"textord"))).italic=0,u=p.width,m&&(c+=p.depth)),l=Ze.makeSpan(["accent-body"],[p]);var d="\\textcircled"===a.label;d&&(l.classes.push("accent-full"),c=i.height);var g=o;d||(g-=u/2),l.style.left=F(g),"\\textcircled"===a.label&&(l.style.top=".2em"),l=Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i},{type:"kern",size:-c},{type:"elem",elem:l}]},t)}var f=Ze.makeSpan(["mord","accent"],[l],t);return n?(n.children[0]=f,n.height=Math.max(f.height,n.height),n.classes[0]="mord",n):f},Xt=(e,t)=>{var r=e.isStretchy?Pt(e.label):new At.MathNode("mo",[Tt(e.label,e.mode)]),a=new At.MathNode("mover",[It(e.base,t),r]);return a.setAttribute("accent","true"),a},Wt=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map((e=>"\\"+e)).join("|"));it({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(e,t)=>{var r=st(t[0]),a=!Wt.test(e.funcName),n=!a||"\\widehat"===e.funcName||"\\widetilde"===e.funcName||"\\widecheck"===e.funcName;return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:a,isShifty:n,base:r}},htmlBuilder:Yt,mathmlBuilder:Xt}),it({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(e,t)=>{var r=t[0],a=e.parser.mode;return"math"===a&&(e.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+e.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:Yt,mathmlBuilder:Xt}),it({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"accentUnder",mode:r.mode,label:a,base:n}},htmlBuilder:(e,t)=>{var r=xt(e.base,t),a=Vt(e,t),n="\\utilde"===e.label?.12:0,i=Ze.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:n},{type:"elem",elem:r}]},t);return Ze.makeSpan(["mord","accentunder"],[i],t)},mathmlBuilder:(e,t)=>{var r=Pt(e.label),a=new At.MathNode("munder",[It(e.base,t),r]);return a.setAttribute("accentunder","true"),a}});var _t=e=>{var t=new At.MathNode("mpadded",e?[e]:[]);return t.setAttribute("width","+0.6em"),t.setAttribute("lspace","0.3em"),t};it({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:a,funcName:n}=e;return{type:"xArrow",mode:a.mode,label:n,body:t[0],below:r[0]}},htmlBuilder(e,t){var r,a=t.style,n=t.havingStyle(a.sup()),i=Ze.wrapFragment(xt(e.body,n,t),t),o="\\x"===e.label.slice(0,2)?"x":"cd";i.classes.push(o+"-arrow-pad"),e.below&&(n=t.havingStyle(a.sub()),(r=Ze.wrapFragment(xt(e.below,n,t),t)).classes.push(o+"-arrow-pad"));var s,l=Vt(e,t),h=-t.fontMetrics().axisHeight+.5*l.height,m=-t.fontMetrics().axisHeight-.5*l.height-.111;if((i.depth>.25||"\\xleftequilibrium"===e.label)&&(m-=i.depth),r){var c=-t.fontMetrics().axisHeight+r.height+.5*l.height+.111;s=Ze.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:m},{type:"elem",elem:l,shift:h},{type:"elem",elem:r,shift:c}]},t)}else s=Ze.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:m},{type:"elem",elem:l,shift:h}]},t);return s.children[0].children[0].children[1].classes.push("svg-align"),Ze.makeSpan(["mrel","x-arrow"],[s],t)},mathmlBuilder(e,t){var r,a=Pt(e.label);if(a.setAttribute("minsize","x"===e.label.charAt(0)?"1.75em":"3.0em"),e.body){var n=_t(It(e.body,t));if(e.below){var i=_t(It(e.below,t));r=new At.MathNode("munderover",[a,i,n])}else r=new At.MathNode("mover",[a,n])}else if(e.below){var o=_t(It(e.below,t));r=new At.MathNode("munder",[a,o])}else r=_t(),r=new At.MathNode("mover",[a,r]);return r}});var jt=Ze.makeSpan;function $t(e,t){var r=dt(e.body,t,!0);return jt([e.mclass],r,t)}function Zt(e,t){var r,a=Nt(e.body,t);return"minner"===e.mclass?r=new At.MathNode("mpadded",a):"mord"===e.mclass?e.isCharacterBox?(r=a[0]).type="mi":r=new At.MathNode("mi",a):(e.isCharacterBox?(r=a[0]).type="mo":r=new At.MathNode("mo",a),"mbin"===e.mclass?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):"mpunct"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):"mopen"===e.mclass||"mclose"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0em"):"minner"===e.mclass&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}it({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+a.slice(5),body:lt(n),isCharacterBox:h.isCharacterBox(n)}},htmlBuilder:$t,mathmlBuilder:Zt});var Kt=e=>{var t="ordgroup"===e.type&&e.body.length?e.body[0]:e;return"atom"!==t.type||"bin"!==t.family&&"rel"!==t.family?"mord":"m"+t.family};it({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(e,t){var{parser:r}=e;return{type:"mclass",mode:r.mode,mclass:Kt(t[0]),body:lt(t[1]),isCharacterBox:h.isCharacterBox(t[1])}}}),it({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(e,t){var r,{parser:a,funcName:n}=e,i=t[1],o=t[0];r="\\stackrel"!==n?Kt(i):"mrel";var s={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:"\\stackrel"!==n,body:lt(i)},l={type:"supsub",mode:o.mode,base:s,sup:"\\underset"===n?null:o,sub:"\\underset"===n?o:null};return{type:"mclass",mode:a.mode,mclass:r,body:[l],isCharacterBox:h.isCharacterBox(l)}},htmlBuilder:$t,mathmlBuilder:Zt}),it({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"pmb",mode:r.mode,mclass:Kt(t[0]),body:lt(t[0])}},htmlBuilder(e,t){var r=dt(e.body,t,!0),a=Ze.makeSpan([e.mclass],r,t);return a.style.textShadow="0.02em 0.01em 0.04px",a},mathmlBuilder(e,t){var r=Nt(e.body,t),a=new At.MathNode("mstyle",r);return a.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),a}});var Jt={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},Qt=e=>"textord"===e.type&&"@"===e.text;function er(e,t,r){var a=Jt[e];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(a,[t[0]],[t[1]]);case"\\uparrow":case"\\downarrow":var n={type:"atom",text:a,mode:"math",family:"rel"},i={type:"ordgroup",mode:"math",body:[r.callFunction("\\\\cdleft",[t[0]],[]),r.callFunction("\\Big",[n],[]),r.callFunction("\\\\cdright",[t[1]],[])]};return r.callFunction("\\\\cdparent",[i],[]);case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":return r.callFunction("\\Big",[{type:"textord",text:"\\Vert",mode:"math"}],[]);default:return{type:"textord",text:" ",mode:"math"}}}it({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e;return{type:"cdlabel",mode:r.mode,side:a.slice(4),label:t[0]}},htmlBuilder(e,t){var r=t.havingStyle(t.style.sup()),a=Ze.wrapFragment(xt(e.label,r,t),t);return a.classes.push("cd-label-"+e.side),a.style.bottom=F(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(e,t){var r=new At.MathNode("mrow",[It(e.label,t)]);return(r=new At.MathNode("mpadded",[r])).setAttribute("width","0"),"left"===e.side&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),(r=new At.MathNode("mstyle",[r])).setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),it({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(e,t){var{parser:r}=e;return{type:"cdlabelparent",mode:r.mode,fragment:t[0]}},htmlBuilder(e,t){var r=Ze.wrapFragment(xt(e.fragment,t),t);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder(e,t){return new At.MathNode("mrow",[It(e.fragment,t)])}}),it({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(e,t){for(var{parser:r}=e,a=Ft(t[0],"ordgroup").body,i="",o=0;o<a.length;o++){i+=Ft(a[o],"textord").text}var s,l=parseInt(i);if(isNaN(l))throw new n("\\@char has non-numeric argument "+i);if(l<0||l>=1114111)throw new n("\\@char with invalid code point "+i);return l<=65535?s=String.fromCharCode(l):(l-=65536,s=String.fromCharCode(55296+(l>>10),56320+(1023&l))),{type:"textord",mode:r.mode,text:s}}});var tr=(e,t)=>{var r=dt(e.body,t.withColor(e.color),!1);return Ze.makeFragment(r)},rr=(e,t)=>{var r=Nt(e.body,t.withColor(e.color)),a=new At.MathNode("mstyle",r);return a.setAttribute("mathcolor",e.color),a};it({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(e,t){var{parser:r}=e,a=Ft(t[0],"color-token").color,n=t[1];return{type:"color",mode:r.mode,color:a,body:lt(n)}},htmlBuilder:tr,mathmlBuilder:rr}),it({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(e,t){var{parser:r,breakOnTokenText:a}=e,n=Ft(t[0],"color-token").color;r.gullet.macros.set("\\current@color",n);var i=r.parseExpression(!0,a);return{type:"color",mode:r.mode,color:n,body:i}},htmlBuilder:tr,mathmlBuilder:rr}),it({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:1,argTypes:["size"],allowedInText:!0},handler(e,t,r){var{parser:a}=e,n=r[0],i=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:i,size:n&&Ft(n,"size").value}},htmlBuilder(e,t){var r=Ze.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=F(V(e.size,t)))),r},mathmlBuilder(e,t){var r=new At.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",F(V(e.size,t)))),r}});var ar={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},nr=e=>{var t=e.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(t))throw new n("Expected a control sequence",e);return t},ir=(e,t,r,a)=>{var n=e.gullet.macros.get(r.text);null==n&&(r.noexpand=!0,n={tokens:[r],numArgs:0,unexpandable:!e.gullet.isExpandable(r.text)}),e.gullet.macros.set(t,n,a)};it({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(e){var{parser:t,funcName:r}=e;t.consumeSpaces();var a=t.fetch();if(ar[a.text])return"\\global"!==r&&"\\\\globallong"!==r||(a.text=ar[a.text]),Ft(t.parseFunction(),"internal");throw new n("Invalid token after macro prefix",a)}}),it({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,a=t.gullet.popToken(),i=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(i))throw new n("Expected a control sequence",a);for(var o,s=0,l=[[]];"{"!==t.gullet.future().text;)if("#"===(a=t.gullet.popToken()).text){if("{"===t.gullet.future().text){o=t.gullet.future(),l[s].push("{");break}if(a=t.gullet.popToken(),!/^[1-9]$/.test(a.text))throw new n('Invalid argument number "'+a.text+'"');if(parseInt(a.text)!==s+1)throw new n('Argument number "'+a.text+'" out of order');s++,l.push([])}else{if("EOF"===a.text)throw new n("Expected a macro definition");l[s].push(a.text)}var{tokens:h}=t.gullet.consumeArg();return o&&h.unshift(o),"\\edef"!==r&&"\\xdef"!==r||(h=t.gullet.expandTokens(h)).reverse(),t.gullet.macros.set(i,{tokens:h,numArgs:s,delimiters:l},r===ar[r]),{type:"internal",mode:t.mode}}}),it({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,a=nr(t.gullet.popToken());t.gullet.consumeSpaces();var n=(e=>{var t=e.gullet.popToken();return"="===t.text&&" "===(t=e.gullet.popToken()).text&&(t=e.gullet.popToken()),t})(t);return ir(t,a,n,"\\\\globallet"===r),{type:"internal",mode:t.mode}}}),it({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,a=nr(t.gullet.popToken()),n=t.gullet.popToken(),i=t.gullet.popToken();return ir(t,a,i,"\\\\globalfuture"===r),t.gullet.pushToken(i),t.gullet.pushToken(n),{type:"internal",mode:t.mode}}});var or=function(e,t,r){var a=q(ae.math[e]&&ae.math[e].replace||e,t,r);if(!a)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return a},sr=function(e,t,r,a){var n=r.havingBaseStyle(t),i=Ze.makeSpan(a.concat(n.sizingClasses(r)),[e],r),o=n.sizeMultiplier/r.sizeMultiplier;return i.height*=o,i.depth*=o,i.maxFontSize=n.sizeMultiplier,i},lr=function(e,t,r){var a=t.havingBaseStyle(r),n=(1-t.sizeMultiplier/a.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=F(n),e.height-=n,e.depth+=n},hr=function(e,t,r,a,n,i){var o=function(e,t,r,a){return Ze.makeSymbol(e,"Size"+t+"-Regular",r,a)}(e,t,n,a),s=sr(Ze.makeSpan(["delimsizing","size"+t],[o],a),w.TEXT,a,i);return r&&lr(s,a,w.TEXT),s},mr=function(e,t,r){var a;return a="Size1-Regular"===t?"delim-size1":"delim-size4",{type:"elem",elem:Ze.makeSpan(["delimsizinginner",a],[Ze.makeSpan([],[Ze.makeSymbol(e,t,r)])])}},cr=function(e,t,r){var a=B["Size4-Regular"][e.charCodeAt(0)]?B["Size4-Regular"][e.charCodeAt(0)][4]:B["Size1-Regular"][e.charCodeAt(0)][4],n=new J("inner",function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145zM367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z";case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257zM478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z";default:return""}}(e,Math.round(1e3*t))),i=new K([n],{width:F(a),height:F(t),style:"width:"+F(a),viewBox:"0 0 "+1e3*a+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),o=Ze.makeSvgSpan([],[i],r);return o.height=t,o.style.height=F(t),o.style.width=F(a),{type:"elem",elem:o}},pr={type:"kern",size:-.008},ur=["|","\\lvert","\\rvert","\\vert"],dr=["\\|","\\lVert","\\rVert","\\Vert"],gr=function(e,t,r,a,n,i){var o,s,l,m,c="",p=0;o=l=m=e,s=null;var u="Size1-Regular";"\\uparrow"===e?l=m="⏐":"\\Uparrow"===e?l=m="‖":"\\downarrow"===e?o=l="⏐":"\\Downarrow"===e?o=l="‖":"\\updownarrow"===e?(o="\\uparrow",l="⏐",m="\\downarrow"):"\\Updownarrow"===e?(o="\\Uparrow",l="‖",m="\\Downarrow"):h.contains(ur,e)?(l="∣",c="vert",p=333):h.contains(dr,e)?(l="∥",c="doublevert",p=556):"["===e||"\\lbrack"===e?(o="⎡",l="⎢",m="⎣",u="Size4-Regular",c="lbrack",p=667):"]"===e||"\\rbrack"===e?(o="⎤",l="⎥",m="⎦",u="Size4-Regular",c="rbrack",p=667):"\\lfloor"===e||"⌊"===e?(l=o="⎢",m="⎣",u="Size4-Regular",c="lfloor",p=667):"\\lceil"===e||"⌈"===e?(o="⎡",l=m="⎢",u="Size4-Regular",c="lceil",p=667):"\\rfloor"===e||"⌋"===e?(l=o="⎥",m="⎦",u="Size4-Regular",c="rfloor",p=667):"\\rceil"===e||"⌉"===e?(o="⎤",l=m="⎥",u="Size4-Regular",c="rceil",p=667):"("===e||"\\lparen"===e?(o="⎛",l="⎜",m="⎝",u="Size4-Regular",c="lparen",p=875):")"===e||"\\rparen"===e?(o="⎞",l="⎟",m="⎠",u="Size4-Regular",c="rparen",p=875):"\\{"===e||"\\lbrace"===e?(o="⎧",s="⎨",m="⎩",l="⎪",u="Size4-Regular"):"\\}"===e||"\\rbrace"===e?(o="⎫",s="⎬",m="⎭",l="⎪",u="Size4-Regular"):"\\lgroup"===e||"⟮"===e?(o="⎧",m="⎩",l="⎪",u="Size4-Regular"):"\\rgroup"===e||"⟯"===e?(o="⎫",m="⎭",l="⎪",u="Size4-Regular"):"\\lmoustache"===e||"⎰"===e?(o="⎧",m="⎭",l="⎪",u="Size4-Regular"):"\\rmoustache"!==e&&"⎱"!==e||(o="⎫",m="⎩",l="⎪",u="Size4-Regular");var d=or(o,u,n),g=d.height+d.depth,f=or(l,u,n),v=f.height+f.depth,b=or(m,u,n),y=b.height+b.depth,x=0,k=1;if(null!==s){var S=or(s,u,n);x=S.height+S.depth,k=2}var M=g+y+x,z=M+Math.max(0,Math.ceil((t-M)/(k*v)))*k*v,A=a.fontMetrics().axisHeight;r&&(A*=a.sizeMultiplier);var T=z/2-A,B=[];if(c.length>0){var C=z-g-y,N=Math.round(1e3*z),q=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v1759 h347 v-84\nH403z M403 1759 V0 H319 V1759 v"+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v1759 H0 v84 H347z\nM347 1759 V0 H263 V1759 v"+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z\nM367 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M410 15 H367 v585 v"+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+" v1715 h263 v84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+" v1799 H0 v-84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v602 h84z\nM403 1759 V0 H319 V1759 v"+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v602 h84z\nM347 1759 V0 h-84 V1759 v"+t+" v602 h84z";case"lparen":return"M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1\nc-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,\n-36,557 l0,"+(t+84)+"c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,\n949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9\nc0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,\n-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189\nl0,-"+(t+92)+"c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,\n-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z";case"rparen":return"M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,\n63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5\nc11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,"+(t+9)+"\nc-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664\nc-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11\nc0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17\nc242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558\nl0,-"+(t+144)+"c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,\n-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z";default:throw new Error("Unknown stretchy delimiter.")}}(c,Math.round(1e3*C)),I=new J(c,q),R=(p/1e3).toFixed(3)+"em",H=(N/1e3).toFixed(3)+"em",O=new K([I],{width:R,height:H,viewBox:"0 0 "+p+" "+N}),E=Ze.makeSvgSpan([],[O],a);E.height=N/1e3,E.style.width=R,E.style.height=H,B.push({type:"elem",elem:E})}else{if(B.push(mr(m,u,n)),B.push(pr),null===s){var L=z-g-y+.016;B.push(cr(l,L,a))}else{var D=(z-g-y-x)/2+.016;B.push(cr(l,D,a)),B.push(pr),B.push(mr(s,u,n)),B.push(pr),B.push(cr(l,D,a))}B.push(pr),B.push(mr(o,u,n))}var P=a.havingBaseStyle(w.TEXT),V=Ze.makeVList({positionType:"bottom",positionData:T,children:B},P);return sr(Ze.makeSpan(["delimsizing","mult"],[V],P),w.TEXT,a,i)},fr=.08,vr=function(e,t,r,a,n){var i=function(e,t,r){t*=1e3;var a="";switch(e){case"sqrtMain":a=function(e,t){return"M95,"+(622+e+t)+"\nc-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14\nc0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54\nc44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10\ns173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429\nc69,-144,104.5,-217.7,106.5,-221\nl"+e/2.075+" -"+e+"\nc5.3,-9.3,12,-14,20,-14\nH400000v"+(40+e)+"H845.2724\ns-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7\nc-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z\nM"+(834+e)+" "+t+"h400000v"+(40+e)+"h-400000z"}(t,z);break;case"sqrtSize1":a=function(e,t){return"M263,"+(601+e+t)+"c0.7,0,18,39.7,52,119\nc34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120\nc340,-704.7,510.7,-1060.3,512,-1067\nl"+e/2.084+" -"+e+"\nc4.7,-7.3,11,-11,19,-11\nH40000v"+(40+e)+"H1012.3\ns-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232\nc-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1\ns-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26\nc-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z\nM"+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"}(t,z);break;case"sqrtSize2":a=function(e,t){return"M983 "+(10+e+t)+"\nl"+e/3.13+" -"+e+"\nc4,-6.7,10,-10,18,-10 H400000v"+(40+e)+"\nH1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7\ns-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744\nc-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30\nc26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722\nc56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5\nc53.7,-170.3,84.5,-266.8,92.5,-289.5z\nM"+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"}(t,z);break;case"sqrtSize3":a=function(e,t){return"M424,"+(2398+e+t)+"\nc-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514\nc0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20\ns-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121\ns209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081\nl"+e/4.223+" -"+e+"c4,-6.7,10,-10,18,-10 H400000\nv"+(40+e)+"H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185\nc-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M"+(1001+e)+" "+t+"\nh400000v"+(40+e)+"h-400000z"}(t,z);break;case"sqrtSize4":a=function(e,t){return"M473,"+(2713+e+t)+"\nc339.3,-1799.3,509.3,-2700,510,-2702 l"+e/5.298+" -"+e+"\nc3.3,-7.3,9.3,-11,18,-11 H400000v"+(40+e)+"H1017.7\ns-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200\nc0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26\ns76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,\n606zM"+(1001+e)+" "+t+"h400000v"+(40+e)+"H1017.7z"}(t,z);break;case"sqrtTall":a=function(e,t,r){return"M702 "+(e+t)+"H400000"+(40+e)+"\nH742v"+(r-54-t-e)+"l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1\nh-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170\nc-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667\n219 661 l218 661zM702 "+t+"H400000v"+(40+e)+"H742z"}(t,z,r)}return a}(e,a,r),o=new J(e,i),s=new K([o],{width:"400em",height:F(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return Ze.makeSvgSpan(["hide-tail"],[s],n)},br=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],yr=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],xr=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],wr=[0,1.2,1.8,2.4,3],kr=[{type:"small",style:w.SCRIPTSCRIPT},{type:"small",style:w.SCRIPT},{type:"small",style:w.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],Sr=[{type:"small",style:w.SCRIPTSCRIPT},{type:"small",style:w.SCRIPT},{type:"small",style:w.TEXT},{type:"stack"}],Mr=[{type:"small",style:w.SCRIPTSCRIPT},{type:"small",style:w.SCRIPT},{type:"small",style:w.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],zr=function(e){if("small"===e.type)return"Main-Regular";if("large"===e.type)return"Size"+e.size+"-Regular";if("stack"===e.type)return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},Ar=function(e,t,r,a){for(var n=Math.min(2,3-a.style.size);n<r.length&&"stack"!==r[n].type;n++){var i=or(e,zr(r[n]),"math"),o=i.height+i.depth;if("small"===r[n].type&&(o*=a.havingBaseStyle(r[n].style).sizeMultiplier),o>t)return r[n]}return r[r.length-1]},Tr=function(e,t,r,a,n,i){var o;"<"===e||"\\lt"===e||"⟨"===e?e="\\langle":">"!==e&&"\\gt"!==e&&"⟩"!==e||(e="\\rangle"),o=h.contains(xr,e)?kr:h.contains(br,e)?Mr:Sr;var s=Ar(e,t,o,a);return"small"===s.type?function(e,t,r,a,n,i){var o=Ze.makeSymbol(e,"Main-Regular",n,a),s=sr(o,t,a,i);return r&&lr(s,a,t),s}(e,s.style,r,a,n,i):"large"===s.type?hr(e,s.size,r,a,n,i):gr(e,t,r,a,n,i)},Br={sqrtImage:function(e,t){var r,a,n=t.havingBaseSizing(),i=Ar("\\surd",e*n.sizeMultiplier,Mr,n),o=n.sizeMultiplier,s=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),l=0,h=0,m=0;return"small"===i.type?(e<1?o=1:e<1.4&&(o=.7),h=(1+s)/o,(r=vr("sqrtMain",l=(1+s+fr)/o,m=1e3+1e3*s+80,s,t)).style.minWidth="0.853em",a=.833/o):"large"===i.type?(m=1080*wr[i.size],h=(wr[i.size]+s)/o,l=(wr[i.size]+s+fr)/o,(r=vr("sqrtSize"+i.size,l,m,s,t)).style.minWidth="1.02em",a=1/o):(l=e+s+fr,h=e+s,m=Math.floor(1e3*e+s)+80,(r=vr("sqrtTall",l,m,s,t)).style.minWidth="0.742em",a=1.056),r.height=h,r.style.height=F(l),{span:r,advanceWidth:a,ruleWidth:(t.fontMetrics().sqrtRuleThickness+s)*o}},sizedDelim:function(e,t,r,a,i){if("<"===e||"\\lt"===e||"⟨"===e?e="\\langle":">"!==e&&"\\gt"!==e&&"⟩"!==e||(e="\\rangle"),h.contains(br,e)||h.contains(xr,e))return hr(e,t,!1,r,a,i);if(h.contains(yr,e))return gr(e,wr[t],!1,r,a,i);throw new n("Illegal delimiter: '"+e+"'")},sizeToMaxHeight:wr,customSizedDelim:Tr,leftRightDelim:function(e,t,r,a,n,i){var o=a.fontMetrics().axisHeight*a.sizeMultiplier,s=5/a.fontMetrics().ptPerEm,l=Math.max(t-o,r+o),h=Math.max(l/500*901,2*l-s);return Tr(e,h,!0,a,n,i)}},Cr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},Nr=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function qr(e,t){var r=Ut(e);if(r&&h.contains(Nr,r.text))return r;throw new n(r?"Invalid delimiter '"+r.text+"' after '"+t.funcName+"'":"Invalid delimiter type '"+e.type+"'",e)}function Ir(e){if(!e.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}it({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(e,t)=>{var r=qr(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:Cr[e.funcName].size,mclass:Cr[e.funcName].mclass,delim:r.text}},htmlBuilder:(e,t)=>"."===e.delim?Ze.makeSpan([e.mclass]):Br.sizedDelim(e.delim,e.size,t,e.mode,[e.mclass]),mathmlBuilder:e=>{var t=[];"."!==e.delim&&t.push(Tt(e.delim,e.mode));var r=new At.MathNode("mo",t);"mopen"===e.mclass||"mclose"===e.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");var a=F(Br.sizeToMaxHeight[e.size]);return r.setAttribute("minsize",a),r.setAttribute("maxsize",a),r}}),it({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=e.parser.gullet.macros.get("\\current@color");if(r&&"string"!=typeof r)throw new n("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:e.parser.mode,delim:qr(t[0],e).text,color:r}}}),it({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=qr(t[0],e),a=e.parser;++a.leftrightDepth;var n=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);var i=Ft(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:n,left:r.text,right:i.delim,rightColor:i.color}},htmlBuilder:(e,t)=>{Ir(e);for(var r,a,n=dt(e.body,t,!0,["mopen","mclose"]),i=0,o=0,s=!1,l=0;l<n.length;l++)n[l].isMiddle?s=!0:(i=Math.max(n[l].height,i),o=Math.max(n[l].depth,o));if(i*=t.sizeMultiplier,o*=t.sizeMultiplier,r="."===e.left?yt(t,["mopen"]):Br.leftRightDelim(e.left,i,o,t,e.mode,["mopen"]),n.unshift(r),s)for(var h=1;h<n.length;h++){var m=n[h].isMiddle;m&&(n[h]=Br.leftRightDelim(m.delim,i,o,m.options,e.mode,[]))}if("."===e.right)a=yt(t,["mclose"]);else{var c=e.rightColor?t.withColor(e.rightColor):t;a=Br.leftRightDelim(e.right,i,o,c,e.mode,["mclose"])}return n.push(a),Ze.makeSpan(["minner"],n,t)},mathmlBuilder:(e,t)=>{Ir(e);var r=Nt(e.body,t);if("."!==e.left){var a=new At.MathNode("mo",[Tt(e.left,e.mode)]);a.setAttribute("fence","true"),r.unshift(a)}if("."!==e.right){var n=new At.MathNode("mo",[Tt(e.right,e.mode)]);n.setAttribute("fence","true"),e.rightColor&&n.setAttribute("mathcolor",e.rightColor),r.push(n)}return Bt(r)}}),it({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=qr(t[0],e);if(!e.parser.leftrightDepth)throw new n("\\middle without preceding \\left",r);return{type:"middle",mode:e.parser.mode,delim:r.text}},htmlBuilder:(e,t)=>{var r;if("."===e.delim)r=yt(t,[]);else{r=Br.sizedDelim(e.delim,1,t,e.mode,[]);var a={delim:e.delim,options:t};r.isMiddle=a}return r},mathmlBuilder:(e,t)=>{var r="\\vert"===e.delim||"|"===e.delim?Tt("|","text"):Tt(e.delim,e.mode),a=new At.MathNode("mo",[r]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a}});var Rr=(e,t)=>{var r,a,n,i=Ze.wrapFragment(xt(e.body,t),t),o=e.label.slice(1),s=t.sizeMultiplier,l=0,m=h.isCharacterBox(e.body);if("sout"===o)(r=Ze.makeSpan(["stretchy","sout"])).height=t.fontMetrics().defaultRuleThickness/s,l=-.5*t.fontMetrics().xHeight;else if("phase"===o){var c=V({number:.6,unit:"pt"},t),p=V({number:.35,unit:"ex"},t);s/=t.havingBaseSizing().sizeMultiplier;var u=i.height+i.depth+c+p;i.style.paddingLeft=F(u/2+c);var d=Math.floor(1e3*u*s),g="M400000 "+(a=d)+" H0 L"+a/2+" 0 l65 45 L145 "+(a-80)+" H400000z",f=new K([new J("phase",g)],{width:"400em",height:F(d/1e3),viewBox:"0 0 400000 "+d,preserveAspectRatio:"xMinYMin slice"});(r=Ze.makeSvgSpan(["hide-tail"],[f],t)).style.height=F(u),l=i.depth+c+p}else{/cancel/.test(o)?m||i.classes.push("cancel-pad"):"angl"===o?i.classes.push("anglpad"):i.classes.push("boxpad");var v=0,b=0,y=0;/box/.test(o)?(y=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness),b=v=t.fontMetrics().fboxsep+("colorbox"===o?0:y)):"angl"===o?(v=4*(y=Math.max(t.fontMetrics().defaultRuleThickness,t.minRuleThickness)),b=Math.max(0,.25-i.depth)):b=v=m?.2:0,r=Dt(i,o,v,b,t),/fbox|boxed|fcolorbox/.test(o)?(r.style.borderStyle="solid",r.style.borderWidth=F(y)):"angl"===o&&.049!==y&&(r.style.borderTopWidth=F(y),r.style.borderRightWidth=F(y)),l=i.depth+b,e.backgroundColor&&(r.style.backgroundColor=e.backgroundColor,e.borderColor&&(r.style.borderColor=e.borderColor))}if(e.backgroundColor)n=Ze.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:l},{type:"elem",elem:i,shift:0}]},t);else{var x=/cancel|phase/.test(o)?["svg-align"]:[];n=Ze.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:0},{type:"elem",elem:r,shift:l,wrapperClasses:x}]},t)}return/cancel/.test(o)&&(n.height=i.height,n.depth=i.depth),/cancel/.test(o)&&!m?Ze.makeSpan(["mord","cancel-lap"],[n],t):Ze.makeSpan(["mord"],[n],t)},Hr=(e,t)=>{var r=0,a=new At.MathNode(e.label.indexOf("colorbox")>-1?"mpadded":"menclose",[It(e.body,t)]);switch(e.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=t.fontMetrics().fboxsep*t.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*r+"pt"),a.setAttribute("height","+"+2*r+"pt"),a.setAttribute("lspace",r+"pt"),a.setAttribute("voffset",r+"pt"),"\\fcolorbox"===e.label){var n=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness);a.setAttribute("style","border: "+n+"em solid "+String(e.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return e.backgroundColor&&a.setAttribute("mathbackground",e.backgroundColor),a};it({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(e,t,r){var{parser:a,funcName:n}=e,i=Ft(t[0],"color-token").color,o=t[1];return{type:"enclose",mode:a.mode,label:n,backgroundColor:i,body:o}},htmlBuilder:Rr,mathmlBuilder:Hr}),it({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(e,t,r){var{parser:a,funcName:n}=e,i=Ft(t[0],"color-token").color,o=Ft(t[1],"color-token").color,s=t[2];return{type:"enclose",mode:a.mode,label:n,backgroundColor:o,borderColor:i,body:s}},htmlBuilder:Rr,mathmlBuilder:Hr}),it({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\fbox",body:t[0]}}}),it({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"enclose",mode:r.mode,label:a,body:n}},htmlBuilder:Rr,mathmlBuilder:Hr}),it({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\angl",body:t[0]}}});var Or={};function Er(e){for(var{type:t,names:r,props:a,handler:n,htmlBuilder:i,mathmlBuilder:o}=e,s={type:t,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n},l=0;l<r.length;++l)Or[r[l]]=s;i&&(at[t]=i),o&&(nt[t]=o)}var Lr={};function Dr(e,t){Lr[e]=t}function Pr(e){var t=[];e.consumeSpaces();for(var r=e.fetch().text;"\\hline"===r||"\\hdashline"===r;)e.consume(),t.push("\\hdashline"===r),e.consumeSpaces(),r=e.fetch().text;return t}var Vr=e=>{if(!e.parser.settings.displayMode)throw new n("{"+e.envName+"} can be used only in display mode.")};function Fr(e){if(-1===e.indexOf("ed"))return-1===e.indexOf("*")}function Gr(e,t,r){var{hskipBeforeAndAfter:i,addJot:o,cols:s,arraystretch:l,colSeparationType:h,autoTag:m,singleRow:c,emptySingleRow:p,maxNumCols:u,leqno:d}=t;if(e.gullet.beginGroup(),c||e.gullet.macros.set("\\cr","\\\\\\relax"),!l){var g=e.gullet.expandMacroAsText("\\arraystretch");if(null==g)l=1;else if(!(l=parseFloat(g))||l<0)throw new n("Invalid \\arraystretch: "+g)}e.gullet.beginGroup();var f=[],v=[f],b=[],y=[],x=null!=m?[]:void 0;function w(){m&&e.gullet.macros.set("\\@eqnsw","1",!0)}function k(){x&&(e.gullet.macros.get("\\df@tag")?(x.push(e.subparse([new a("\\df@tag")])),e.gullet.macros.set("\\df@tag",void 0,!0)):x.push(Boolean(m)&&"1"===e.gullet.macros.get("\\@eqnsw")))}for(w(),y.push(Pr(e));;){var S=e.parseExpression(!1,c?"\\end":"\\\\");e.gullet.endGroup(),e.gullet.beginGroup(),S={type:"ordgroup",mode:e.mode,body:S},r&&(S={type:"styling",mode:e.mode,style:r,body:[S]}),f.push(S);var M=e.fetch().text;if("&"===M){if(u&&f.length===u){if(c||h)throw new n("Too many tab characters: &",e.nextToken);e.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}e.consume()}else{if("\\end"===M){k(),1===f.length&&"styling"===S.type&&0===S.body[0].body.length&&(v.length>1||!p)&&v.pop(),y.length<v.length+1&&y.push([]);break}if("\\\\"!==M)throw new n("Expected & or \\\\ or \\cr or \\end",e.nextToken);e.consume();var z=void 0;" "!==e.gullet.future().text&&(z=e.parseSizeGroup(!0)),b.push(z?z.value:null),k(),y.push(Pr(e)),f=[],v.push(f),w()}}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:e.mode,addJot:o,arraystretch:l,body:v,cols:s,rowGaps:b,hskipBeforeAndAfter:i,hLinesBeforeRow:y,colSeparationType:h,tags:x,leqno:d}}function Ur(e){return"d"===e.slice(0,1)?"display":"text"}var Yr=function(e,t){var r,a,i=e.body.length,o=e.hLinesBeforeRow,s=0,l=new Array(i),m=[],c=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),p=1/t.fontMetrics().ptPerEm,u=5*p;e.colSeparationType&&"small"===e.colSeparationType&&(u=t.havingStyle(w.SCRIPT).sizeMultiplier/t.sizeMultiplier*.2778);var d="CD"===e.colSeparationType?V({number:3,unit:"ex"},t):12*p,g=3*p,f=e.arraystretch*d,v=.7*f,b=.3*f,y=0;function x(e){for(var t=0;t<e.length;++t)t>0&&(y+=.25),m.push({pos:y,isDashed:e[t]})}for(x(o[0]),r=0;r<e.body.length;++r){var k=e.body[r],S=v,M=b;s<k.length&&(s=k.length);var z=new Array(k.length);for(a=0;a<k.length;++a){var A=xt(k[a],t);M<A.depth&&(M=A.depth),S<A.height&&(S=A.height),z[a]=A}var T=e.rowGaps[r],B=0;T&&(B=V(T,t))>0&&(M<(B+=b)&&(M=B),B=0),e.addJot&&(M+=g),z.height=S,z.depth=M,y+=S,z.pos=y,y+=M+B,l[r]=z,x(o[r+1])}var C,N,q=y/2+t.fontMetrics().axisHeight,I=e.cols||[],R=[],H=[];if(e.tags&&e.tags.some((e=>e)))for(r=0;r<i;++r){var O=l[r],E=O.pos-q,L=e.tags[r],D=void 0;(D=!0===L?Ze.makeSpan(["eqn-num"],[],t):!1===L?Ze.makeSpan([],[],t):Ze.makeSpan([],dt(L,t,!0),t)).depth=O.depth,D.height=O.height,H.push({type:"elem",elem:D,shift:E})}for(a=0,N=0;a<s||N<I.length;++a,++N){for(var P=I[N]||{},G=!0;"separator"===P.type;){if(G||((C=Ze.makeSpan(["arraycolsep"],[])).style.width=F(t.fontMetrics().doubleRuleSep),R.push(C)),"|"!==P.separator&&":"!==P.separator)throw new n("Invalid separator type: "+P.separator);var U="|"===P.separator?"solid":"dashed",Y=Ze.makeSpan(["vertical-separator"],[],t);Y.style.height=F(y),Y.style.borderRightWidth=F(c),Y.style.borderRightStyle=U,Y.style.margin="0 "+F(-c/2);var X=y-q;X&&(Y.style.verticalAlign=F(-X)),R.push(Y),P=I[++N]||{},G=!1}if(!(a>=s)){var W=void 0;(a>0||e.hskipBeforeAndAfter)&&0!==(W=h.deflt(P.pregap,u))&&((C=Ze.makeSpan(["arraycolsep"],[])).style.width=F(W),R.push(C));var _=[];for(r=0;r<i;++r){var j=l[r],$=j[a];if($){var Z=j.pos-q;$.depth=j.depth,$.height=j.height,_.push({type:"elem",elem:$,shift:Z})}}_=Ze.makeVList({positionType:"individualShift",children:_},t),_=Ze.makeSpan(["col-align-"+(P.align||"c")],[_]),R.push(_),(a<s-1||e.hskipBeforeAndAfter)&&0!==(W=h.deflt(P.postgap,u))&&((C=Ze.makeSpan(["arraycolsep"],[])).style.width=F(W),R.push(C))}}if(l=Ze.makeSpan(["mtable"],R),m.length>0){for(var K=Ze.makeLineSpan("hline",t,c),J=Ze.makeLineSpan("hdashline",t,c),Q=[{type:"elem",elem:l,shift:0}];m.length>0;){var ee=m.pop(),te=ee.pos-q;ee.isDashed?Q.push({type:"elem",elem:J,shift:te}):Q.push({type:"elem",elem:K,shift:te})}l=Ze.makeVList({positionType:"individualShift",children:Q},t)}if(0===H.length)return Ze.makeSpan(["mord"],[l],t);var re=Ze.makeVList({positionType:"individualShift",children:H},t);return re=Ze.makeSpan(["tag"],[re],t),Ze.makeFragment([l,re])},Xr={c:"center ",l:"left ",r:"right "},Wr=function(e,t){for(var r=[],a=new At.MathNode("mtd",[],["mtr-glue"]),n=new At.MathNode("mtd",[],["mml-eqn-num"]),i=0;i<e.body.length;i++){for(var o=e.body[i],s=[],l=0;l<o.length;l++)s.push(new At.MathNode("mtd",[It(o[l],t)]));e.tags&&e.tags[i]&&(s.unshift(a),s.push(a),e.leqno?s.unshift(n):s.push(n)),r.push(new At.MathNode("mtr",s))}var h=new At.MathNode("mtable",r),m=.5===e.arraystretch?.1:.16+e.arraystretch-1+(e.addJot?.09:0);h.setAttribute("rowspacing",F(m));var c="",p="";if(e.cols&&e.cols.length>0){var u=e.cols,d="",g=!1,f=0,v=u.length;"separator"===u[0].type&&(c+="top ",f=1),"separator"===u[u.length-1].type&&(c+="bottom ",v-=1);for(var b=f;b<v;b++)"align"===u[b].type?(p+=Xr[u[b].align],g&&(d+="none "),g=!0):"separator"===u[b].type&&g&&(d+="|"===u[b].separator?"solid ":"dashed ",g=!1);h.setAttribute("columnalign",p.trim()),/[sd]/.test(d)&&h.setAttribute("columnlines",d.trim())}if("align"===e.colSeparationType){for(var y=e.cols||[],x="",w=1;w<y.length;w++)x+=w%2?"0em ":"1em ";h.setAttribute("columnspacing",x.trim())}else"alignat"===e.colSeparationType||"gather"===e.colSeparationType?h.setAttribute("columnspacing","0em"):"small"===e.colSeparationType?h.setAttribute("columnspacing","0.2778em"):"CD"===e.colSeparationType?h.setAttribute("columnspacing","0.5em"):h.setAttribute("columnspacing","1em");var k="",S=e.hLinesBeforeRow;c+=S[0].length>0?"left ":"",c+=S[S.length-1].length>0?"right ":"";for(var M=1;M<S.length-1;M++)k+=0===S[M].length?"none ":S[M][0]?"dashed ":"solid ";return/[sd]/.test(k)&&h.setAttribute("rowlines",k.trim()),""!==c&&(h=new At.MathNode("menclose",[h])).setAttribute("notation",c.trim()),e.arraystretch&&e.arraystretch<1&&(h=new At.MathNode("mstyle",[h])).setAttribute("scriptlevel","1"),h},_r=function(e,t){-1===e.envName.indexOf("ed")&&Vr(e);var r,a=[],i=e.envName.indexOf("at")>-1?"alignat":"align",o="split"===e.envName,s=Gr(e.parser,{cols:a,addJot:!0,autoTag:o?void 0:Fr(e.envName),emptySingleRow:!0,colSeparationType:i,maxNumCols:o?2:void 0,leqno:e.parser.settings.leqno},"display"),l=0,h={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&"ordgroup"===t[0].type){for(var m="",c=0;c<t[0].body.length;c++){m+=Ft(t[0].body[c],"textord").text}r=Number(m),l=2*r}var p=!l;s.body.forEach((function(e){for(var t=1;t<e.length;t+=2){var a=Ft(e[t],"styling");Ft(a.body[0],"ordgroup").body.unshift(h)}if(p)l<e.length&&(l=e.length);else{var i=e.length/2;if(r<i)throw new n("Too many math in a row: expected "+r+", but got "+i,e[0])}}));for(var u=0;u<l;++u){var d="r",g=0;u%2==1?d="l":u>0&&p&&(g=1),a[u]={type:"align",align:d,pregap:g,postgap:0}}return s.colSeparationType=p?"align":"alignat",s};Er({type:"array",names:["array","darray"],props:{numArgs:1},handler(e,t){var r=(Ut(t[0])?[t[0]]:Ft(t[0],"ordgroup").body).map((function(e){var t=Gt(e).text;if(-1!=="lcr".indexOf(t))return{type:"align",align:t};if("|"===t)return{type:"separator",separator:"|"};if(":"===t)return{type:"separator",separator:":"};throw new n("Unknown column alignment: "+t,e)})),a={cols:r,hskipBeforeAndAfter:!0,maxNumCols:r.length};return Gr(e.parser,a,Ur(e.envName))},htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(e){var t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName.replace("*","")],r="c",a={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if("*"===e.envName.charAt(e.envName.length-1)){var i=e.parser;if(i.consumeSpaces(),"["===i.fetch().text){if(i.consume(),i.consumeSpaces(),r=i.fetch().text,-1==="lcr".indexOf(r))throw new n("Expected l or c or r",i.nextToken);i.consume(),i.consumeSpaces(),i.expect("]"),i.consume(),a.cols=[{type:"align",align:r}]}}var o=Gr(e.parser,a,Ur(e.envName)),s=Math.max(0,...o.body.map((e=>e.length)));return o.cols=new Array(s).fill({type:"align",align:r}),t?{type:"leftright",mode:e.mode,body:[o],left:t[0],right:t[1],rightColor:void 0}:o},htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(e){var t=Gr(e.parser,{arraystretch:.5},"script");return t.colSeparationType="small",t},htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["subarray"],props:{numArgs:1},handler(e,t){var r=(Ut(t[0])?[t[0]]:Ft(t[0],"ordgroup").body).map((function(e){var t=Gt(e).text;if(-1!=="lc".indexOf(t))return{type:"align",align:t};throw new n("Unknown column alignment: "+t,e)}));if(r.length>1)throw new n("{subarray} can contain only one column");var a={cols:r,hskipBeforeAndAfter:!1,arraystretch:.5};if((a=Gr(e.parser,a,"script")).body.length>0&&a.body[0].length>1)throw new n("{subarray} can contain only one column");return a},htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(e){var t=Gr(e.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},Ur(e.envName));return{type:"leftright",mode:e.mode,body:[t],left:e.envName.indexOf("r")>-1?".":"\\{",right:e.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:_r,htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(e){h.contains(["gather","gather*"],e.envName)&&Vr(e);var t={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:Fr(e.envName),emptySingleRow:!0,leqno:e.parser.settings.leqno};return Gr(e.parser,t,"display")},htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:_r,htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(e){Vr(e);var t={autoTag:Fr(e.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:e.parser.settings.leqno};return Gr(e.parser,t,"display")},htmlBuilder:Yr,mathmlBuilder:Wr}),Er({type:"array",names:["CD"],props:{numArgs:0},handler(e){return Vr(e),function(e){var t=[];for(e.gullet.beginGroup(),e.gullet.macros.set("\\cr","\\\\\\relax"),e.gullet.beginGroup();;){t.push(e.parseExpression(!1,"\\\\")),e.gullet.endGroup(),e.gullet.beginGroup();var r=e.fetch().text;if("&"!==r&&"\\\\"!==r){if("\\end"===r){0===t[t.length-1].length&&t.pop();break}throw new n("Expected \\\\ or \\cr or \\end",e.nextToken)}e.consume()}for(var a,i,o=[],s=[o],l=0;l<t.length;l++){for(var h=t[l],m={type:"styling",body:[],mode:"math",style:"display"},c=0;c<h.length;c++)if(Qt(h[c])){o.push(m);var p=Gt(h[c+=1]).text,u=new Array(2);if(u[0]={type:"ordgroup",mode:"math",body:[]},u[1]={type:"ordgroup",mode:"math",body:[]},"=|.".indexOf(p)>-1);else{if(!("<>AV".indexOf(p)>-1))throw new n('Expected one of "<>AV=|." after @',h[c]);for(var d=0;d<2;d++){for(var g=!0,f=c+1;f<h.length;f++){if(i=p,("mathord"===(a=h[f]).type||"atom"===a.type)&&a.text===i){g=!1,c=f;break}if(Qt(h[f]))throw new n("Missing a "+p+" character to complete a CD arrow.",h[f]);u[d].body.push(h[f])}if(g)throw new n("Missing a "+p+" character to complete a CD arrow.",h[c])}}var v={type:"styling",body:[er(p,u,e)],mode:"math",style:"display"};o.push(v),m={type:"styling",body:[],mode:"math",style:"display"}}else m.body.push(h[c]);l%2==0?o.push(m):o.shift(),o=[],s.push(o)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:"math",body:s,arraystretch:1,addJot:!0,rowGaps:[null],cols:new Array(s[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25}),colSeparationType:"CD",hLinesBeforeRow:new Array(s.length+1).fill([])}}(e.parser)},htmlBuilder:Yr,mathmlBuilder:Wr}),Dr("\\nonumber","\\gdef\\@eqnsw{0}"),Dr("\\notag","\\nonumber"),it({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(e,t){throw new n(e.funcName+" valid only within array environment")}});var jr=Or;it({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(e,t){var{parser:r,funcName:a}=e,i=t[0];if("ordgroup"!==i.type)throw new n("Invalid environment name",i);for(var o="",s=0;s<i.body.length;++s)o+=Ft(i.body[s],"textord").text;if("\\begin"===a){if(!jr.hasOwnProperty(o))throw new n("No such environment: "+o,i);var l=jr[o],{args:h,optArgs:m}=r.parseArguments("\\begin{"+o+"}",l),c={mode:r.mode,envName:o,parser:r},p=l.handler(c,h,m);r.expect("\\end",!1);var u=r.nextToken,d=Ft(r.parseFunction(),"environment");if(d.name!==o)throw new n("Mismatch: \\begin{"+o+"} matched by \\end{"+d.name+"}",u);return p}return{type:"environment",mode:r.mode,name:o,nameGroup:i}}});var $r=(e,t)=>{var r=e.font,a=t.withFont(r);return xt(e.body,a)},Zr=(e,t)=>{var r=e.font,a=t.withFont(r);return It(e.body,a)},Kr={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};it({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=st(t[0]),i=a;return i in Kr&&(i=Kr[i]),{type:"font",mode:r.mode,font:i.slice(1),body:n}},htmlBuilder:$r,mathmlBuilder:Zr}),it({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(e,t)=>{var{parser:r}=e,a=t[0],n=h.isCharacterBox(a);return{type:"mclass",mode:r.mode,mclass:Kt(a),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:a}],isCharacterBox:n}}}),it({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:a,breakOnTokenText:n}=e,{mode:i}=r,o=r.parseExpression(!0,n);return{type:"font",mode:i,font:"math"+a.slice(1),body:{type:"ordgroup",mode:r.mode,body:o}}},htmlBuilder:$r,mathmlBuilder:Zr});var Jr=(e,t)=>{var r=t;return"display"===e?r=r.id>=w.SCRIPT.id?r.text():w.DISPLAY:"text"===e&&r.size===w.DISPLAY.size?r=w.TEXT:"script"===e?r=w.SCRIPT:"scriptscript"===e&&(r=w.SCRIPTSCRIPT),r},Qr=(e,t)=>{var r,a=Jr(e.size,t.style),n=a.fracNum(),i=a.fracDen();r=t.havingStyle(n);var o=xt(e.numer,r,t);if(e.continued){var s=8.5/t.fontMetrics().ptPerEm,l=3.5/t.fontMetrics().ptPerEm;o.height=o.height<s?s:o.height,o.depth=o.depth<l?l:o.depth}r=t.havingStyle(i);var h,m,c,p,u,d,g,f,v,b,y=xt(e.denom,r,t);if(e.hasBarLine?(e.barSize?(m=V(e.barSize,t),h=Ze.makeLineSpan("frac-line",t,m)):h=Ze.makeLineSpan("frac-line",t),m=h.height,c=h.height):(h=null,m=0,c=t.fontMetrics().defaultRuleThickness),a.size===w.DISPLAY.size||"display"===e.size?(p=t.fontMetrics().num1,u=m>0?3*c:7*c,d=t.fontMetrics().denom1):(m>0?(p=t.fontMetrics().num2,u=c):(p=t.fontMetrics().num3,u=3*c),d=t.fontMetrics().denom2),h){var x=t.fontMetrics().axisHeight;p-o.depth-(x+.5*m)<u&&(p+=u-(p-o.depth-(x+.5*m))),x-.5*m-(y.height-d)<u&&(d+=u-(x-.5*m-(y.height-d)));var k=-(x-.5*m);g=Ze.makeVList({positionType:"individualShift",children:[{type:"elem",elem:y,shift:d},{type:"elem",elem:h,shift:k},{type:"elem",elem:o,shift:-p}]},t)}else{var S=p-o.depth-(y.height-d);S<u&&(p+=.5*(u-S),d+=.5*(u-S)),g=Ze.makeVList({positionType:"individualShift",children:[{type:"elem",elem:y,shift:d},{type:"elem",elem:o,shift:-p}]},t)}return r=t.havingStyle(a),g.height*=r.sizeMultiplier/t.sizeMultiplier,g.depth*=r.sizeMultiplier/t.sizeMultiplier,f=a.size===w.DISPLAY.size?t.fontMetrics().delim1:a.size===w.SCRIPTSCRIPT.size?t.havingStyle(w.SCRIPT).fontMetrics().delim2:t.fontMetrics().delim2,v=null==e.leftDelim?yt(t,["mopen"]):Br.customSizedDelim(e.leftDelim,f,!0,t.havingStyle(a),e.mode,["mopen"]),b=e.continued?Ze.makeSpan([]):null==e.rightDelim?yt(t,["mclose"]):Br.customSizedDelim(e.rightDelim,f,!0,t.havingStyle(a),e.mode,["mclose"]),Ze.makeSpan(["mord"].concat(r.sizingClasses(t)),[v,Ze.makeSpan(["mfrac"],[g]),b],t)},ea=(e,t)=>{var r=new At.MathNode("mfrac",[It(e.numer,t),It(e.denom,t)]);if(e.hasBarLine){if(e.barSize){var a=V(e.barSize,t);r.setAttribute("linethickness",F(a))}}else r.setAttribute("linethickness","0px");var n=Jr(e.size,t.style);if(n.size!==t.style.size){r=new At.MathNode("mstyle",[r]);var i=n.size===w.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",i),r.setAttribute("scriptlevel","0")}if(null!=e.leftDelim||null!=e.rightDelim){var o=[];if(null!=e.leftDelim){var s=new At.MathNode("mo",[new At.TextNode(e.leftDelim.replace("\\",""))]);s.setAttribute("fence","true"),o.push(s)}if(o.push(r),null!=e.rightDelim){var l=new At.MathNode("mo",[new At.TextNode(e.rightDelim.replace("\\",""))]);l.setAttribute("fence","true"),o.push(l)}return Bt(o)}return r};it({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(e,t)=>{var r,{parser:a,funcName:n}=e,i=t[0],o=t[1],s=null,l=null,h="auto";switch(n){case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,s="(",l=")";break;case"\\\\bracefrac":r=!1,s="\\{",l="\\}";break;case"\\\\brackfrac":r=!1,s="[",l="]";break;default:throw new Error("Unrecognized genfrac command")}switch(n){case"\\dfrac":case"\\dbinom":h="display";break;case"\\tfrac":case"\\tbinom":h="text"}return{type:"genfrac",mode:a.mode,continued:!1,numer:i,denom:o,hasBarLine:r,leftDelim:s,rightDelim:l,size:h,barSize:null}},htmlBuilder:Qr,mathmlBuilder:ea}),it({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0],i=t[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:n,denom:i,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),it({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(e){var t,{parser:r,funcName:a,token:n}=e;switch(a){case"\\over":t="\\frac";break;case"\\choose":t="\\binom";break;case"\\atop":t="\\\\atopfrac";break;case"\\brace":t="\\\\bracefrac";break;case"\\brack":t="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:t,token:n}}});var ta=["display","text","script","scriptscript"],ra=function(e){var t=null;return e.length>0&&(t="."===(t=e)?null:t),t};it({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(e,t){var r,{parser:a}=e,n=t[4],i=t[5],o=st(t[0]),s="atom"===o.type&&"open"===o.family?ra(o.text):null,l=st(t[1]),h="atom"===l.type&&"close"===l.family?ra(l.text):null,m=Ft(t[2],"size"),c=null;r=!!m.isBlank||(c=m.value).number>0;var p="auto",u=t[3];if("ordgroup"===u.type){if(u.body.length>0){var d=Ft(u.body[0],"textord");p=ta[Number(d.text)]}}else u=Ft(u,"textord"),p=ta[Number(u.text)];return{type:"genfrac",mode:a.mode,numer:n,denom:i,continued:!1,hasBarLine:r,barSize:c,leftDelim:s,rightDelim:h,size:p}},htmlBuilder:Qr,mathmlBuilder:ea}),it({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(e,t){var{parser:r,funcName:a,token:n}=e;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:Ft(t[0],"size").value,token:n}}}),it({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0],i=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e}(Ft(t[1],"infix").size),o=t[2],s=i.number>0;return{type:"genfrac",mode:r.mode,numer:n,denom:o,continued:!1,hasBarLine:s,barSize:i,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:Qr,mathmlBuilder:ea});var aa=(e,t)=>{var r,a,n=t.style;"supsub"===e.type?(r=e.sup?xt(e.sup,t.havingStyle(n.sup()),t):xt(e.sub,t.havingStyle(n.sub()),t),a=Ft(e.base,"horizBrace")):a=Ft(e,"horizBrace");var i,o=xt(a.base,t.havingBaseStyle(w.DISPLAY)),s=Vt(a,t);if(a.isOver?(i=Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:s}]},t)).children[0].children[0].children[1].classes.push("svg-align"):(i=Ze.makeVList({positionType:"bottom",positionData:o.depth+.1+s.height,children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:o}]},t)).children[0].children[0].children[0].classes.push("svg-align"),r){var l=Ze.makeSpan(["mord",a.isOver?"mover":"munder"],[i],t);i=a.isOver?Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:.2},{type:"elem",elem:r}]},t):Ze.makeVList({positionType:"bottom",positionData:l.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:l}]},t)}return Ze.makeSpan(["mord",a.isOver?"mover":"munder"],[i],t)};it({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e;return{type:"horizBrace",mode:r.mode,label:a,isOver:/^\\over/.test(a),base:t[0]}},htmlBuilder:aa,mathmlBuilder:(e,t)=>{var r=Pt(e.label);return new At.MathNode(e.isOver?"mover":"munder",[It(e.base,t),r])}}),it({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[1],n=Ft(t[0],"url").url;return r.settings.isTrusted({command:"\\href",url:n})?{type:"href",mode:r.mode,href:n,body:lt(a)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(e,t)=>{var r=dt(e.body,t,!1);return Ze.makeAnchor(e.href,[],r,t)},mathmlBuilder:(e,t)=>{var r=qt(e.body,t);return r instanceof Mt||(r=new Mt("mrow",[r])),r.setAttribute("href",e.href),r}}),it({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=Ft(t[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:a}))return r.formatUnsupportedCmd("\\url");for(var n=[],i=0;i<a.length;i++){var o=a[i];"~"===o&&(o="\\textasciitilde"),n.push({type:"textord",mode:"text",text:o})}var s={type:"text",mode:r.mode,font:"\\texttt",body:n};return{type:"href",mode:r.mode,href:a,body:lt(s)}}}),it({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(e,t){var{parser:r}=e;return{type:"hbox",mode:r.mode,body:lt(t[0])}},htmlBuilder(e,t){var r=dt(e.body,t,!1);return Ze.makeFragment(r)},mathmlBuilder(e,t){return new At.MathNode("mrow",Nt(e.body,t))}}),it({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(e,t)=>{var r,{parser:a,funcName:i,token:o}=e,s=Ft(t[0],"raw").string,l=t[1];a.settings.strict&&a.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var h={};switch(i){case"\\htmlClass":h.class=s,r={command:"\\htmlClass",class:s};break;case"\\htmlId":h.id=s,r={command:"\\htmlId",id:s};break;case"\\htmlStyle":h.style=s,r={command:"\\htmlStyle",style:s};break;case"\\htmlData":for(var m=s.split(","),c=0;c<m.length;c++){var p=m[c].split("=");if(2!==p.length)throw new n("Error parsing key-value for \\htmlData");h["data-"+p[0].trim()]=p[1].trim()}r={command:"\\htmlData",attributes:h};break;default:throw new Error("Unrecognized html command")}return a.settings.isTrusted(r)?{type:"html",mode:a.mode,attributes:h,body:lt(l)}:a.formatUnsupportedCmd(i)},htmlBuilder:(e,t)=>{var r=dt(e.body,t,!1),a=["enclosing"];e.attributes.class&&a.push(...e.attributes.class.trim().split(/\s+/));var n=Ze.makeSpan(a,r,t);for(var i in e.attributes)"class"!==i&&e.attributes.hasOwnProperty(i)&&n.setAttribute(i,e.attributes[i]);return n},mathmlBuilder:(e,t)=>qt(e.body,t)}),it({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"htmlmathml",mode:r.mode,html:lt(t[0]),mathml:lt(t[1])}},htmlBuilder:(e,t)=>{var r=dt(e.html,t,!1);return Ze.makeFragment(r)},mathmlBuilder:(e,t)=>qt(e.mathml,t)});var na=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new n("Invalid size: '"+e+"' in \\includegraphics");var r={number:+(t[1]+t[2]),unit:t[3]};if(!P(r))throw new n("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r};it({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(e,t,r)=>{var{parser:a}=e,i={number:0,unit:"em"},o={number:.9,unit:"em"},s={number:0,unit:"em"},l="";if(r[0])for(var h=Ft(r[0],"raw").string.split(","),m=0;m<h.length;m++){var c=h[m].split("=");if(2===c.length){var p=c[1].trim();switch(c[0].trim()){case"alt":l=p;break;case"width":i=na(p);break;case"height":o=na(p);break;case"totalheight":s=na(p);break;default:throw new n("Invalid key: '"+c[0]+"' in \\includegraphics.")}}}var u=Ft(t[0],"url").url;return""===l&&(l=(l=(l=u).replace(/^.*[\\/]/,"")).substring(0,l.lastIndexOf("."))),a.settings.isTrusted({command:"\\includegraphics",url:u})?{type:"includegraphics",mode:a.mode,alt:l,width:i,height:o,totalheight:s,src:u}:a.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(e,t)=>{var r=V(e.height,t),a=0;e.totalheight.number>0&&(a=V(e.totalheight,t)-r);var n=0;e.width.number>0&&(n=V(e.width,t));var i={height:F(r+a)};n>0&&(i.width=F(n)),a>0&&(i.verticalAlign=F(-a));var o=new j(e.src,e.alt,i);return o.height=r,o.depth=a,o},mathmlBuilder:(e,t)=>{var r=new At.MathNode("mglyph",[]);r.setAttribute("alt",e.alt);var a=V(e.height,t),n=0;if(e.totalheight.number>0&&(n=V(e.totalheight,t)-a,r.setAttribute("valign",F(-n))),r.setAttribute("height",F(a+n)),e.width.number>0){var i=V(e.width,t);r.setAttribute("width",F(i))}return r.setAttribute("src",e.src),r}}),it({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:a}=e,n=Ft(t[0],"size");if(r.settings.strict){var i="m"===a[1],o="mu"===n.value.unit;i?(o||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, not "+n.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):o&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:n.value}},htmlBuilder(e,t){return Ze.makeGlue(e.dimension,t)},mathmlBuilder(e,t){var r=V(e.dimension,t);return new At.SpaceNode(r)}}),it({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"lap",mode:r.mode,alignment:a.slice(5),body:n}},htmlBuilder:(e,t)=>{var r;"clap"===e.alignment?(r=Ze.makeSpan([],[xt(e.body,t)]),r=Ze.makeSpan(["inner"],[r],t)):r=Ze.makeSpan(["inner"],[xt(e.body,t)]);var a=Ze.makeSpan(["fix"],[]),n=Ze.makeSpan([e.alignment],[r,a],t),i=Ze.makeSpan(["strut"]);return i.style.height=F(n.height+n.depth),n.depth&&(i.style.verticalAlign=F(-n.depth)),n.children.unshift(i),n=Ze.makeSpan(["thinbox"],[n],t),Ze.makeSpan(["mord","vbox"],[n],t)},mathmlBuilder:(e,t)=>{var r=new At.MathNode("mpadded",[It(e.body,t)]);if("rlap"!==e.alignment){var a="llap"===e.alignment?"-1":"-0.5";r.setAttribute("lspace",a+"width")}return r.setAttribute("width","0px"),r}}),it({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){var{funcName:r,parser:a}=e,n=a.mode;a.switchMode("math");var i="\\("===r?"\\)":"$",o=a.parseExpression(!1,i);return a.expect(i),a.switchMode(n),{type:"styling",mode:a.mode,style:"text",body:o}}}),it({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){throw new n("Mismatched "+e.funcName)}});var ia=(e,t)=>{switch(t.style.size){case w.DISPLAY.size:return e.display;case w.TEXT.size:return e.text;case w.SCRIPT.size:return e.script;case w.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};it({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"mathchoice",mode:r.mode,display:lt(t[0]),text:lt(t[1]),script:lt(t[2]),scriptscript:lt(t[3])}},htmlBuilder:(e,t)=>{var r=ia(e,t),a=dt(r,t,!1);return Ze.makeFragment(a)},mathmlBuilder:(e,t)=>{var r=ia(e,t);return qt(r,t)}});var oa=(e,t,r,a,n,i,o)=>{e=Ze.makeSpan([],[e]);var s,l,m,c=r&&h.isCharacterBox(r);if(t){var p=xt(t,a.havingStyle(n.sup()),a);l={elem:p,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-p.depth)}}if(r){var u=xt(r,a.havingStyle(n.sub()),a);s={elem:u,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-u.height)}}if(l&&s){var d=a.fontMetrics().bigOpSpacing5+s.elem.height+s.elem.depth+s.kern+e.depth+o;m=Ze.makeVList({positionType:"bottom",positionData:d,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:s.elem,marginLeft:F(-i)},{type:"kern",size:s.kern},{type:"elem",elem:e},{type:"kern",size:l.kern},{type:"elem",elem:l.elem,marginLeft:F(i)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(s){var g=e.height-o;m=Ze.makeVList({positionType:"top",positionData:g,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:s.elem,marginLeft:F(-i)},{type:"kern",size:s.kern},{type:"elem",elem:e}]},a)}else{if(!l)return e;var f=e.depth+o;m=Ze.makeVList({positionType:"bottom",positionData:f,children:[{type:"elem",elem:e},{type:"kern",size:l.kern},{type:"elem",elem:l.elem,marginLeft:F(i)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}var v=[m];if(s&&0!==i&&!c){var b=Ze.makeSpan(["mspace"],[],a);b.style.marginRight=F(i),v.unshift(b)}return Ze.makeSpan(["mop","op-limits"],v,a)},sa=["\\smallint"],la=(e,t)=>{var r,a,n,i=!1;"supsub"===e.type?(r=e.sup,a=e.sub,n=Ft(e.base,"op"),i=!0):n=Ft(e,"op");var o,s=t.style,l=!1;if(s.size===w.DISPLAY.size&&n.symbol&&!h.contains(sa,n.name)&&(l=!0),n.symbol){var m=l?"Size2-Regular":"Size1-Regular",c="";if("\\oiint"!==n.name&&"\\oiiint"!==n.name||(c=n.name.slice(1),n.name="oiint"===c?"\\iint":"\\iiint"),o=Ze.makeSymbol(n.name,m,"math",t,["mop","op-symbol",l?"large-op":"small-op"]),c.length>0){var p=o.italic,u=Ze.staticSvg(c+"Size"+(l?"2":"1"),t);o=Ze.makeVList({positionType:"individualShift",children:[{type:"elem",elem:o,shift:0},{type:"elem",elem:u,shift:l?.08:0}]},t),n.name="\\"+c,o.classes.unshift("mop"),o.italic=p}}else if(n.body){var d=dt(n.body,t,!0);1===d.length&&d[0]instanceof Z?(o=d[0]).classes[0]="mop":o=Ze.makeSpan(["mop"],d,t)}else{for(var g=[],f=1;f<n.name.length;f++)g.push(Ze.mathsym(n.name[f],n.mode,t));o=Ze.makeSpan(["mop"],g,t)}var v=0,b=0;return(o instanceof Z||"\\oiint"===n.name||"\\oiiint"===n.name)&&!n.suppressBaseShift&&(v=(o.height-o.depth)/2-t.fontMetrics().axisHeight,b=o.italic),i?oa(o,r,a,t,s,b,v):(v&&(o.style.position="relative",o.style.top=F(v)),o)},ha=(e,t)=>{var r;if(e.symbol)r=new Mt("mo",[Tt(e.name,e.mode)]),h.contains(sa,e.name)&&r.setAttribute("largeop","false");else if(e.body)r=new Mt("mo",Nt(e.body,t));else{r=new Mt("mi",[new zt(e.name.slice(1))]);var a=new Mt("mo",[Tt("⁡","text")]);r=e.parentIsSupSub?new Mt("mrow",[r,a]):St([r,a])}return r},ma={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};it({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=a;return 1===n.length&&(n=ma[n]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:la,mathmlBuilder:ha}),it({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:lt(a)}},htmlBuilder:la,mathmlBuilder:ha});var ca={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};it({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:la,mathmlBuilder:ha}),it({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:la,mathmlBuilder:ha}),it({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e,a=r;return 1===a.length&&(a=ca[a]),{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:la,mathmlBuilder:ha});var pa=(e,t)=>{var r,a,n,i,o=!1;if("supsub"===e.type?(r=e.sup,a=e.sub,n=Ft(e.base,"operatorname"),o=!0):n=Ft(e,"operatorname"),n.body.length>0){for(var s=n.body.map((e=>{var t=e.text;return"string"==typeof t?{type:"textord",mode:e.mode,text:t}:e})),l=dt(s,t.withFont("mathrm"),!0),h=0;h<l.length;h++){var m=l[h];m instanceof Z&&(m.text=m.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}i=Ze.makeSpan(["mop"],l,t)}else i=Ze.makeSpan(["mop"],[],t);return o?oa(i,r,a,t,t.style,0,0):i};function ua(e,t,r){for(var a=dt(e,t,!1),n=t.sizeMultiplier/r.sizeMultiplier,i=0;i<a.length;i++){var o=a[i].classes.indexOf("sizing");o<0?Array.prototype.push.apply(a[i].classes,t.sizingClasses(r)):a[i].classes[o+1]==="reset-size"+t.size&&(a[i].classes[o+1]="reset-size"+r.size),a[i].height*=n,a[i].depth*=n}return Ze.makeFragment(a)}it({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"operatorname",mode:r.mode,body:lt(n),alwaysHandleSupSub:"\\operatornamewithlimits"===a,limits:!1,parentIsSupSub:!1}},htmlBuilder:pa,mathmlBuilder:(e,t)=>{for(var r=Nt(e.body,t.withFont("mathrm")),a=!0,n=0;n<r.length;n++){var i=r[n];if(i instanceof At.SpaceNode);else if(i instanceof At.MathNode)switch(i.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":var o=i.children[0];1===i.children.length&&o instanceof At.TextNode?o.text=o.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break;default:a=!1}else a=!1}if(a){var s=r.map((e=>e.toText())).join("");r=[new At.TextNode(s)]}var l=new At.MathNode("mi",r);l.setAttribute("mathvariant","normal");var h=new At.MathNode("mo",[Tt("⁡","text")]);return e.parentIsSupSub?new At.MathNode("mrow",[l,h]):At.newDocumentFragment([l,h])}}),Dr("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),ot({type:"ordgroup",htmlBuilder(e,t){return e.semisimple?Ze.makeFragment(dt(e.body,t,!1)):Ze.makeSpan(["mord"],dt(e.body,t,!0),t)},mathmlBuilder(e,t){return qt(e.body,t,!0)}}),it({type:"overline",names:["\\overline"],props:{numArgs:1},handler(e,t){var{parser:r}=e,a=t[0];return{type:"overline",mode:r.mode,body:a}},htmlBuilder(e,t){var r=xt(e.body,t.havingCrampedStyle()),a=Ze.makeLineSpan("overline-line",t),n=t.fontMetrics().defaultRuleThickness,i=Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*n},{type:"elem",elem:a},{type:"kern",size:n}]},t);return Ze.makeSpan(["mord","overline"],[i],t)},mathmlBuilder(e,t){var r=new At.MathNode("mo",[new At.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new At.MathNode("mover",[It(e.body,t),r]);return a.setAttribute("accent","true"),a}}),it({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"phantom",mode:r.mode,body:lt(a)}},htmlBuilder:(e,t)=>{var r=dt(e.body,t.withPhantom(),!1);return Ze.makeFragment(r)},mathmlBuilder:(e,t)=>{var r=Nt(e.body,t);return new At.MathNode("mphantom",r)}}),it({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"hphantom",mode:r.mode,body:a}},htmlBuilder:(e,t)=>{var r=Ze.makeSpan([],[xt(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var a=0;a<r.children.length;a++)r.children[a].height=0,r.children[a].depth=0;return r=Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t),Ze.makeSpan(["mord"],[r],t)},mathmlBuilder:(e,t)=>{var r=Nt(lt(e.body),t),a=new At.MathNode("mphantom",r),n=new At.MathNode("mpadded",[a]);return n.setAttribute("height","0px"),n.setAttribute("depth","0px"),n}}),it({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"vphantom",mode:r.mode,body:a}},htmlBuilder:(e,t)=>{var r=Ze.makeSpan(["inner"],[xt(e.body,t.withPhantom())]),a=Ze.makeSpan(["fix"],[]);return Ze.makeSpan(["mord","rlap"],[r,a],t)},mathmlBuilder:(e,t)=>{var r=Nt(lt(e.body),t),a=new At.MathNode("mphantom",r),n=new At.MathNode("mpadded",[a]);return n.setAttribute("width","0px"),n}}),it({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e,a=Ft(t[0],"size").value,n=t[1];return{type:"raisebox",mode:r.mode,dy:a,body:n}},htmlBuilder(e,t){var r=xt(e.body,t),a=V(e.dy,t);return Ze.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:r}]},t)},mathmlBuilder(e,t){var r=new At.MathNode("mpadded",[It(e.body,t)]),a=e.dy.number+e.dy.unit;return r.setAttribute("voffset",a),r}}),it({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(e){var{parser:t}=e;return{type:"internal",mode:t.mode}}}),it({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler(e,t,r){var{parser:a}=e,n=r[0],i=Ft(t[0],"size"),o=Ft(t[1],"size");return{type:"rule",mode:a.mode,shift:n&&Ft(n,"size").value,width:i.value,height:o.value}},htmlBuilder(e,t){var r=Ze.makeSpan(["mord","rule"],[],t),a=V(e.width,t),n=V(e.height,t),i=e.shift?V(e.shift,t):0;return r.style.borderRightWidth=F(a),r.style.borderTopWidth=F(n),r.style.bottom=F(i),r.width=a,r.height=n+i,r.depth=-i,r.maxFontSize=1.125*n*t.sizeMultiplier,r},mathmlBuilder(e,t){var r=V(e.width,t),a=V(e.height,t),n=e.shift?V(e.shift,t):0,i=t.color&&t.getColor()||"black",o=new At.MathNode("mspace");o.setAttribute("mathbackground",i),o.setAttribute("width",F(r)),o.setAttribute("height",F(a));var s=new At.MathNode("mpadded",[o]);return n>=0?s.setAttribute("height",F(n)):(s.setAttribute("height",F(n)),s.setAttribute("depth",F(-n))),s.setAttribute("voffset",F(n)),s}});var da=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];it({type:"sizing",names:da,props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{breakOnTokenText:r,funcName:a,parser:n}=e,i=n.parseExpression(!1,r);return{type:"sizing",mode:n.mode,size:da.indexOf(a)+1,body:i}},htmlBuilder:(e,t)=>{var r=t.havingSize(e.size);return ua(e.body,r,t)},mathmlBuilder:(e,t)=>{var r=t.havingSize(e.size),a=Nt(e.body,r),n=new At.MathNode("mstyle",a);return n.setAttribute("mathsize",F(r.sizeMultiplier)),n}}),it({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(e,t,r)=>{var{parser:a}=e,n=!1,i=!1,o=r[0]&&Ft(r[0],"ordgroup");if(o)for(var s="",l=0;l<o.body.length;++l){if("t"===(s=o.body[l].text))n=!0;else{if("b"!==s){n=!1,i=!1;break}i=!0}}else n=!0,i=!0;var h=t[0];return{type:"smash",mode:a.mode,body:h,smashHeight:n,smashDepth:i}},htmlBuilder:(e,t)=>{var r=Ze.makeSpan([],[xt(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(var a=0;a<r.children.length;a++)r.children[a].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].depth=0;var i=Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t);return Ze.makeSpan(["mord"],[i],t)},mathmlBuilder:(e,t)=>{var r=new At.MathNode("mpadded",[It(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),it({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:a}=e,n=r[0],i=t[0];return{type:"sqrt",mode:a.mode,body:i,index:n}},htmlBuilder(e,t){var r=xt(e.body,t.havingCrampedStyle());0===r.height&&(r.height=t.fontMetrics().xHeight),r=Ze.wrapFragment(r,t);var a=t.fontMetrics().defaultRuleThickness,n=a;t.style.id<w.TEXT.id&&(n=t.fontMetrics().xHeight);var i=a+n/4,o=r.height+r.depth+i+a,{span:s,ruleWidth:l,advanceWidth:h}=Br.sqrtImage(o,t),m=s.height-l;m>r.height+r.depth+i&&(i=(i+m-r.height-r.depth)/2);var c=s.height-r.height-i-l;r.style.paddingLeft=F(h);var p=Ze.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+c)},{type:"elem",elem:s},{type:"kern",size:l}]},t);if(e.index){var u=t.havingStyle(w.SCRIPTSCRIPT),d=xt(e.index,u,t),g=.6*(p.height-p.depth),f=Ze.makeVList({positionType:"shift",positionData:-g,children:[{type:"elem",elem:d}]},t),v=Ze.makeSpan(["root"],[f]);return Ze.makeSpan(["mord","sqrt"],[v,p],t)}return Ze.makeSpan(["mord","sqrt"],[p],t)},mathmlBuilder(e,t){var{body:r,index:a}=e;return a?new At.MathNode("mroot",[It(r,t),It(a,t)]):new At.MathNode("msqrt",[It(r,t)])}});var ga={display:w.DISPLAY,text:w.TEXT,script:w.SCRIPT,scriptscript:w.SCRIPTSCRIPT};it({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e,t){var{breakOnTokenText:r,funcName:a,parser:n}=e,i=n.parseExpression(!0,r),o=a.slice(1,a.length-5);return{type:"styling",mode:n.mode,style:o,body:i}},htmlBuilder(e,t){var r=ga[e.style],a=t.havingStyle(r).withFont("");return ua(e.body,a,t)},mathmlBuilder(e,t){var r=ga[e.style],a=t.havingStyle(r),n=Nt(e.body,a),i=new At.MathNode("mstyle",n),o={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[e.style];return i.setAttribute("scriptlevel",o[0]),i.setAttribute("displaystyle",o[1]),i}});ot({type:"supsub",htmlBuilder(e,t){var r=function(e,t){var r=e.base;return r?"op"===r.type?r.limits&&(t.style.size===w.DISPLAY.size||r.alwaysHandleSupSub)?la:null:"operatorname"===r.type?r.alwaysHandleSupSub&&(t.style.size===w.DISPLAY.size||r.limits)?pa:null:"accent"===r.type?h.isCharacterBox(r.base)?Yt:null:"horizBrace"===r.type&&!e.sub===r.isOver?aa:null:null}(e,t);if(r)return r(e,t);var a,n,i,{base:o,sup:s,sub:l}=e,m=xt(o,t),c=t.fontMetrics(),p=0,u=0,d=o&&h.isCharacterBox(o);if(s){var g=t.havingStyle(t.style.sup());a=xt(s,g,t),d||(p=m.height-g.fontMetrics().supDrop*g.sizeMultiplier/t.sizeMultiplier)}if(l){var f=t.havingStyle(t.style.sub());n=xt(l,f,t),d||(u=m.depth+f.fontMetrics().subDrop*f.sizeMultiplier/t.sizeMultiplier)}i=t.style===w.DISPLAY?c.sup1:t.style.cramped?c.sup3:c.sup2;var v,b=t.sizeMultiplier,y=F(.5/c.ptPerEm/b),x=null;if(n){var k=e.base&&"op"===e.base.type&&e.base.name&&("\\oiint"===e.base.name||"\\oiiint"===e.base.name);(m instanceof Z||k)&&(x=F(-m.italic))}if(a&&n){p=Math.max(p,i,a.depth+.25*c.xHeight),u=Math.max(u,c.sub2);var S=4*c.defaultRuleThickness;if(p-a.depth-(n.height-u)<S){u=S-(p-a.depth)+n.height;var M=.8*c.xHeight-(p-a.depth);M>0&&(p+=M,u-=M)}var z=[{type:"elem",elem:n,shift:u,marginRight:y,marginLeft:x},{type:"elem",elem:a,shift:-p,marginRight:y}];v=Ze.makeVList({positionType:"individualShift",children:z},t)}else if(n){u=Math.max(u,c.sub1,n.height-.8*c.xHeight);var A=[{type:"elem",elem:n,marginLeft:x,marginRight:y}];v=Ze.makeVList({positionType:"shift",positionData:u,children:A},t)}else{if(!a)throw new Error("supsub must have either sup or sub.");p=Math.max(p,i,a.depth+.25*c.xHeight),v=Ze.makeVList({positionType:"shift",positionData:-p,children:[{type:"elem",elem:a,marginRight:y}]},t)}var T=bt(m,"right")||"mord";return Ze.makeSpan([T],[m,Ze.makeSpan(["msupsub"],[v])],t)},mathmlBuilder(e,t){var r,a=!1;e.base&&"horizBrace"===e.base.type&&!!e.sup===e.base.isOver&&(a=!0,r=e.base.isOver),!e.base||"op"!==e.base.type&&"operatorname"!==e.base.type||(e.base.parentIsSupSub=!0);var n,i=[It(e.base,t)];if(e.sub&&i.push(It(e.sub,t)),e.sup&&i.push(It(e.sup,t)),a)n=r?"mover":"munder";else if(e.sub)if(e.sup){var o=e.base;n=o&&"op"===o.type&&o.limits&&t.style===w.DISPLAY||o&&"operatorname"===o.type&&o.alwaysHandleSupSub&&(t.style===w.DISPLAY||o.limits)?"munderover":"msubsup"}else{var s=e.base;n=s&&"op"===s.type&&s.limits&&(t.style===w.DISPLAY||s.alwaysHandleSupSub)||s&&"operatorname"===s.type&&s.alwaysHandleSupSub&&(s.limits||t.style===w.DISPLAY)?"munder":"msub"}else{var l=e.base;n=l&&"op"===l.type&&l.limits&&(t.style===w.DISPLAY||l.alwaysHandleSupSub)||l&&"operatorname"===l.type&&l.alwaysHandleSupSub&&(l.limits||t.style===w.DISPLAY)?"mover":"msup"}return new At.MathNode(n,i)}}),ot({type:"atom",htmlBuilder(e,t){return Ze.mathsym(e.text,e.mode,t,["m"+e.family])},mathmlBuilder(e,t){var r=new At.MathNode("mo",[Tt(e.text,e.mode)]);if("bin"===e.family){var a=Ct(e,t);"bold-italic"===a&&r.setAttribute("mathvariant",a)}else"punct"===e.family?r.setAttribute("separator","true"):"open"!==e.family&&"close"!==e.family||r.setAttribute("stretchy","false");return r}});var fa={mi:"italic",mn:"normal",mtext:"normal"};ot({type:"mathord",htmlBuilder(e,t){return Ze.makeOrd(e,t,"mathord")},mathmlBuilder(e,t){var r=new At.MathNode("mi",[Tt(e.text,e.mode,t)]),a=Ct(e,t)||"italic";return a!==fa[r.type]&&r.setAttribute("mathvariant",a),r}}),ot({type:"textord",htmlBuilder(e,t){return Ze.makeOrd(e,t,"textord")},mathmlBuilder(e,t){var r,a=Tt(e.text,e.mode,t),n=Ct(e,t)||"normal";return r="text"===e.mode?new At.MathNode("mtext",[a]):/[0-9]/.test(e.text)?new At.MathNode("mn",[a]):"\\prime"===e.text?new At.MathNode("mo",[a]):new At.MathNode("mi",[a]),n!==fa[r.type]&&r.setAttribute("mathvariant",n),r}});var va={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},ba={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};ot({type:"spacing",htmlBuilder(e,t){if(ba.hasOwnProperty(e.text)){var r=ba[e.text].className||"";if("text"===e.mode){var a=Ze.makeOrd(e,t,"textord");return a.classes.push(r),a}return Ze.makeSpan(["mspace",r],[Ze.mathsym(e.text,e.mode,t)],t)}if(va.hasOwnProperty(e.text))return Ze.makeSpan(["mspace",va[e.text]],[],t);throw new n('Unknown type of space "'+e.text+'"')},mathmlBuilder(e,t){if(!ba.hasOwnProperty(e.text)){if(va.hasOwnProperty(e.text))return new At.MathNode("mspace");throw new n('Unknown type of space "'+e.text+'"')}return new At.MathNode("mtext",[new At.TextNode(" ")])}});var ya=()=>{var e=new At.MathNode("mtd",[]);return e.setAttribute("width","50%"),e};ot({type:"tag",mathmlBuilder(e,t){var r=new At.MathNode("mtable",[new At.MathNode("mtr",[ya(),new At.MathNode("mtd",[qt(e.body,t)]),ya(),new At.MathNode("mtd",[qt(e.tag,t)])])]);return r.setAttribute("width","100%"),r}});var xa={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},wa={"\\textbf":"textbf","\\textmd":"textmd"},ka={"\\textit":"textit","\\textup":"textup"},Sa=(e,t)=>{var r=e.font;return r?xa[r]?t.withTextFontFamily(xa[r]):wa[r]?t.withTextFontWeight(wa[r]):t.withTextFontShape(ka[r]):t};it({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"text",mode:r.mode,body:lt(n),font:a}},htmlBuilder(e,t){var r=Sa(e,t),a=dt(e.body,r,!0);return Ze.makeSpan(["mord","text"],a,r)},mathmlBuilder(e,t){var r=Sa(e,t);return qt(e.body,r)}}),it({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"underline",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=xt(e.body,t),a=Ze.makeLineSpan("underline-line",t),n=t.fontMetrics().defaultRuleThickness,i=Ze.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:n},{type:"elem",elem:a},{type:"kern",size:3*n},{type:"elem",elem:r}]},t);return Ze.makeSpan(["mord","underline"],[i],t)},mathmlBuilder(e,t){var r=new At.MathNode("mo",[new At.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new At.MathNode("munder",[It(e.body,t),r]);return a.setAttribute("accentunder","true"),a}}),it({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"vcenter",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=xt(e.body,t),a=t.fontMetrics().axisHeight,n=.5*(r.height-a-(r.depth+a));return Ze.makeVList({positionType:"shift",positionData:n,children:[{type:"elem",elem:r}]},t)},mathmlBuilder(e,t){return new At.MathNode("mpadded",[It(e.body,t)],["vcenter"])}}),it({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(e,t,r){throw new n("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(e,t){for(var r=Ma(e),a=[],n=t.havingStyle(t.style.text()),i=0;i<r.length;i++){var o=r[i];"~"===o&&(o="\\textasciitilde"),a.push(Ze.makeSymbol(o,"Typewriter-Regular",e.mode,n,["mord","texttt"]))}return Ze.makeSpan(["mord","text"].concat(n.sizingClasses(t)),Ze.tryCombineChars(a),n)},mathmlBuilder(e,t){var r=new At.TextNode(Ma(e)),a=new At.MathNode("mtext",[r]);return a.setAttribute("mathvariant","monospace"),a}});var Ma=e=>e.body.replace(/ /g,e.star?"␣":" "),za=rt,Aa="[ \r\n\t]",Ta="(\\\\[a-zA-Z@]+)"+Aa+"*",Ba="[̀-ͯ]",Ca=new RegExp(Ba+"+$"),Na="("+Aa+"+)|\\\\(\n|[ \r\t]+\n?)[ \r\t]*|([!-\\[\\]-‧‪-퟿豈-￿]"+Ba+"*|[\ud800-\udbff][\udc00-\udfff]"+Ba+"*|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5|"+Ta+"|\\\\[^\ud800-\udfff])";class qa{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=new RegExp(Na,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new a("EOF",new r(this,t,t));var i=this.tokenRegex.exec(e);if(null===i||i.index!==t)throw new n("Unexpected character: '"+e[t]+"'",new a(e[t],new r(this,t,t+1)));var o=i[6]||i[3]||(i[2]?"\\ ":" ");if(14===this.catcodes[o]){var s=e.indexOf("\n",this.tokenRegex.lastIndex);return-1===s?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=s+1,this.lex()}return new a(o,new r(this,t,this.tokenRegex.lastIndex))}}class Ia{constructor(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(0===this.undefStack.length)throw new n("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(null==e[t]?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(void 0===r&&(r=!1),r){for(var a=0;a<this.undefStack.length;a++)delete this.undefStack[a][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var n=this.undefStack[this.undefStack.length-1];n&&!n.hasOwnProperty(e)&&(n[e]=this.current[e])}null==t?delete this.current[e]:this.current[e]=t}}var Ra=Lr;Dr("\\noexpand",(function(e){var t=e.popToken();return e.isExpandable(t.text)&&(t.noexpand=!0,t.treatAsRelax=!0),{tokens:[t],numArgs:0}})),Dr("\\expandafter",(function(e){var t=e.popToken();return e.expandOnce(!0),{tokens:[t],numArgs:0}})),Dr("\\@firstoftwo",(function(e){return{tokens:e.consumeArgs(2)[0],numArgs:0}})),Dr("\\@secondoftwo",(function(e){return{tokens:e.consumeArgs(2)[1],numArgs:0}})),Dr("\\@ifnextchar",(function(e){var t=e.consumeArgs(3);e.consumeSpaces();var r=e.future();return 1===t[0].length&&t[0][0].text===r.text?{tokens:t[1],numArgs:0}:{tokens:t[2],numArgs:0}})),Dr("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),Dr("\\TextOrMath",(function(e){var t=e.consumeArgs(2);return"text"===e.mode?{tokens:t[0],numArgs:0}:{tokens:t[1],numArgs:0}}));var Ha={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};Dr("\\char",(function(e){var t,r=e.popToken(),a="";if("'"===r.text)t=8,r=e.popToken();else if('"'===r.text)t=16,r=e.popToken();else if("`"===r.text)if("\\"===(r=e.popToken()).text[0])a=r.text.charCodeAt(1);else{if("EOF"===r.text)throw new n("\\char` missing argument");a=r.text.charCodeAt(0)}else t=10;if(t){if(null==(a=Ha[r.text])||a>=t)throw new n("Invalid base-"+t+" digit "+r.text);for(var i;null!=(i=Ha[e.future().text])&&i<t;)a*=t,a+=i,e.popToken()}return"\\@char{"+a+"}"}));var Oa=(e,t,r)=>{var a=e.consumeArg().tokens;if(1!==a.length)throw new n("\\newcommand's first argument must be a macro name");var i=a[0].text,o=e.isDefined(i);if(o&&!t)throw new n("\\newcommand{"+i+"} attempting to redefine "+i+"; use \\renewcommand");if(!o&&!r)throw new n("\\renewcommand{"+i+"} when command "+i+" does not yet exist; use \\newcommand");var s=0;if(1===(a=e.consumeArg().tokens).length&&"["===a[0].text){for(var l="",h=e.expandNextToken();"]"!==h.text&&"EOF"!==h.text;)l+=h.text,h=e.expandNextToken();if(!l.match(/^\s*[0-9]+\s*$/))throw new n("Invalid number of arguments: "+l);s=parseInt(l),a=e.consumeArg().tokens}return e.macros.set(i,{tokens:a,numArgs:s}),""};Dr("\\newcommand",(e=>Oa(e,!1,!0))),Dr("\\renewcommand",(e=>Oa(e,!0,!1))),Dr("\\providecommand",(e=>Oa(e,!0,!0))),Dr("\\message",(e=>{var t=e.consumeArgs(1)[0];return console.log(t.reverse().map((e=>e.text)).join("")),""})),Dr("\\errmessage",(e=>{var t=e.consumeArgs(1)[0];return console.error(t.reverse().map((e=>e.text)).join("")),""})),Dr("\\show",(e=>{var t=e.popToken(),r=t.text;return console.log(t,e.macros.get(r),za[r],ae.math[r],ae.text[r]),""})),Dr("\\bgroup","{"),Dr("\\egroup","}"),Dr("~","\\nobreakspace"),Dr("\\lq","`"),Dr("\\rq","'"),Dr("\\aa","\\r a"),Dr("\\AA","\\r A"),Dr("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}"),Dr("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),Dr("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}"),Dr("ℬ","\\mathscr{B}"),Dr("ℰ","\\mathscr{E}"),Dr("ℱ","\\mathscr{F}"),Dr("ℋ","\\mathscr{H}"),Dr("ℐ","\\mathscr{I}"),Dr("ℒ","\\mathscr{L}"),Dr("ℳ","\\mathscr{M}"),Dr("ℛ","\\mathscr{R}"),Dr("ℭ","\\mathfrak{C}"),Dr("ℌ","\\mathfrak{H}"),Dr("ℨ","\\mathfrak{Z}"),Dr("\\Bbbk","\\Bbb{k}"),Dr("·","\\cdotp"),Dr("\\llap","\\mathllap{\\textrm{#1}}"),Dr("\\rlap","\\mathrlap{\\textrm{#1}}"),Dr("\\clap","\\mathclap{\\textrm{#1}}"),Dr("\\mathstrut","\\vphantom{(}"),Dr("\\underbar","\\underline{\\text{#1}}"),Dr("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),Dr("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}"),Dr("\\ne","\\neq"),Dr("≠","\\neq"),Dr("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}"),Dr("∉","\\notin"),Dr("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}"),Dr("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}"),Dr("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}"),Dr("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}"),Dr("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}"),Dr("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}"),Dr("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}"),Dr("⟂","\\perp"),Dr("‼","\\mathclose{!\\mkern-0.8mu!}"),Dr("∌","\\notni"),Dr("⌜","\\ulcorner"),Dr("⌝","\\urcorner"),Dr("⌞","\\llcorner"),Dr("⌟","\\lrcorner"),Dr("©","\\copyright"),Dr("®","\\textregistered"),Dr("️","\\textregistered"),Dr("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),Dr("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),Dr("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),Dr("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),Dr("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}"),Dr("⋮","\\vdots"),Dr("\\varGamma","\\mathit{\\Gamma}"),Dr("\\varDelta","\\mathit{\\Delta}"),Dr("\\varTheta","\\mathit{\\Theta}"),Dr("\\varLambda","\\mathit{\\Lambda}"),Dr("\\varXi","\\mathit{\\Xi}"),Dr("\\varPi","\\mathit{\\Pi}"),Dr("\\varSigma","\\mathit{\\Sigma}"),Dr("\\varUpsilon","\\mathit{\\Upsilon}"),Dr("\\varPhi","\\mathit{\\Phi}"),Dr("\\varPsi","\\mathit{\\Psi}"),Dr("\\varOmega","\\mathit{\\Omega}"),Dr("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),Dr("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),Dr("\\boxed","\\fbox{$\\displaystyle{#1}$}"),Dr("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),Dr("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),Dr("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var Ea={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};Dr("\\dots",(function(e){var t="\\dotso",r=e.expandAfterFuture().text;return r in Ea?t=Ea[r]:("\\not"===r.slice(0,4)||r in ae.math&&h.contains(["bin","rel"],ae.math[r].group))&&(t="\\dotsb"),t}));var La={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};Dr("\\dotso",(function(e){return e.future().text in La?"\\ldots\\,":"\\ldots"})),Dr("\\dotsc",(function(e){var t=e.future().text;return t in La&&","!==t?"\\ldots\\,":"\\ldots"})),Dr("\\cdots",(function(e){return e.future().text in La?"\\@cdots\\,":"\\@cdots"})),Dr("\\dotsb","\\cdots"),Dr("\\dotsm","\\cdots"),Dr("\\dotsi","\\!\\cdots"),Dr("\\dotsx","\\ldots\\,"),Dr("\\DOTSI","\\relax"),Dr("\\DOTSB","\\relax"),Dr("\\DOTSX","\\relax"),Dr("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),Dr("\\,","\\tmspace+{3mu}{.1667em}"),Dr("\\thinspace","\\,"),Dr("\\>","\\mskip{4mu}"),Dr("\\:","\\tmspace+{4mu}{.2222em}"),Dr("\\medspace","\\:"),Dr("\\;","\\tmspace+{5mu}{.2777em}"),Dr("\\thickspace","\\;"),Dr("\\!","\\tmspace-{3mu}{.1667em}"),Dr("\\negthinspace","\\!"),Dr("\\negmedspace","\\tmspace-{4mu}{.2222em}"),Dr("\\negthickspace","\\tmspace-{5mu}{.277em}"),Dr("\\enspace","\\kern.5em "),Dr("\\enskip","\\hskip.5em\\relax"),Dr("\\quad","\\hskip1em\\relax"),Dr("\\qquad","\\hskip2em\\relax"),Dr("\\tag","\\@ifstar\\tag@literal\\tag@paren"),Dr("\\tag@paren","\\tag@literal{({#1})}"),Dr("\\tag@literal",(e=>{if(e.macros.get("\\df@tag"))throw new n("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"})),Dr("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),Dr("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),Dr("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),Dr("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),Dr("\\newline","\\\\\\relax"),Dr("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var Da=F(B["Main-Regular"]["T".charCodeAt(0)][1]-.7*B["Main-Regular"]["A".charCodeAt(0)][1]);Dr("\\LaTeX","\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+Da+"}{\\scriptstyle A}\\kern-.15em\\TeX}{LaTeX}}"),Dr("\\KaTeX","\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+Da+"}{\\scriptstyle A}\\kern-.15em\\TeX}{KaTeX}}"),Dr("\\hspace","\\@ifstar\\@hspacer\\@hspace"),Dr("\\@hspace","\\hskip #1\\relax"),Dr("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),Dr("\\ordinarycolon",":"),Dr("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),Dr("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),Dr("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),Dr("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),Dr("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),Dr("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),Dr("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),Dr("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),Dr("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),Dr("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),Dr("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),Dr("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),Dr("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),Dr("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),Dr("∷","\\dblcolon"),Dr("∹","\\eqcolon"),Dr("≔","\\coloneqq"),Dr("≕","\\eqqcolon"),Dr("⩴","\\Coloneqq"),Dr("\\ratio","\\vcentcolon"),Dr("\\coloncolon","\\dblcolon"),Dr("\\colonequals","\\coloneqq"),Dr("\\coloncolonequals","\\Coloneqq"),Dr("\\equalscolon","\\eqqcolon"),Dr("\\equalscoloncolon","\\Eqqcolon"),Dr("\\colonminus","\\coloneq"),Dr("\\coloncolonminus","\\Coloneq"),Dr("\\minuscolon","\\eqcolon"),Dr("\\minuscoloncolon","\\Eqcolon"),Dr("\\coloncolonapprox","\\Colonapprox"),Dr("\\coloncolonsim","\\Colonsim"),Dr("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),Dr("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),Dr("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),Dr("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),Dr("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}"),Dr("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),Dr("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),Dr("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),Dr("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),Dr("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),Dr("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),Dr("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),Dr("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),Dr("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}"),Dr("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}"),Dr("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}"),Dr("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}"),Dr("\\nleqq","\\html@mathml{\\@nleqq}{≰}"),Dr("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}"),Dr("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}"),Dr("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}"),Dr("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}"),Dr("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}"),Dr("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}"),Dr("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}"),Dr("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}"),Dr("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}"),Dr("\\imath","\\html@mathml{\\@imath}{ı}"),Dr("\\jmath","\\html@mathml{\\@jmath}{ȷ}"),Dr("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}"),Dr("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}"),Dr("⟦","\\llbracket"),Dr("⟧","\\rrbracket"),Dr("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}"),Dr("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}"),Dr("⦃","\\lBrace"),Dr("⦄","\\rBrace"),Dr("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}"),Dr("⦵","\\minuso"),Dr("\\darr","\\downarrow"),Dr("\\dArr","\\Downarrow"),Dr("\\Darr","\\Downarrow"),Dr("\\lang","\\langle"),Dr("\\rang","\\rangle"),Dr("\\uarr","\\uparrow"),Dr("\\uArr","\\Uparrow"),Dr("\\Uarr","\\Uparrow"),Dr("\\N","\\mathbb{N}"),Dr("\\R","\\mathbb{R}"),Dr("\\Z","\\mathbb{Z}"),Dr("\\alef","\\aleph"),Dr("\\alefsym","\\aleph"),Dr("\\Alpha","\\mathrm{A}"),Dr("\\Beta","\\mathrm{B}"),Dr("\\bull","\\bullet"),Dr("\\Chi","\\mathrm{X}"),Dr("\\clubs","\\clubsuit"),Dr("\\cnums","\\mathbb{C}"),Dr("\\Complex","\\mathbb{C}"),Dr("\\Dagger","\\ddagger"),Dr("\\diamonds","\\diamondsuit"),Dr("\\empty","\\emptyset"),Dr("\\Epsilon","\\mathrm{E}"),Dr("\\Eta","\\mathrm{H}"),Dr("\\exist","\\exists"),Dr("\\harr","\\leftrightarrow"),Dr("\\hArr","\\Leftrightarrow"),Dr("\\Harr","\\Leftrightarrow"),Dr("\\hearts","\\heartsuit"),Dr("\\image","\\Im"),Dr("\\infin","\\infty"),Dr("\\Iota","\\mathrm{I}"),Dr("\\isin","\\in"),Dr("\\Kappa","\\mathrm{K}"),Dr("\\larr","\\leftarrow"),Dr("\\lArr","\\Leftarrow"),Dr("\\Larr","\\Leftarrow"),Dr("\\lrarr","\\leftrightarrow"),Dr("\\lrArr","\\Leftrightarrow"),Dr("\\Lrarr","\\Leftrightarrow"),Dr("\\Mu","\\mathrm{M}"),Dr("\\natnums","\\mathbb{N}"),Dr("\\Nu","\\mathrm{N}"),Dr("\\Omicron","\\mathrm{O}"),Dr("\\plusmn","\\pm"),Dr("\\rarr","\\rightarrow"),Dr("\\rArr","\\Rightarrow"),Dr("\\Rarr","\\Rightarrow"),Dr("\\real","\\Re"),Dr("\\reals","\\mathbb{R}"),Dr("\\Reals","\\mathbb{R}"),Dr("\\Rho","\\mathrm{P}"),Dr("\\sdot","\\cdot"),Dr("\\sect","\\S"),Dr("\\spades","\\spadesuit"),Dr("\\sub","\\subset"),Dr("\\sube","\\subseteq"),Dr("\\supe","\\supseteq"),Dr("\\Tau","\\mathrm{T}"),Dr("\\thetasym","\\vartheta"),Dr("\\weierp","\\wp"),Dr("\\Zeta","\\mathrm{Z}"),Dr("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),Dr("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),Dr("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),Dr("\\bra","\\mathinner{\\langle{#1}|}"),Dr("\\ket","\\mathinner{|{#1}\\rangle}"),Dr("\\braket","\\mathinner{\\langle{#1}\\rangle}"),Dr("\\Bra","\\left\\langle#1\\right|"),Dr("\\Ket","\\left|#1\\right\\rangle");var Pa=e=>t=>{var r=t.consumeArg().tokens,a=t.consumeArg().tokens,n=t.consumeArg().tokens,i=t.consumeArg().tokens,o=t.macros.get("|"),s=t.macros.get("\\|");t.macros.beginGroup();var l=t=>r=>{e&&(r.macros.set("|",o),n.length&&r.macros.set("\\|",s));var i=t;!t&&n.length&&("|"===r.future().text&&(r.popToken(),i=!0));return{tokens:i?n:a,numArgs:0}};t.macros.set("|",l(!1)),n.length&&t.macros.set("\\|",l(!0));var h=t.consumeArg().tokens,m=t.expandTokens([...i,...h,...r]);return t.macros.endGroup(),{tokens:m.reverse(),numArgs:0}};Dr("\\bra@ket",Pa(!1)),Dr("\\bra@set",Pa(!0)),Dr("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),Dr("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),Dr("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),Dr("\\angln","{\\angl n}"),Dr("\\blue","\\textcolor{##6495ed}{#1}"),Dr("\\orange","\\textcolor{##ffa500}{#1}"),Dr("\\pink","\\textcolor{##ff00af}{#1}"),Dr("\\red","\\textcolor{##df0030}{#1}"),Dr("\\green","\\textcolor{##28ae7b}{#1}"),Dr("\\gray","\\textcolor{gray}{#1}"),Dr("\\purple","\\textcolor{##9d38bd}{#1}"),Dr("\\blueA","\\textcolor{##ccfaff}{#1}"),Dr("\\blueB","\\textcolor{##80f6ff}{#1}"),Dr("\\blueC","\\textcolor{##63d9ea}{#1}"),Dr("\\blueD","\\textcolor{##11accd}{#1}"),Dr("\\blueE","\\textcolor{##0c7f99}{#1}"),Dr("\\tealA","\\textcolor{##94fff5}{#1}"),Dr("\\tealB","\\textcolor{##26edd5}{#1}"),Dr("\\tealC","\\textcolor{##01d1c1}{#1}"),Dr("\\tealD","\\textcolor{##01a995}{#1}"),Dr("\\tealE","\\textcolor{##208170}{#1}"),Dr("\\greenA","\\textcolor{##b6ffb0}{#1}"),Dr("\\greenB","\\textcolor{##8af281}{#1}"),Dr("\\greenC","\\textcolor{##74cf70}{#1}"),Dr("\\greenD","\\textcolor{##1fab54}{#1}"),Dr("\\greenE","\\textcolor{##0d923f}{#1}"),Dr("\\goldA","\\textcolor{##ffd0a9}{#1}"),Dr("\\goldB","\\textcolor{##ffbb71}{#1}"),Dr("\\goldC","\\textcolor{##ff9c39}{#1}"),Dr("\\goldD","\\textcolor{##e07d10}{#1}"),Dr("\\goldE","\\textcolor{##a75a05}{#1}"),Dr("\\redA","\\textcolor{##fca9a9}{#1}"),Dr("\\redB","\\textcolor{##ff8482}{#1}"),Dr("\\redC","\\textcolor{##f9685d}{#1}"),Dr("\\redD","\\textcolor{##e84d39}{#1}"),Dr("\\redE","\\textcolor{##bc2612}{#1}"),Dr("\\maroonA","\\textcolor{##ffbde0}{#1}"),Dr("\\maroonB","\\textcolor{##ff92c6}{#1}"),Dr("\\maroonC","\\textcolor{##ed5fa6}{#1}"),Dr("\\maroonD","\\textcolor{##ca337c}{#1}"),Dr("\\maroonE","\\textcolor{##9e034e}{#1}"),Dr("\\purpleA","\\textcolor{##ddd7ff}{#1}"),Dr("\\purpleB","\\textcolor{##c6b9fc}{#1}"),Dr("\\purpleC","\\textcolor{##aa87ff}{#1}"),Dr("\\purpleD","\\textcolor{##7854ab}{#1}"),Dr("\\purpleE","\\textcolor{##543b78}{#1}"),Dr("\\mintA","\\textcolor{##f5f9e8}{#1}"),Dr("\\mintB","\\textcolor{##edf2df}{#1}"),Dr("\\mintC","\\textcolor{##e0e5cc}{#1}"),Dr("\\grayA","\\textcolor{##f6f7f7}{#1}"),Dr("\\grayB","\\textcolor{##f0f1f2}{#1}"),Dr("\\grayC","\\textcolor{##e3e5e6}{#1}"),Dr("\\grayD","\\textcolor{##d6d8da}{#1}"),Dr("\\grayE","\\textcolor{##babec2}{#1}"),Dr("\\grayF","\\textcolor{##888d93}{#1}"),Dr("\\grayG","\\textcolor{##626569}{#1}"),Dr("\\grayH","\\textcolor{##3b3e40}{#1}"),Dr("\\grayI","\\textcolor{##21242c}{#1}"),Dr("\\kaBlue","\\textcolor{##314453}{#1}"),Dr("\\kaGreen","\\textcolor{##71B307}{#1}");var Va={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class Fa{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new Ia(Ra,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new qa(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,r,n;if(e){if(this.consumeSpaces(),"["!==this.future().text)return null;t=this.popToken(),({tokens:n,end:r}=this.consumeArg(["]"]))}else({tokens:n,start:t,end:r}=this.consumeArg());return this.pushToken(new a("EOF",r.loc)),this.pushTokens(n),t.range(r,"")}consumeSpaces(){for(;;){if(" "!==this.future().text)break;this.stack.pop()}}consumeArg(e){var t=[],r=e&&e.length>0;r||this.consumeSpaces();var a,i=this.future(),o=0,s=0;do{if(a=this.popToken(),t.push(a),"{"===a.text)++o;else if("}"===a.text){if(-1===--o)throw new n("Extra }",a)}else if("EOF"===a.text)throw new n("Unexpected end of input in a macro argument, expected '"+(e&&r?e[s]:"}")+"'",a);if(e&&r)if((0===o||1===o&&"{"===e[s])&&a.text===e[s]){if(++s===e.length){t.splice(-s,s);break}}else s=0}while(0!==o||r);return"{"===i.text&&"}"===t[t.length-1].text&&(t.pop(),t.shift()),t.reverse(),{tokens:t,start:i,end:a}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new n("The length of delimiters doesn't match the number of args!");for(var r=t[0],a=0;a<r.length;a++){var i=this.popToken();if(r[a]!==i.text)throw new n("Use of the macro doesn't match its definition",i)}}for(var o=[],s=0;s<e;s++)o.push(this.consumeArg(t&&t[s+1]).tokens);return o}expandOnce(e){var t=this.popToken(),r=t.text,a=t.noexpand?null:this._getExpansion(r);if(null==a||e&&a.unexpandable){if(e&&null==a&&"\\"===r[0]&&!this.isDefined(r))throw new n("Undefined control sequence: "+r);return this.pushToken(t),t}if(this.expansionCount++,this.expansionCount>this.settings.maxExpand)throw new n("Too many expansions: infinite loop or need to increase maxExpand setting");var i=a.tokens,o=this.consumeArgs(a.numArgs,a.delimiters);if(a.numArgs)for(var s=(i=i.slice()).length-1;s>=0;--s){var l=i[s];if("#"===l.text){if(0===s)throw new n("Incomplete placeholder at end of macro body",l);if("#"===(l=i[--s]).text)i.splice(s+1,1);else{if(!/^[1-9]$/.test(l.text))throw new n("Not a valid argument number",l);i.splice(s,2,...o[+l.text-1])}}}return this.pushTokens(i),i}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;){var e=this.expandOnce();if(e instanceof a)return e.treatAsRelax&&(e.text="\\relax"),this.stack.pop()}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new a(e)]):void 0}expandTokens(e){var t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;){var n=this.expandOnce(!0);n instanceof a&&(n.treatAsRelax&&(n.noexpand=!1,n.treatAsRelax=!1),t.push(this.stack.pop()))}return t}expandMacroAsText(e){var t=this.expandMacro(e);return t?t.map((e=>e.text)).join(""):t}_getExpansion(e){var t=this.macros.get(e);if(null==t)return t;if(1===e.length){var r=this.lexer.catcodes[e];if(null!=r&&13!==r)return}var a="function"==typeof t?t(this):t;if("string"==typeof a){var n=0;if(-1!==a.indexOf("#"))for(var i=a.replace(/##/g,"");-1!==i.indexOf("#"+(n+1));)++n;for(var o=new qa(a,this.settings),s=[],l=o.lex();"EOF"!==l.text;)s.push(l),l=o.lex();return s.reverse(),{tokens:s,numArgs:n}}return a}isDefined(e){return this.macros.has(e)||za.hasOwnProperty(e)||ae.math.hasOwnProperty(e)||ae.text.hasOwnProperty(e)||Va.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return null!=t?"string"==typeof t||"function"==typeof t||!t.unexpandable:za.hasOwnProperty(e)&&!za[e].primitive}}var Ga=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,Ua=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g","ʰ":"h","ⁱ":"i","ʲ":"j","ᵏ":"k","ˡ":"l","ᵐ":"m","ⁿ":"n","ᵒ":"o","ᵖ":"p","ʳ":"r","ˢ":"s","ᵗ":"t","ᵘ":"u","ᵛ":"v","ʷ":"w","ˣ":"x","ʸ":"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),Ya={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},Xa={"á":"á","à":"à","ä":"ä","ǟ":"ǟ","ã":"ã","ā":"ā","ă":"ă","ắ":"ắ","ằ":"ằ","ẵ":"ẵ","ǎ":"ǎ","â":"â","ấ":"ấ","ầ":"ầ","ẫ":"ẫ","ȧ":"ȧ","ǡ":"ǡ","å":"å","ǻ":"ǻ","ḃ":"ḃ","ć":"ć","ḉ":"ḉ","č":"č","ĉ":"ĉ","ċ":"ċ","ç":"ç","ď":"ď","ḋ":"ḋ","ḑ":"ḑ","é":"é","è":"è","ë":"ë","ẽ":"ẽ","ē":"ē","ḗ":"ḗ","ḕ":"ḕ","ĕ":"ĕ","ḝ":"ḝ","ě":"ě","ê":"ê","ế":"ế","ề":"ề","ễ":"ễ","ė":"ė","ȩ":"ȩ","ḟ":"ḟ","ǵ":"ǵ","ḡ":"ḡ","ğ":"ğ","ǧ":"ǧ","ĝ":"ĝ","ġ":"ġ","ģ":"ģ","ḧ":"ḧ","ȟ":"ȟ","ĥ":"ĥ","ḣ":"ḣ","ḩ":"ḩ","í":"í","ì":"ì","ï":"ï","ḯ":"ḯ","ĩ":"ĩ","ī":"ī","ĭ":"ĭ","ǐ":"ǐ","î":"î","ǰ":"ǰ","ĵ":"ĵ","ḱ":"ḱ","ǩ":"ǩ","ķ":"ķ","ĺ":"ĺ","ľ":"ľ","ļ":"ļ","ḿ":"ḿ","ṁ":"ṁ","ń":"ń","ǹ":"ǹ","ñ":"ñ","ň":"ň","ṅ":"ṅ","ņ":"ņ","ó":"ó","ò":"ò","ö":"ö","ȫ":"ȫ","õ":"õ","ṍ":"ṍ","ṏ":"ṏ","ȭ":"ȭ","ō":"ō","ṓ":"ṓ","ṑ":"ṑ","ŏ":"ŏ","ǒ":"ǒ","ô":"ô","ố":"ố","ồ":"ồ","ỗ":"ỗ","ȯ":"ȯ","ȱ":"ȱ","ő":"ő","ṕ":"ṕ","ṗ":"ṗ","ŕ":"ŕ","ř":"ř","ṙ":"ṙ","ŗ":"ŗ","ś":"ś","ṥ":"ṥ","š":"š","ṧ":"ṧ","ŝ":"ŝ","ṡ":"ṡ","ş":"ş","ẗ":"ẗ","ť":"ť","ṫ":"ṫ","ţ":"ţ","ú":"ú","ù":"ù","ü":"ü","ǘ":"ǘ","ǜ":"ǜ","ǖ":"ǖ","ǚ":"ǚ","ũ":"ũ","ṹ":"ṹ","ū":"ū","ṻ":"ṻ","ŭ":"ŭ","ǔ":"ǔ","û":"û","ů":"ů","ű":"ű","ṽ":"ṽ","ẃ":"ẃ","ẁ":"ẁ","ẅ":"ẅ","ŵ":"ŵ","ẇ":"ẇ","ẘ":"ẘ","ẍ":"ẍ","ẋ":"ẋ","ý":"ý","ỳ":"ỳ","ÿ":"ÿ","ỹ":"ỹ","ȳ":"ȳ","ŷ":"ŷ","ẏ":"ẏ","ẙ":"ẙ","ź":"ź","ž":"ž","ẑ":"ẑ","ż":"ż","Á":"Á","À":"À","Ä":"Ä","Ǟ":"Ǟ","Ã":"Ã","Ā":"Ā","Ă":"Ă","Ắ":"Ắ","Ằ":"Ằ","Ẵ":"Ẵ","Ǎ":"Ǎ","Â":"Â","Ấ":"Ấ","Ầ":"Ầ","Ẫ":"Ẫ","Ȧ":"Ȧ","Ǡ":"Ǡ","Å":"Å","Ǻ":"Ǻ","Ḃ":"Ḃ","Ć":"Ć","Ḉ":"Ḉ","Č":"Č","Ĉ":"Ĉ","Ċ":"Ċ","Ç":"Ç","Ď":"Ď","Ḋ":"Ḋ","Ḑ":"Ḑ","É":"É","È":"È","Ë":"Ë","Ẽ":"Ẽ","Ē":"Ē","Ḗ":"Ḗ","Ḕ":"Ḕ","Ĕ":"Ĕ","Ḝ":"Ḝ","Ě":"Ě","Ê":"Ê","Ế":"Ế","Ề":"Ề","Ễ":"Ễ","Ė":"Ė","Ȩ":"Ȩ","Ḟ":"Ḟ","Ǵ":"Ǵ","Ḡ":"Ḡ","Ğ":"Ğ","Ǧ":"Ǧ","Ĝ":"Ĝ","Ġ":"Ġ","Ģ":"Ģ","Ḧ":"Ḧ","Ȟ":"Ȟ","Ĥ":"Ĥ","Ḣ":"Ḣ","Ḩ":"Ḩ","Í":"Í","Ì":"Ì","Ï":"Ï","Ḯ":"Ḯ","Ĩ":"Ĩ","Ī":"Ī","Ĭ":"Ĭ","Ǐ":"Ǐ","Î":"Î","İ":"İ","Ĵ":"Ĵ","Ḱ":"Ḱ","Ǩ":"Ǩ","Ķ":"Ķ","Ĺ":"Ĺ","Ľ":"Ľ","Ļ":"Ļ","Ḿ":"Ḿ","Ṁ":"Ṁ","Ń":"Ń","Ǹ":"Ǹ","Ñ":"Ñ","Ň":"Ň","Ṅ":"Ṅ","Ņ":"Ņ","Ó":"Ó","Ò":"Ò","Ö":"Ö","Ȫ":"Ȫ","Õ":"Õ","Ṍ":"Ṍ","Ṏ":"Ṏ","Ȭ":"Ȭ","Ō":"Ō","Ṓ":"Ṓ","Ṑ":"Ṑ","Ŏ":"Ŏ","Ǒ":"Ǒ","Ô":"Ô","Ố":"Ố","Ồ":"Ồ","Ỗ":"Ỗ","Ȯ":"Ȯ","Ȱ":"Ȱ","Ő":"Ő","Ṕ":"Ṕ","Ṗ":"Ṗ","Ŕ":"Ŕ","Ř":"Ř","Ṙ":"Ṙ","Ŗ":"Ŗ","Ś":"Ś","Ṥ":"Ṥ","Š":"Š","Ṧ":"Ṧ","Ŝ":"Ŝ","Ṡ":"Ṡ","Ş":"Ş","Ť":"Ť","Ṫ":"Ṫ","Ţ":"Ţ","Ú":"Ú","Ù":"Ù","Ü":"Ü","Ǘ":"Ǘ","Ǜ":"Ǜ","Ǖ":"Ǖ","Ǚ":"Ǚ","Ũ":"Ũ","Ṹ":"Ṹ","Ū":"Ū","Ṻ":"Ṻ","Ŭ":"Ŭ","Ǔ":"Ǔ","Û":"Û","Ů":"Ů","Ű":"Ű","Ṽ":"Ṽ","Ẃ":"Ẃ","Ẁ":"Ẁ","Ẅ":"Ẅ","Ŵ":"Ŵ","Ẇ":"Ẇ","Ẍ":"Ẍ","Ẋ":"Ẋ","Ý":"Ý","Ỳ":"Ỳ","Ÿ":"Ÿ","Ỹ":"Ỹ","Ȳ":"Ȳ","Ŷ":"Ŷ","Ẏ":"Ẏ","Ź":"Ź","Ž":"Ž","Ẑ":"Ẑ","Ż":"Ż","ά":"ά","ὰ":"ὰ","ᾱ":"ᾱ","ᾰ":"ᾰ","έ":"έ","ὲ":"ὲ","ή":"ή","ὴ":"ὴ","ί":"ί","ὶ":"ὶ","ϊ":"ϊ","ΐ":"ΐ","ῒ":"ῒ","ῑ":"ῑ","ῐ":"ῐ","ό":"ό","ὸ":"ὸ","ύ":"ύ","ὺ":"ὺ","ϋ":"ϋ","ΰ":"ΰ","ῢ":"ῢ","ῡ":"ῡ","ῠ":"ῠ","ώ":"ώ","ὼ":"ὼ","Ύ":"Ύ","Ὺ":"Ὺ","Ϋ":"Ϋ","Ῡ":"Ῡ","Ῠ":"Ῠ","Ώ":"Ώ","Ὼ":"Ὼ"};class Wa{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new Fa(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(void 0===t&&(t=!0),this.fetch().text!==e)throw new n("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return null==this.nextToken&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new a("}")),this.gullet.pushTokens(e);var r=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,r}parseExpression(e,t){for(var r=[];;){"math"===this.mode&&this.consumeSpaces();var a=this.fetch();if(-1!==Wa.endOfExpression.indexOf(a.text))break;if(t&&a.text===t)break;if(e&&za[a.text]&&za[a.text].infix)break;var n=this.parseAtom(t);if(!n)break;"internal"!==n.type&&r.push(n)}return"text"===this.mode&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){for(var t,r=-1,a=0;a<e.length;a++)if("infix"===e[a].type){if(-1!==r)throw new n("only one infix operator per group",e[a].token);r=a,t=e[a].replaceWith}if(-1!==r&&t){var i,o,s=e.slice(0,r),l=e.slice(r+1);return i=1===s.length&&"ordgroup"===s[0].type?s[0]:{type:"ordgroup",mode:this.mode,body:s},o=1===l.length&&"ordgroup"===l[0].type?l[0]:{type:"ordgroup",mode:this.mode,body:l},["\\\\abovefrac"===t?this.callFunction(t,[i,e[r],o],[]):this.callFunction(t,[i,o],[])]}return e}handleSupSubscript(e){var t=this.fetch(),r=t.text;this.consume(),this.consumeSpaces();var a=this.parseGroup(e);if(!a)throw new n("Expected group after '"+r+"'",t);return a}formatUnsupportedCmd(e){for(var t=[],r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});var a={type:"text",mode:this.mode,body:t};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[a]}}parseAtom(e){var t,r,a=this.parseGroup("atom",e);if("text"===this.mode)return a;for(;;){this.consumeSpaces();var i=this.fetch();if("\\limits"===i.text||"\\nolimits"===i.text){if(a&&"op"===a.type){var o="\\limits"===i.text;a.limits=o,a.alwaysHandleSupSub=!0}else{if(!a||"operatorname"!==a.type)throw new n("Limit controls must follow a math operator",i);a.alwaysHandleSupSub&&(a.limits="\\limits"===i.text)}this.consume()}else if("^"===i.text){if(t)throw new n("Double superscript",i);t=this.handleSupSubscript("superscript")}else if("_"===i.text){if(r)throw new n("Double subscript",i);r=this.handleSupSubscript("subscript")}else if("'"===i.text){if(t)throw new n("Double superscript",i);var s={type:"textord",mode:this.mode,text:"\\prime"},l=[s];for(this.consume();"'"===this.fetch().text;)l.push(s),this.consume();"^"===this.fetch().text&&l.push(this.handleSupSubscript("superscript")),t={type:"ordgroup",mode:this.mode,body:l}}else{if(!Ua[i.text])break;var h=Ua[i.text],m=Ga.test(i.text);for(this.consume();;){var c=this.fetch().text;if(!Ua[c])break;if(Ga.test(c)!==m)break;this.consume(),h+=Ua[c]}var p=new Wa(h,this.settings).parse();m?r={type:"ordgroup",mode:"math",body:p}:t={type:"ordgroup",mode:"math",body:p}}}return t||r?{type:"supsub",mode:this.mode,base:a,sup:t,sub:r}:a}parseFunction(e,t){var r=this.fetch(),a=r.text,i=za[a];if(!i)return null;if(this.consume(),t&&"atom"!==t&&!i.allowedInArgument)throw new n("Got function '"+a+"' with no arguments"+(t?" as "+t:""),r);if("text"===this.mode&&!i.allowedInText)throw new n("Can't use function '"+a+"' in text mode",r);if("math"===this.mode&&!1===i.allowedInMath)throw new n("Can't use function '"+a+"' in math mode",r);var{args:o,optArgs:s}=this.parseArguments(a,i);return this.callFunction(a,o,s,r,e)}callFunction(e,t,r,a,i){var o={funcName:e,parser:this,token:a,breakOnTokenText:i},s=za[e];if(s&&s.handler)return s.handler(o,t,r);throw new n("No function handler for "+e)}parseArguments(e,t){var r=t.numArgs+t.numOptionalArgs;if(0===r)return{args:[],optArgs:[]};for(var a=[],i=[],o=0;o<r;o++){var s=t.argTypes&&t.argTypes[o],l=o<t.numOptionalArgs;(t.primitive&&null==s||"sqrt"===t.type&&1===o&&null==i[0])&&(s="primitive");var h=this.parseGroupOfType("argument to '"+e+"'",s,l);if(l)i.push(h);else{if(null==h)throw new n("Null argument, please report this as a bug");a.push(h)}}return{args:a,optArgs:i}}parseGroupOfType(e,t,r){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,t);case"hbox":var a=this.parseArgumentGroup(r,"text");return null!=a?{type:"styling",mode:a.mode,body:[a],style:"text"}:null;case"raw":var i=this.parseStringGroup("raw",r);return null!=i?{type:"raw",mode:"text",string:i.text}:null;case"primitive":if(r)throw new n("A primitive argument cannot be optional");var o=this.parseGroup(e);if(null==o)throw new n("Expected group as "+e,this.fetch());return o;case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new n("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;" "===this.fetch().text;)this.consume()}parseStringGroup(e,t){var r=this.gullet.scanArgument(t);if(null==r)return null;for(var a,n="";"EOF"!==(a=this.fetch()).text;)n+=a.text,this.consume();return this.consume(),r.text=n,r}parseRegexGroup(e,t){for(var r,a=this.fetch(),i=a,o="";"EOF"!==(r=this.fetch()).text&&e.test(o+r.text);)o+=(i=r).text,this.consume();if(""===o)throw new n("Invalid "+t+": '"+a.text+"'",a);return a.range(i,o)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(null==t)return null;var r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new n("Invalid color: '"+t.text+"'",t);var a=r[0];return/^[0-9a-f]{6}$/i.test(a)&&(a="#"+a),{type:"color-token",mode:this.mode,color:a}}parseSizeGroup(e){var t,r=!1;if(this.gullet.consumeSpaces(),!(t=e||"{"===this.gullet.future().text?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||0!==t.text.length||(t.text="0pt",r=!0);var a=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!a)throw new n("Invalid size: '"+t.text+"'",t);var i={number:+(a[1]+a[2]),unit:a[3]};if(!P(i))throw new n("Invalid unit: '"+i.unit+"'",t);return{type:"size",mode:this.mode,value:i,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),null==t)return null;var r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){var r=this.gullet.scanArgument(e);if(null==r)return null;var a=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var n=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var i={type:"ordgroup",mode:this.mode,loc:r.loc,body:n};return t&&this.switchMode(a),i}parseGroup(e,t){var a,i=this.fetch(),o=i.text;if("{"===o||"\\begingroup"===o){this.consume();var s="{"===o?"}":"\\endgroup";this.gullet.beginGroup();var l=this.parseExpression(!1,s),h=this.fetch();this.expect(s),this.gullet.endGroup(),a={type:"ordgroup",mode:this.mode,loc:r.range(i,h),body:l,semisimple:"\\begingroup"===o||void 0}}else if(null==(a=this.parseFunction(t,e)||this.parseSymbol())&&"\\"===o[0]&&!Va.hasOwnProperty(o)){if(this.settings.throwOnError)throw new n("Undefined control sequence: "+o,i);a=this.formatUnsupportedCmd(o),this.consume()}return a}formLigatures(e){for(var t=e.length-1,a=0;a<t;++a){var n=e[a],i=n.text;"-"===i&&"-"===e[a+1].text&&(a+1<t&&"-"===e[a+2].text?(e.splice(a,3,{type:"textord",mode:"text",loc:r.range(n,e[a+2]),text:"---"}),t-=2):(e.splice(a,2,{type:"textord",mode:"text",loc:r.range(n,e[a+1]),text:"--"}),t-=1)),"'"!==i&&"`"!==i||e[a+1].text!==i||(e.splice(a,2,{type:"textord",mode:"text",loc:r.range(n,e[a+1]),text:i+i}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var a=t.slice(5),i="*"===a.charAt(0);if(i&&(a=a.slice(1)),a.length<2||a.charAt(0)!==a.slice(-1))throw new n("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:a=a.slice(1,-1),star:i}}Xa.hasOwnProperty(t[0])&&!ae[this.mode][t[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=Xa[t[0]]+t.slice(1));var o,s=Ca.exec(t);if(s&&("i"===(t=t.substring(0,s.index))?t="ı":"j"===t&&(t="ȷ")),ae[this.mode][t]){this.settings.strict&&"math"===this.mode&&Oe.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var l,h=ae[this.mode][t].group,m=r.range(e);if(te.hasOwnProperty(h)){var c=h;l={type:"atom",mode:this.mode,family:c,loc:m,text:t}}else l={type:h,mode:this.mode,loc:m,text:t};o=l}else{if(!(t.charCodeAt(0)>=128))return null;this.settings.strict&&(M(t.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'" ('+t.charCodeAt(0)+")",e)),o={type:"textord",mode:"text",loc:r.range(e),text:t}}if(this.consume(),s)for(var p=0;p<s[0].length;p++){var u=s[0][p];if(!Ya[u])throw new n("Unknown accent ' "+u+"'",e);var d=Ya[u][this.mode]||Ya[u].text;if(!d)throw new n("Accent "+u+" unsupported in "+this.mode+" mode",e);o={type:"accent",mode:this.mode,loc:r.range(e),label:d,isStretchy:!1,isShifty:!0,base:o}}return o}}Wa.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var _a=function(e,t){if(!("string"==typeof e||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var r=new Wa(e,t);delete r.gullet.macros.current["\\df@tag"];var i=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new n("\\tag works only in display equations");i=[{type:"tag",mode:"text",body:i,tag:r.subparse([new a("\\df@tag")])}]}return i},ja=function(e,t,r){t.textContent="";var a=Za(e,r).toNode();t.appendChild(a)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&("undefined"!=typeof console&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),ja=function(){throw new n("KaTeX doesn't work in quirks mode.")});var $a=function(e,t,r){if(r.throwOnError||!(e instanceof n))throw e;var a=Ze.makeSpan(["katex-error"],[new Z(t)]);return a.setAttribute("title",e.toString()),a.setAttribute("style","color:"+r.errorColor),a},Za=function(e,t){var r=new p(t);try{return function(e,t,r){var a,n=Ht(r);if("mathml"===r.output)return Rt(e,t,n,r.displayMode,!0);if("html"===r.output){var i=kt(e,n);a=Ze.makeSpan(["katex"],[i])}else{var o=Rt(e,t,n,r.displayMode,!1),s=kt(e,n);a=Ze.makeSpan(["katex"],[o,s])}return Ot(a,r)}(_a(e,r),e,r)}catch(t){return $a(t,e,r)}},Ka={version:"0.16.2",render:ja,renderToString:function(e,t){return Za(e,t).toMarkup()},ParseError:n,SETTINGS_SCHEMA:m,__parse:function(e,t){var r=new p(t);return _a(e,r)},__renderToDomTree:Za,__renderToHTMLTree:function(e,t){var r=new p(t);try{return function(e,t,r){var a=kt(e,Ht(r)),n=Ze.makeSpan(["katex"],[a]);return Ot(n,r)}(_a(e,r),0,r)}catch(t){return $a(t,e,r)}},__setFontMetrics:function(e,t){B[e]=t},__defineSymbol:ne,__defineMacro:Dr,__domTree:{Span:W,Anchor:_,SymbolNode:Z,SvgNode:K,PathNode:J,LineNode:Q}},Ja=window;for(var ke in t)Ja[ke]=t[ke];t.__esModule&&Object.defineProperty(Ja,"__esModule",{value:!0})}();