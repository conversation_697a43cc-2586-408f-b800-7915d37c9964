{"version": 3, "sources": ["../../../build/scss/parts/adminlte.pages.scss", "../../../node_modules/bootstrap/scss/_close.scss", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../../build/scss/pages/_mailbox.scss", "../../../node_modules/bootstrap/scss/mixins/_lists.scss", "dist/css/alt/adminlte.pages.css", "../../../build/scss/pages/_lockscreen.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../../build/scss/pages/_login_and_register.scss", "../../../build/scss/pages/_404_500_errors.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../../build/scss/pages/_invoice.scss", "../../../build/scss/pages/_profile.scss", "../../../build/scss/pages/_e-commerce.scss", "../../../node_modules/bootstrap/scss/mixins/_image.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../build/scss/pages/_projects.scss", "../../../build/scss/pages/_iframe.scss", "../../../build/scss/pages/_kanban.scss"], "names": [], "mappings": "AAAA;;;;;;ACAA,OAAA,0BACE,MAAA,MC8HI,UAAA,OD5HJ,YAAA,IACA,YAAA,EACA,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KACA,QAAA,GEKA,aAAA,gCFDE,MAAA,KACA,gBAAA,KEIF,2CAAA,2CAAA,8DAAA,8DFCI,QAAA,IAWN,aAAA,gCACE,QAAA,EACA,iBAAA,YACA,OAAA,EAMF,iBAAA,oCACE,eAAA,KGlCF,yBAEI,OAAA,EAIJ,kBACE,QAAA,IADF,8BAII,cAAA,IAAA,MAAA,iBAIJ,mBACE,cAAA,IAAA,MAAA,iBACA,QAAA,KAFF,sBAKI,UAAA,KACA,OAAA,EANJ,sBAUI,OAAA,EACA,QAAA,IAAA,EAAA,EAIJ,mBACE,MAAA,KACA,UAAA,KAGF,sBACE,QAAA,KAGF,qBCtCE,aAAA,EACA,WAAA,KDqCF,wBAGI,OAAA,IAAA,MAAA,KACA,MAAA,KACA,cAAA,KACA,aAAA,KACA,MAAA,MAIJ,yBACE,MAAA,KACA,YAAA,IAGF,yBEiCA,yBACA,yBF/BE,QAAA,MAGF,yBACE,iBAAA,QACA,QAAA,KAGF,yBACE,MAAA,KACA,UAAA,KAFF,8BAKI,QAAA,aACA,YAAA,OAIJ,yBACE,MAAA,KACA,UAAA,KACA,WAAA,QACA,QAAA,KAAA,KACA,WAAA,OALF,iCAQI,QAAA,EARJ,qCAWM,OAAA,KACA,UAAA,KGtFN,YACE,iBAAA,QADF,6BAKI,YAAA,IACA,WAAA,OAIJ,iBACE,UAAA,KACA,YAAA,IACA,cAAA,KACA,WAAA,OAJF,mBAOI,MAAA,QAIJ,oBACE,OAAA,EAAA,KACA,WAAA,IACA,UAAA,MAKF,iBChBI,cAAA,IDkBF,iBAAA,KACA,OAAA,KAAA,KAAA,KACA,QAAA,EACA,SAAA,SACA,MAAA,MAIF,kBC1BI,cAAA,ID4BF,iBAAA,KACA,KAAA,MACA,QAAA,IACA,SAAA,SACA,IAAA,MACA,QAAA,GAPF,sBC1BI,cAAA,IDqCA,OAAA,KACA,MAAA,KAKJ,wBACE,YAAA,KADF,sCAII,OAAA,EAJJ,6BAQI,iBAAA,KACA,OAAA,EACA,QAAA,EAAA,KAIJ,mBACE,WAAA,KAGF,4BAEI,iBAAA,QAFJ,8BAKI,MAAA,KALJ,wCAQI,iBAAA,QARJ,6BAWI,iBAAA,QEtFJ,YHkNA,eGhNE,UAAA,OACA,YAAA,IACA,cAAA,MACA,WAAA,OALF,cH0NA,iBGlNI,MAAA,QAIJ,YHmNA,eGjNE,eAAA,OAAA,YAAA,OACA,iBAAA,QACA,QAAA,YAAA,QAAA,KACA,mBAAA,OAAA,eAAA,OACA,OAAA,MACA,cAAA,OAAA,gBAAA,OAGF,WHuNA,cGrNE,MAAA,MAEA,yBAJF,WH6NE,cGxNE,WAAA,MACA,MAAA,KANJ,iBHoOA,oBG1NI,cAAA,EAIJ,iBH2NA,oBGzNE,iBAAA,KACA,WAAA,EACA,MAAA,KACA,QAAA,KALF,4CHmOA,+CG1NM,aAAA,EATN,kDHwOA,qDG5NQ,WAAA,KHiOR,wFG7OA,yFH+OA,2FADA,4FG9NU,aAAA,QAhBV,2DHoPA,8DG9NU,WAAA,KHmOV,2FGzPA,4FH2PA,8FADA,+FG/NU,aAAA,QA3BV,6DHgQA,gEG/NU,WAAA,KAjCV,6FHqQA,gGGhOU,aAAA,QArCV,gDH0QA,mDG/NM,iBAAA,YACA,2BAAA,OACA,YAAA,EACA,wBAAA,OACA,MAAA,KACA,WAAA,aAAA,KAAA,WAAA,CAAA,WAAA,KAAA,YAKN,eH+NA,kBG7NE,OAAA,EACA,QAAA,EAAA,KAAA,KACA,WAAA,OAGF,mBACE,OAAA,KAAA,EAGF,4BH+NA,+BG5NI,iBAAA,QACA,aAAA,QACA,MAAA,KALJ,yBHsOA,4BG5NM,MAAA,KC9GN,YACE,OAAA,KAAA,KAAA,EACA,MAAA,MCmEE,4BDrEJ,YAKI,MAAA,MALJ,sBAUI,MAAA,KACA,UAAA,MACA,YAAA,ICyDA,4BDrEJ,sBAeM,MAAA,KACA,WAAA,QAhBN,2BAsBI,QAAA,MACA,YAAA,MC8CA,4BDrEJ,2BA0BM,YAAA,GA1BN,8BA8BM,UAAA,KACA,YAAA,ICsCF,4BDrEJ,8BAkCQ,WAAA,QElCR,SACE,iBAAA,KACA,OAAA,IAAA,MAAA,iBACA,SAAA,SAGF,eACE,WAAA,EAGF,oBAEI,iBAAA,QCZJ,kBACE,OAAA,IAAA,MAAA,QACA,OAAA,EAAA,KACA,QAAA,IACA,MAAA,MAGF,kBACE,UAAA,KACA,WAAA,IAGF,MACE,cAAA,IAAA,MAAA,QACA,MAAA,KACA,cAAA,KACA,eAAA,KAJF,mBAOI,cAAA,EACA,cAAA,EACA,eAAA,EATJ,kBAaI,cAAA,KACA,MAAA,KAdJ,WAkBI,MAAA,KAIJ,iBAEI,MAAA,KACA,aAAA,QCpCJ,eCME,UAAA,KAGA,OAAA,KDPA,MAAA,KAIF,sBACE,eAAA,QAAA,YAAA,QACA,QAAA,YAAA,QAAA,KACA,WAAA,KAIF,qBEFM,WAAA,EAAA,IAAA,IAAA,iBREF,cAAA,OMIF,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,QAAA,YAAA,QAAA,KACA,aAAA,KACA,UAAA,KACA,QAAA,MATF,yBCPE,UAAA,KAGA,OAAA,KDiBE,oBAAA,OAAA,WAAA,OAbJ,2BAiBI,QAAA,GAKJ,iBAEI,aAAA,MGtCJ,aAEI,eAAA,OAFJ,uBAMI,cAAA,EX6dJ,4BWneA,2BAYI,cAAA,IACA,QAAA,OACA,MAAA,OAdJ,yBAmBI,WAAA,OCvBJ,+BAEI,QAAA,KAFJ,kCAKI,YAAA,YACA,WAAA,YACA,eAAA,YZqfJ,8BY5fA,8BAWI,QAAA,KAIJ,4BACE,SAAA,OAGF,iBACE,OAAA,KADF,yCAKM,WAAA,KACA,MAAA,KANN,mDASQ,YAAA,OATR,0CAaM,SAAA,SAbN,wCAgBM,MAAA,KACA,QAAA,YAAA,QAAA,KACA,cAAA,OAAA,gBAAA,OACA,eAAA,OAAA,YAAA,OAnBN,0CAsBM,SAAA,SACA,IAAA,EACA,KAAA,EACA,MAAA,KACA,QAAA,KACA,iBAAA,QA3BN,8CA8BQ,QAAA,YAAA,QAAA,KACA,cAAA,OAAA,gBAAA,OACA,eAAA,OAAA,YAAA,OACA,MAAA,KACA,OAAA,KAlCR,oCAuCM,OAAA,EACA,MAAA,KACA,OAAA,KAzCN,qDA4CQ,eAAA,YAIJ,yDACE,SAAA,SACA,KAAA,EACA,IAAA,EACA,MAAA,EACA,OAAA,EACA,YAAA,YACA,OAAA,KACA,WAAA,KACA,QAAA,KC5EN,wBACE,OAAA,IADF,iCAII,OAAA,KACA,WAAA,KACA,WAAA,OANJ,4CbolBA,kDa1kBM,MAAA,oBAAA,MAAA,iBAAA,MAAA,YACA,QAAA,YAAA,QAAA,KACA,eAAA,QAAA,YAAA,QAZN,iDAgBI,OAAA,2CAhBJ,yCAqBM,QAAA,MArBN,uCAyBM,MAAA,MACA,QAAA,aACA,OAAA,EAAA,MA3BN,mDA8BQ,YAAA,EA9BR,kDAkCQ,OAAA,KACA,WAAA,KAnCR,0DAwCU,QAAA,MAAA,OAxCV,wDA2CU,QAAA,OA3CV,2CAkDM,gBAAA,UACA,aAAA,EACA,cAAA", "sourcesContent": ["/*!\n *   AdminLTE v3.1.0-rc\n *     Only Pages\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n@import \"~bootstrap/scss/close\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../mixins\";\n\n@import \"pages\";\n", ".close {\n  float: right;\n  @include font-size($close-font-size);\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  opacity: .5;\n\n  // Override <a>'s hover style\n  @include hover() {\n    color: $close-color;\n    text-decoration: none;\n  }\n\n  &:not(:disabled):not(.disabled) {\n    @include hover-focus() {\n      opacity: .75;\n    }\n  }\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n// stylelint-disable-next-line selector-no-qualifying-type\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\n// Future-proof disabling of clicks on `<a>` elements\n\n// stylelint-disable-next-line selector-no-qualifying-type\na.close.disabled {\n  pointer-events: none;\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "//\n// Pages: Mailbox\n//\n\n.mailbox-messages {\n  > .table {\n    margin: 0;\n  }\n}\n\n.mailbox-controls {\n  padding: 5px;\n\n  &.with-border {\n    border-bottom: 1px solid $card-border-color;\n  }\n}\n\n.mailbox-read-info {\n  border-bottom: 1px solid $card-border-color;\n  padding: 10px;\n\n  h3 {\n    font-size: 20px;\n    margin: 0;\n  }\n\n  h5 {\n    margin: 0;\n    padding: 5px 0 0;\n  }\n}\n\n.mailbox-read-time {\n  color: #999;\n  font-size: 13px;\n}\n\n.mailbox-read-message {\n  padding: 10px;\n}\n\n.mailbox-attachments {\n  @include list-unstyled ();\n  li {\n    border: 1px solid #eee;\n    float: left;\n    margin-bottom: 10px;\n    margin-right: 10px;\n    width: 200px;\n  }\n}\n\n.mailbox-attachment-name {\n  color: #666;\n  font-weight: 700;\n}\n\n.mailbox-attachment-icon,\n.mailbox-attachment-info,\n.mailbox-attachment-size {\n  display: block;\n}\n\n.mailbox-attachment-info {\n  background-color: $gray-100;\n  padding: 10px;\n}\n\n.mailbox-attachment-size {\n  color: #999;\n  font-size: 12px;\n\n  > span {\n    display: inline-block;\n    padding-top: .75rem;\n  }\n}\n\n.mailbox-attachment-icon {\n  color: #666;\n  font-size: 65px;\n  max-height: 132.5px;\n  padding: 20px 10px;\n  text-align: center;\n\n  &.has-img {\n    padding: 0;\n\n    > img {\n      height: auto;\n      max-width: 100%;\n    }\n  }\n}\n\n.mailbox-attachment-close {\n  @extend .close;\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "/*!\n *   AdminLTE v3.1.0-rc\n *     Only Pages\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n.close, .mailbox-attachment-close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: .5;\n}\n\n.close:hover, .mailbox-attachment-close:hover {\n  color: #000;\n  text-decoration: none;\n}\n\n.close:not(:disabled):not(.disabled):hover, .mailbox-attachment-close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus, .mailbox-attachment-close:not(:disabled):not(.disabled):focus {\n  opacity: .75;\n}\n\nbutton.close, button.mailbox-attachment-close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\na.close.disabled, a.disabled.mailbox-attachment-close {\n  pointer-events: none;\n}\n\n.mailbox-messages > .table {\n  margin: 0;\n}\n\n.mailbox-controls {\n  padding: 5px;\n}\n\n.mailbox-controls.with-border {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.mailbox-read-info {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  padding: 10px;\n}\n\n.mailbox-read-info h3 {\n  font-size: 20px;\n  margin: 0;\n}\n\n.mailbox-read-info h5 {\n  margin: 0;\n  padding: 5px 0 0;\n}\n\n.mailbox-read-time {\n  color: #999;\n  font-size: 13px;\n}\n\n.mailbox-read-message {\n  padding: 10px;\n}\n\n.mailbox-attachments {\n  padding-left: 0;\n  list-style: none;\n}\n\n.mailbox-attachments li {\n  border: 1px solid #eee;\n  float: left;\n  margin-bottom: 10px;\n  margin-right: 10px;\n  width: 200px;\n}\n\n.mailbox-attachment-name {\n  color: #666;\n  font-weight: 700;\n}\n\n.mailbox-attachment-icon,\n.mailbox-attachment-info,\n.mailbox-attachment-size {\n  display: block;\n}\n\n.mailbox-attachment-info {\n  background-color: #f8f9fa;\n  padding: 10px;\n}\n\n.mailbox-attachment-size {\n  color: #999;\n  font-size: 12px;\n}\n\n.mailbox-attachment-size > span {\n  display: inline-block;\n  padding-top: .75rem;\n}\n\n.mailbox-attachment-icon {\n  color: #666;\n  font-size: 65px;\n  max-height: 132.5px;\n  padding: 20px 10px;\n  text-align: center;\n}\n\n.mailbox-attachment-icon.has-img {\n  padding: 0;\n}\n\n.mailbox-attachment-icon.has-img > img {\n  height: auto;\n  max-width: 100%;\n}\n\n.lockscreen {\n  background-color: #e9ecef;\n}\n\n.lockscreen .lockscreen-name {\n  font-weight: 600;\n  text-align: center;\n}\n\n.lockscreen-logo {\n  font-size: 35px;\n  font-weight: 300;\n  margin-bottom: 25px;\n  text-align: center;\n}\n\n.lockscreen-logo a {\n  color: #495057;\n}\n\n.lockscreen-wrapper {\n  margin: 0 auto;\n  margin-top: 10%;\n  max-width: 400px;\n}\n\n.lockscreen-item {\n  border-radius: 4px;\n  background-color: #fff;\n  margin: 10px auto 30px;\n  padding: 0;\n  position: relative;\n  width: 290px;\n}\n\n.lockscreen-image {\n  border-radius: 50%;\n  background-color: #fff;\n  left: -10px;\n  padding: 5px;\n  position: absolute;\n  top: -25px;\n  z-index: 10;\n}\n\n.lockscreen-image > img {\n  border-radius: 50%;\n  height: 70px;\n  width: 70px;\n}\n\n.lockscreen-credentials {\n  margin-left: 70px;\n}\n\n.lockscreen-credentials .form-control {\n  border: 0;\n}\n\n.lockscreen-credentials .btn {\n  background-color: #fff;\n  border: 0;\n  padding: 0 10px;\n}\n\n.lockscreen-footer {\n  margin-top: 10px;\n}\n\n.dark-mode .lockscreen-item {\n  background-color: #343a40;\n}\n\n.dark-mode .lockscreen-logo a {\n  color: #fff;\n}\n\n.dark-mode .lockscreen-credentials .btn {\n  background-color: #343a40;\n}\n\n.dark-mode .lockscreen-image {\n  background-color: #6c757d;\n}\n\n.login-logo,\n.register-logo {\n  font-size: 2.1rem;\n  font-weight: 300;\n  margin-bottom: .9rem;\n  text-align: center;\n}\n\n.login-logo a,\n.register-logo a {\n  color: #495057;\n}\n\n.login-page,\n.register-page {\n  -ms-flex-align: center;\n  align-items: center;\n  background-color: #e9ecef;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  height: 100vh;\n  -ms-flex-pack: center;\n  justify-content: center;\n}\n\n.login-box,\n.register-box {\n  width: 360px;\n}\n\n@media (max-width: 576px) {\n  .login-box,\n  .register-box {\n    margin-top: .5rem;\n    width: 90%;\n  }\n}\n\n.login-box .card,\n.register-box .card {\n  margin-bottom: 0;\n}\n\n.login-card-body,\n.register-card-body {\n  background-color: #fff;\n  border-top: 0;\n  color: #666;\n  padding: 20px;\n}\n\n.login-card-body .input-group .form-control,\n.register-card-body .input-group .form-control {\n  border-right: 0;\n}\n\n.login-card-body .input-group .form-control:focus,\n.register-card-body .input-group .form-control:focus {\n  box-shadow: none;\n}\n\n.login-card-body .input-group .form-control:focus ~ .input-group-prepend .input-group-text,\n.login-card-body .input-group .form-control:focus ~ .input-group-append .input-group-text,\n.register-card-body .input-group .form-control:focus ~ .input-group-prepend .input-group-text,\n.register-card-body .input-group .form-control:focus ~ .input-group-append .input-group-text {\n  border-color: #80bdff;\n}\n\n.login-card-body .input-group .form-control.is-valid:focus,\n.register-card-body .input-group .form-control.is-valid:focus {\n  box-shadow: none;\n}\n\n.login-card-body .input-group .form-control.is-valid ~ .input-group-prepend .input-group-text,\n.login-card-body .input-group .form-control.is-valid ~ .input-group-append .input-group-text,\n.register-card-body .input-group .form-control.is-valid ~ .input-group-prepend .input-group-text,\n.register-card-body .input-group .form-control.is-valid ~ .input-group-append .input-group-text {\n  border-color: #28a745;\n}\n\n.login-card-body .input-group .form-control.is-invalid:focus,\n.register-card-body .input-group .form-control.is-invalid:focus {\n  box-shadow: none;\n}\n\n.login-card-body .input-group .form-control.is-invalid ~ .input-group-append .input-group-text,\n.register-card-body .input-group .form-control.is-invalid ~ .input-group-append .input-group-text {\n  border-color: #dc3545;\n}\n\n.login-card-body .input-group .input-group-text,\n.register-card-body .input-group .input-group-text {\n  background-color: transparent;\n  border-bottom-right-radius: 0.25rem;\n  border-left: 0;\n  border-top-right-radius: 0.25rem;\n  color: #777;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.login-box-msg,\n.register-box-msg {\n  margin: 0;\n  padding: 0 20px 20px;\n  text-align: center;\n}\n\n.social-auth-links {\n  margin: 10px 0;\n}\n\n.dark-mode .login-card-body,\n.dark-mode .register-card-body {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .login-logo a,\n.dark-mode .register-logo a {\n  color: #fff;\n}\n\n.error-page {\n  margin: 20px auto 0;\n  width: 600px;\n}\n\n@media (max-width: 767.98px) {\n  .error-page {\n    width: 100%;\n  }\n}\n\n.error-page > .headline {\n  float: left;\n  font-size: 100px;\n  font-weight: 300;\n}\n\n@media (max-width: 767.98px) {\n  .error-page > .headline {\n    float: none;\n    text-align: center;\n  }\n}\n\n.error-page > .error-content {\n  display: block;\n  margin-left: 190px;\n}\n\n@media (max-width: 767.98px) {\n  .error-page > .error-content {\n    margin-left: 0;\n  }\n}\n\n.error-page > .error-content > h3 {\n  font-size: 25px;\n  font-weight: 300;\n}\n\n@media (max-width: 767.98px) {\n  .error-page > .error-content > h3 {\n    text-align: center;\n  }\n}\n\n.invoice {\n  background-color: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n  position: relative;\n}\n\n.invoice-title {\n  margin-top: 0;\n}\n\n.dark-mode .invoice {\n  background-color: #343a40;\n}\n\n.profile-user-img {\n  border: 3px solid #adb5bd;\n  margin: 0 auto;\n  padding: 3px;\n  width: 100px;\n}\n\n.profile-username {\n  font-size: 21px;\n  margin-top: 5px;\n}\n\n.post {\n  border-bottom: 1px solid #adb5bd;\n  color: #666;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n}\n\n.post:last-of-type {\n  border-bottom: 0;\n  margin-bottom: 0;\n  padding-bottom: 0;\n}\n\n.post .user-block {\n  margin-bottom: 15px;\n  width: 100%;\n}\n\n.post .row {\n  width: 100%;\n}\n\n.dark-mode .post {\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.product-image {\n  max-width: 100%;\n  height: auto;\n  width: 100%;\n}\n\n.product-image-thumbs {\n  -ms-flex-align: stretch;\n  align-items: stretch;\n  display: -ms-flexbox;\n  display: flex;\n  margin-top: 2rem;\n}\n\n.product-image-thumb {\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.075);\n  border-radius: 0.25rem;\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n  display: -ms-flexbox;\n  display: flex;\n  margin-right: 1rem;\n  max-width: 7rem;\n  padding: 0.5rem;\n}\n\n.product-image-thumb img {\n  max-width: 100%;\n  height: auto;\n  -ms-flex-item-align: center;\n  align-self: center;\n}\n\n.product-image-thumb:hover {\n  opacity: .5;\n}\n\n.product-share a {\n  margin-right: .5rem;\n}\n\n.projects td {\n  vertical-align: middle;\n}\n\n.projects .list-inline {\n  margin-bottom: 0;\n}\n\n.projects img.table-avatar,\n.projects .table-avatar img {\n  border-radius: 50%;\n  display: inline;\n  width: 2.5rem;\n}\n\n.projects .project-state {\n  text-align: center;\n}\n\nbody.iframe-mode .main-sidebar {\n  display: none;\n}\n\nbody.iframe-mode .content-wrapper {\n  margin-left: 0 !important;\n  margin-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\nbody.iframe-mode .main-header,\nbody.iframe-mode .main-footer {\n  display: none;\n}\n\nbody.iframe-mode-fullscreen {\n  overflow: hidden;\n}\n\n.content-wrapper {\n  height: 100%;\n}\n\n.content-wrapper.iframe-mode .navbar-nav {\n  overflow-y: auto;\n  width: 100%;\n}\n\n.content-wrapper.iframe-mode .navbar-nav .nav-link {\n  white-space: nowrap;\n}\n\n.content-wrapper.iframe-mode .tab-content {\n  position: relative;\n}\n\n.content-wrapper.iframe-mode .tab-empty {\n  width: 100%;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -ms-flex-align: center;\n  align-items: center;\n}\n\n.content-wrapper.iframe-mode .tab-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  display: none;\n  background-color: #f4f6f9;\n}\n\n.content-wrapper.iframe-mode .tab-loading > div {\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -ms-flex-align: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n}\n\n.content-wrapper.iframe-mode iframe {\n  border: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.content-wrapper.iframe-mode iframe .content-wrapper {\n  padding-bottom: 0 !important;\n}\n\nbody.iframe-mode-fullscreen .content-wrapper.iframe-mode {\n  position: absolute;\n  left: 0;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  margin-left: 0 !important;\n  height: 100%;\n  min-height: 100%;\n  z-index: 1048;\n}\n\n.content-wrapper.kanban {\n  height: 1px;\n}\n\n.content-wrapper.kanban .content {\n  height: 100%;\n  overflow-x: auto;\n  overflow-y: hidden;\n}\n\n.content-wrapper.kanban .content .container,\n.content-wrapper.kanban .content .container-fluid {\n  width: -webkit-max-content;\n  width: -moz-max-content;\n  width: max-content;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-align: stretch;\n  align-items: stretch;\n}\n\n.content-wrapper.kanban .content-header + .content {\n  height: calc(100% - ((2 * 15px) + (1.8rem * 1.2)));\n}\n\n.content-wrapper.kanban .card .card-body {\n  padding: .5rem;\n}\n\n.content-wrapper.kanban .card.card-row {\n  width: 340px;\n  display: inline-block;\n  margin: 0 .5rem;\n}\n\n.content-wrapper.kanban .card.card-row:first-child {\n  margin-left: 0;\n}\n\n.content-wrapper.kanban .card.card-row .card-body {\n  height: 100%;\n  overflow-y: auto;\n}\n\n.content-wrapper.kanban .card.card-row .card .card-header {\n  padding: .5rem .75rem;\n}\n\n.content-wrapper.kanban .card.card-row .card .card-body {\n  padding: .75rem;\n}\n\n.content-wrapper.kanban .btn-tool.btn-link {\n  text-decoration: underline;\n  padding-left: 0;\n  padding-right: 0;\n}\n/*# sourceMappingURL=adminlte.pages.css.map */", "//\n// Pages: Lock Screen\n//\n\n// ADD THIS CLASS TO THE <BODY> TAG\n.lockscreen {\n  background-color: $gray-200;\n\n  // User name [optional]\n  .lockscreen-name {\n    font-weight: 600;\n    text-align: center;\n  }\n}\n\n.lockscreen-logo {\n  font-size: 35px;\n  font-weight: 300;\n  margin-bottom: 25px;\n  text-align: center;\n\n  a {\n    color: $gray-700;\n  }\n}\n\n.lockscreen-wrapper {\n  margin: 0 auto;\n  margin-top: 10%;\n  max-width: 400px;\n}\n\n\n// Will contain the image and the sign in form\n.lockscreen-item {\n  @include border-radius(4px);\n  background-color: $white;\n  margin: 10px auto 30px;\n  padding: 0;\n  position: relative;\n  width: 290px;\n}\n\n// User image\n.lockscreen-image {\n  @include border-radius(50%);\n  background-color: $white;\n  left: -10px;\n  padding: 5px;\n  position: absolute;\n  top: -25px;\n  z-index: 10;\n\n  > img {\n    @include border-radius(50%);\n    height: 70px;\n    width: 70px;\n  }\n}\n\n// Contains the password input and the login button\n.lockscreen-credentials {\n  margin-left: 70px;\n\n  .form-control {\n    border: 0;\n  }\n\n  .btn {\n    background-color: $white;\n    border: 0;\n    padding: 0 10px;\n  }\n}\n\n.lockscreen-footer {\n  margin-top: 10px;\n}\n\n.dark-mode {\n  .lockscreen-item {\n    background-color: $dark;\n  }\n  .lockscreen-logo a {\n    color: $white;\n  }\n  .lockscreen-credentials .btn {\n    background-color: $dark;\n  }\n  .lockscreen-image {\n    background-color: $gray-600;\n  }\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "//\n// Pages: Login & Register\n//\n\n.login-logo,\n.register-logo {\n  font-size: 2.1rem;\n  font-weight: 300;\n  margin-bottom: .9rem;\n  text-align: center;\n\n  a {\n    color: $gray-700;\n  }\n}\n\n.login-page,\n.register-page {\n  align-items: center;\n  background-color: $gray-200;\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  justify-content: center;\n}\n\n.login-box,\n.register-box {\n  width: 360px;\n\n  @media (max-width: map-get($grid-breakpoints, sm)) {\n    margin-top: .5rem;\n    width: 90%;\n  }\n\n  .card {\n    margin-bottom: 0;\n  }\n}\n\n.login-card-body,\n.register-card-body {\n  background-color: $white;\n  border-top: 0;\n  color: #666;\n  padding: 20px;\n\n  .input-group {\n    .form-control {\n      border-right: 0;\n\n      &:focus {\n        box-shadow: none;\n\n        ~ .input-group-prepend .input-group-text,\n        ~ .input-group-append .input-group-text {\n          border-color: $input-focus-border-color;\n        }\n      }\n\n      &.is-valid {\n        &:focus {\n          box-shadow: none;\n        }\n\n        ~ .input-group-prepend .input-group-text,\n        ~ .input-group-append .input-group-text {\n          border-color: $success;\n        }\n      }\n\n      &.is-invalid {\n        &:focus {\n          box-shadow: none;\n        }\n\n        ~ .input-group-append .input-group-text {\n          border-color: $danger;\n        }\n      }\n    }\n\n    .input-group-text {\n      background-color: transparent;\n      border-bottom-right-radius: $border-radius;\n      border-left: 0;\n      border-top-right-radius: $border-radius;\n      color: #777;\n      transition: $input-transition;\n    }\n  }\n}\n\n.login-box-msg,\n.register-box-msg {\n  margin: 0;\n  padding: 0 20px 20px;\n  text-align: center;\n}\n\n.social-auth-links {\n  margin: 10px 0;\n}\n\n.dark-mode {\n  .login-card-body,\n  .register-card-body {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: $white;\n  }\n  .login-logo,\n  .register-logo {\n    a {\n      color: $white;\n    }\n  }\n}\n", "//\n// Pages: 400 and 500 error pages\n//\n\n.error-page {\n  margin: 20px auto 0;\n  width: 600px;\n\n  @include media-breakpoint-down(sm) {\n    width: 100%;\n  }\n\n  //For the error number e.g: 404\n  > .headline {\n    float: left;\n    font-size: 100px;\n    font-weight: 300;\n\n    @include media-breakpoint-down(sm) {\n      float: none;\n      text-align: center;\n    }\n  }\n\n  //For the message\n  > .error-content {\n    display: block;\n    margin-left: 190px;\n\n    @include media-breakpoint-down(sm) {\n      margin-left: 0;\n    }\n\n    > h3 {\n      font-size: 25px;\n      font-weight: 300;\n\n      @include media-breakpoint-down(sm) {\n        text-align: center;\n      }\n    }\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\n// Pages: Invoice\n//\n\n.invoice {\n  background-color: $white;\n  border: 1px solid $card-border-color;\n  position: relative;\n}\n\n.invoice-title {\n  margin-top: 0;\n}\n\n.dark-mode {\n  .invoice {\n    background-color: $dark;\n  }\n}\n", "//\n// Pages: Profile\n//\n\n.profile-user-img {\n  border: 3px solid $gray-500;\n  margin: 0 auto;\n  padding: 3px;\n  width: 100px;\n}\n\n.profile-username {\n  font-size: 21px;\n  margin-top: 5px;\n}\n\n.post {\n  border-bottom: 1px solid $gray-500;\n  color: #666;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n\n  &:last-of-type {\n    border-bottom: 0;\n    margin-bottom: 0;\n    padding-bottom: 0;\n  }\n\n  .user-block {\n    margin-bottom: 15px;\n    width: 100%;\n  }\n\n  .row {\n    width: 100%;\n  }\n}\n\n.dark-mode {\n  .post {\n    color: $white;\n    border-color: $gray-600;\n  }\n}\n", "//\n// Pages: E-commerce\n//\n\n// product image\n.product-image {\n  @include img-fluid ();\n  width: 100%;\n}\n\n// product image thumbnails list\n.product-image-thumbs {\n  align-items: stretch;\n  display: flex;\n  margin-top: 2rem;\n}\n\n// product image thumbnail\n.product-image-thumb {\n  @include box-shadow($thumbnail-box-shadow);\n  @include border-radius($thumbnail-border-radius);\n\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  display: flex;\n  margin-right: 1rem;\n  max-width: 6.5rem + ($thumbnail-padding * 2);\n  padding: $thumbnail-padding * 2;\n\n  img {\n    @include img-fluid ();\n    align-self: center;\n  }\n\n  &:hover {\n    opacity: .5;\n  }\n}\n\n// product share\n.product-share {\n  a {\n    margin-right: .5rem;\n  }\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid() {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size.\n\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url($file-1x);\n\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\n  // but doesn't convert dppx=>dpi.\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\n  // Compatibility info: https://caniuse.com/#feat=css-media-resolution\n  @media only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\n    only screen and (min-resolution: 2dppx) { // Standardized\n    background-image: url($file-2x);\n    background-size: $width-1x $height-1x;\n  }\n  @include deprecate(\"`img-retina()`\", \"v4.3.0\", \"v5\");\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Pages: Projects\n//\n\n.projects {\n  td {\n    vertical-align: middle;\n  }\n\n  .list-inline {\n    margin-bottom: 0;\n  }\n\n  // table avatar\n  img.table-avatar,\n  .table-avatar img {\n    border-radius: 50%;\n    display: inline;\n    width: 2.5rem;\n  }\n\n  // project state\n  .project-state {\n    text-align: center;\n  }\n}\n", "body.iframe-mode {\n  .main-sidebar {\n    display: none;\n  }\n  .content-wrapper {\n    margin-left: 0 !important;\n    margin-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .main-header,\n  .main-footer {\n    display: none;\n  }\n}\n\nbody.iframe-mode-fullscreen {\n  overflow: hidden;\n}\n\n.content-wrapper {\n  height: 100%;\n\n  &.iframe-mode {\n    .navbar-nav {\n      overflow-y: auto;\n      width: 100%;\n\n      .nav-link {\n        white-space: nowrap;\n      }\n    }\n    .tab-content {\n      position: relative;\n    }\n    .tab-empty {\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n    .tab-loading {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      display: none;\n      background-color: $main-bg;\n\n      > div {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 100%;\n        height: 100%;\n      }\n    }\n\n    iframe {\n      border: 0;\n      width: 100%;\n      height: 100%;\n\n      .content-wrapper {\n        padding-bottom: 0 !important;\n      }\n    }\n\n    body.iframe-mode-fullscreen & {\n      position: absolute;\n      left: 0;\n      top: 0;\n      right: 0;\n      bottom: 0;\n      margin-left: 0 !important;\n      height: 100%;\n      min-height: 100%;\n      z-index: $zindex-main-sidebar + 10;\n    }\n  }\n}\n", ".content-wrapper.kanban {\n  height: 1px;\n\n  .content {\n    height: 100%;\n    overflow-x: auto;\n    overflow-y: hidden;\n\n    .container,\n    .container-fluid {\n      width: max-content;\n      display: flex;\n      align-items: stretch;\n    }\n  }\n  .content-header + .content {\n    height: calc(100% - ((2 * 15px) + (1.8rem * #{$headings-line-height})));\n  }\n\n  .card {\n    .card-body {\n      padding: .5rem;\n    }\n\n    &.card-row {\n      width: 340px;\n      display: inline-block;\n      margin: 0 .5rem;\n\n      &:first-child {\n        margin-left: 0;\n      }\n\n      .card-body {\n        height: 100%;\n        overflow-y: auto;\n      }\n\n      .card {\n        .card-header {\n          padding: .5rem .75rem;\n        }\n        .card-body {\n          padding: .75rem;\n        }\n      }\n    }\n  }\n  .btn-tool {\n    &.btn-link {\n      text-decoration: underline;\n      padding-left: 0;\n      padding-right: 0;\n    }\n  }\n}\n"]}