{"version": 3, "sources": ["../../../build/scss/parts/adminlte.plugins.scss", "adminlte.plugins.css", "../../../build/scss/plugins/_fullcalendar.scss", "../../../build/scss/_bootstrap-variables.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../../build/scss/mixins/_miscellaneous.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../build/scss/_variables.scss", "../../../build/scss/plugins/_select2.scss", "../../../build/scss/plugins/_mixins.scss", "../../../build/scss/plugins/_bootstrap-slider.scss", "../../../build/scss/plugins/_icheck-bootstrap.scss", "../../../build/scss/plugins/_mapael.scss", "../../../node_modules/bootstrap/scss/mixins/_reset-text.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../build/scss/plugins/_jqvmap.scss", "../../../build/scss/plugins/_sweetalert2.scss", "../../../build/scss/plugins/_toastr.scss", "../../../build/scss/plugins/_pace.scss", "../../../build/scss/plugins/_bootstrap-switch.scss", "../../../build/scss/plugins/_miscellaneous.scss"], "names": [], "mappings": "AAAA;;;;;;ECME;ACDF;EACE,mBCMgB;EDLhB,sBAAsB;EACtB,yBAAyB;EACzB,kBAAkB;EAClB,cCQgB;AFLlB;;ACRA;EAUI,yBAAyB;ADE7B;;ACGA;EACE,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,iBAAiB;ADAnB;;ACGA;EACE,mBAAmB;ADArB;;ACGA;EACE,kBAAkB;ADApB;;ACIA;EACE,mBAAmB;ADDrB;;ACIA;EACE,SAAS;EACT,WAAW;ADDb;;ACIA;;EAEE,cAAc;EACd,eAAe;ADDjB;;ACIA;;EAEE,eAAe;ADDjB;;ACIA;;EAEE,SAAS;EACT,aAAa;ADDf;;AGeI;EFVF;IACE,0BAAsB;IAAtB,sBAAsB;EDDxB;ECAA;IAII,iBAAQ;IAAR,QAAQ;IACR,oBAAoB;EDDxB;ECJA;IASI,iBAAQ;IAAR,QAAQ;IACR,sBAAsB;EDF1B;ECRA;IAcI,iBAAQ;IAAR,QAAQ;EDHZ;AACF;;ACOA;EACE,eAAe;EACf,gBAAgB;EAChB,mBAAmB;ADJrB;;ACOA;EACE,gBAAgB;EAChB,SAAS;EACT,UAAU;ADJZ;;ACCA;EAMI,WAAW;EACX,eAAe;EACf,iBAAiB;EACjB,iBAAiB;ADHrB;;ACNA;;;;;;;;EAmBM,gCAAgC;ADFtC;;ACjBA;;;;;;;;EGrEE,wBAAyB;AJiG3B;;ACAA;EACE,0BAA0B;ADG5B;;ACAA;EIxGM,sEC4IgE;ELjCpE,sBC0FkC;EDzFlC,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;EAClB,iBAAiB;ADEnB;;ACTA;EIxGM,6CJkHmD;ADGzD;;AOvHA;EAEI,yBLDc;EKGd,2BLuTgC;EKtThC,2BLqZ0F;AF7R9F;;AO7HA;EAUM,qBAAoC;APuH1C;;AOjIA;EAeI,yBLdc;AFoIlB;;AOrIA;EAoBI,iBAAiB;EACjB,yBAAiB;EAAjB,sBAAiB;EAAjB,qBAAiB;EAAjB,iBAAiB;APqHrB;;AO1IA;EAyBI,eAAe;EAEf,YAAY;EACZ,gBAAgB;APoHpB;;AOhJA;EAgCI,kBAAkB;EAClB,kBAAkB;APoHtB;;AOrJA;EAqCI,YAAY;EACZ,UAAU;APoHd;;AO1JA;EA0CI,aAAa;APoHjB;;AO9JA;;EAgDM,yBL/CY;AFkKlB;;AOnKA;;EAmDQ,aAAa;EACb,yBLyVkE;AFpO1E;;AOzKA;EA2DM,aAAa;APkHnB;;AO7KA;EA+DM,gBAAgB;APkHtB;;AOjLA;EAqEM,cLlEY;AFkLlB;;AOrLA;EA2EM,yBL3EY;AFyLlB;;AOzLA;EA+EQ,cLAe;AF8GvB;;AO7LA;EAsFI,yBLhEa;EKiEb,WL1FW;AFqMf;;AOlMA;EA8FQ,yBAJwB;EAKxB,WLlGO;AF0Mf;;AOvMA;EAuGM,yBLtGY;EKuGZ,+BLkTwF;AF9M9F;;AO5MA;EA2GQ,qBLkSkE;AF7L1E;;AOhNA;EA+GQ,4BL2M6B;EK1M7B,wBAAoC;APqG5C;;AOrNA;EAmHU,WAAW;EACX,qBAAiC;APsG3C;;AO1NA;EAuHY,sBAAsB;APuGlC;;AO9NA;EA8HY,SAAS;EACT,eAAe;APoG3B;;AOnOA;EAqIQ,yBL/GS;EKgHT,qBAAkC;EAClC,WL1IO;EK2IP,eAAe;EACf,kBAAkB;APkG1B;;AO3OA;EA6IQ,+BAA+B;EAC/B,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;APkG1B;;AOlPA;EAmJU,WLtJK;AFyPf;;AO/FM;EAIM,eAAe;AP+F3B;;AOnGM;EASI,iBAAiB;AP8F3B;;AO9PA;;EAwKQ,qBLqOkE;AF1I1E;;AOnQA;EA4KQ,SAAS;AP2FjB;;AOvQA;EAkLI,mBAAmB;APyFvB;;AOtFE;EAEI,4BAA4B;EAC5B,yBAAyB;APwF/B;;AOpFE;EAEI,6BAA6B;EAC7B,0BAA0B;APsFhC;;AOhFA;EAEI,gBAAgB;APkFpB;;AO7EA;EAEI,mBLmBkD;AF4DtD;;AO3EA;;EAIM,6BLuM2F;AF3HjG;;AOhFA;;EAOQ,kBAAkB;AP8E1B;;AOrFA;;EAWQ,YAAY;AP+EpB;;AO1FA;;EAgBM,iCL2L2F;AF5GjG;;AO/FA;;EAmBQ,0BL6F4B;EK5F5B,mBAAqC;APiF7C;;AOrGA;;EAuBU,oBAAoC;APmF9C;;AO1GA;;EA4BY,eAAe;APmF3B;;AO1EA;EACE,aAAa;AP6Ef;;AQ7UE;EAKQ,qBAAkC;AR4U5C;;AQjVE;EAUM,qBAAkC;AR2U1C;;AQvUI;;;;;;EAOQ,yBAAsD;ARyUlE;;AQhVI;;EAaI,yBNGS;EMFT,WNvBO;AF+Vf;;AQtVI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFsWf;;AQ7VI;;EA6BQ,qBAAkC;ARqU9C;;AQlWI;;EAiCQ,yBNjBK;EMkBL,qBAAgC;EAChC,WN5CG;AFkXf;;AQzWI;;EAuCQ,+BNhDG;AFuXf;;AQ9WI;;EA0CU,WNnDC;AF4Xf;;AQnXI;;EAgDM,qBAAkC;ARwU5C;;AQtYE;EAKQ,qBAAkC;ARqY5C;;AQ1YE;EAUM,qBAAkC;ARoY1C;;AQhYI;;;;;;EAOQ,yBAAsD;ARkYlE;;AQzYI;;EAaI,yBNhBU;EMiBV,WNvBO;AFwZf;;AQ/YI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AF+Zf;;AQtZI;;EA6BQ,qBAAkC;AR8X9C;;AQ3ZI;;EAiCQ,yBNpCM;EMqCN,qBAAgC;EAChC,WN5CG;AF2af;;AQlaI;;EAuCQ,+BNhDG;AFgbf;;AQvaI;;EA0CU,WNnDC;AFqbf;;AQ5aI;;EAgDM,qBAAkC;ARiY5C;;AQ/bE;EAKQ,qBAAkC;AR8b5C;;AQncE;EAUM,qBAAkC;AR6b1C;;AQzbI;;;;;;EAOQ,yBAAsD;AR2blE;;AQlcI;;EAaI,yBNUS;EMTT,WNvBO;AFidf;;AQxcI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFwdf;;AQ/cI;;EA6BQ,qBAAkC;ARub9C;;AQpdI;;EAiCQ,yBNVK;EMWL,qBAAgC;EAChC,WN5CG;AFoef;;AQ3dI;;EAuCQ,+BNhDG;AFyef;;AQheI;;EA0CU,WNnDC;AF8ef;;AQreI;;EAgDM,qBAAkC;AR0b5C;;AQxfE;EAKQ,qBAAkC;ARuf5C;;AQ5fE;EAUM,qBAAkC;ARsf1C;;AQlfI;;;;;;EAOQ,yBAAsD;ARoflE;;AQ3fI;;EAaI,yBNYS;EMXT,WNvBO;AF0gBf;;AQjgBI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFihBf;;AQxgBI;;EA6BQ,qBAAkC;ARgf9C;;AQ7gBI;;EAiCQ,yBNRK;EMSL,qBAAgC;EAChC,WN5CG;AF6hBf;;AQphBI;;EAuCQ,+BNhDG;AFkiBf;;AQzhBI;;EA0CU,WNnDC;AFuiBf;;AQ9hBI;;EAgDM,qBAAkC;ARmf5C;;AQjjBE;EAKQ,qBAAkC;ARgjB5C;;AQrjBE;EAUM,qBAAkC;AR+iB1C;;AQ3iBI;;;;;;EAOQ,yBAAsD;AR6iBlE;;AQpjBI;;EAaI,yBNSS;EMRT,cN2De;AFifvB;;AQ1jBI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AFwfvB;;AQjkBI;;EA6BQ,qBAAkC;ARyiB9C;;AQtkBI;;EAiCQ,yBNXK;EMYL,qBAAgC;EAChC,cNsCW;AFogBvB;;AQ7kBI;;EAuCQ,4BNkCW;AFygBvB;;AQllBI;;EA0CU,cN+BS;AF8gBvB;;AQvlBI;;EAgDM,qBAAkC;AR4iB5C;;AQ1mBE;EAKQ,qBAAkC;ARymB5C;;AQ9mBE;EAUM,qBAAkC;ARwmB1C;;AQpmBI;;;;;;EAOQ,yBAAsD;ARsmBlE;;AQ7mBI;;EAaI,yBNOS;EMNT,WNvBO;AF4nBf;;AQnnBI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFmoBf;;AQ1nBI;;EA6BQ,qBAAkC;ARkmB9C;;AQ/nBI;;EAiCQ,yBNbK;EMcL,qBAAgC;EAChC,WN5CG;AF+oBf;;AQtoBI;;EAuCQ,+BNhDG;AFopBf;;AQ3oBI;;EA0CU,WNnDC;AFypBf;;AQhpBI;;EAgDM,qBAAkC;ARqmB5C;;AQnqBE;EAKQ,mBAAkC;ARkqB5C;;AQvqBE;EAUM,mBAAkC;ARiqB1C;;AQ7pBI;;;;;;EAOQ,uBAAsD;AR+pBlE;;AQtqBI;;EAaI,yBNrBU;EMsBV,cN2De;AFmmBvB;;AQ5qBI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AF0mBvB;;AQnrBI;;EA6BQ,mBAAkC;AR2pB9C;;AQxrBI;;EAiCQ,yBNzCM;EM0CN,qBAAgC;EAChC,cNsCW;AFsnBvB;;AQ/rBI;;EAuCQ,4BNkCW;AF2nBvB;;AQpsBI;;EA0CU,cN+BS;AFgoBvB;;AQzsBI;;EAgDM,mBAAkC;AR8pB5C;;AQ5tBE;EAKQ,qBAAkC;AR2tB5C;;AQhuBE;EAUM,qBAAkC;AR0tB1C;;AQttBI;;;;;;EAOQ,yBAAsD;ARwtBlE;;AQ/tBI;;EAaI,yBNdU;EMeV,WNvBO;AF8uBf;;AQruBI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFqvBf;;AQ5uBI;;EA6BQ,qBAAkC;ARotB9C;;AQjvBI;;EAiCQ,yBNlCM;EMmCN,qBAAgC;EAChC,WN5CG;AFiwBf;;AQxvBI;;EAuCQ,+BNhDG;AFswBf;;AQ7vBI;;EA0CU,WNnDC;AF2wBf;;AQlwBI;;EAgDM,qBAAkC;ARutB5C;;AQrxBE;EAKQ,qBAAkC;ARoxB5C;;AQzxBE;EAUM,qBAAkC;ARmxB1C;;AQ/wBI;;;;;;EAOQ,yBAAsD;ARixBlE;;AQxxBI;;EAaI,yBF1BW;EE2BX,WNvBO;AFuyBf;;AQ9xBI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AF8yBf;;AQryBI;;EA6BQ,qBAAkC;AR6wB9C;;AQ1yBI;;EAiCQ,yBF9CO;EE+CP,qBAAgC;EAChC,WN5CG;AF0zBf;;AQjzBI;;EAuCQ,+BNhDG;AF+zBf;;AQtzBI;;EA0CU,WNnDC;AFo0Bf;;AQ3zBI;;EAgDM,qBAAkC;ARgxB5C;;AQ90BE;EAKQ,qBAAkC;AR60B5C;;AQl1BE;EAUM,qBAAkC;AR40B1C;;AQx0BI;;;;;;EAOQ,yBAAsD;AR00BlE;;AQj1BI;;EAaI,yBFzBM;EE0BN,WNvBO;AFg2Bf;;AQv1BI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFu2Bf;;AQ91BI;;EA6BQ,qBAAkC;ARs0B9C;;AQn2BI;;EAiCQ,yBF7CE;EE8CF,qBAAgC;EAChC,WN5CG;AFm3Bf;;AQ12BI;;EAuCQ,+BNhDG;AFw3Bf;;AQ/2BI;;EA0CU,WNnDC;AF63Bf;;AQp3BI;;EAgDM,qBAAkC;ARy0B5C;;AQv4BE;EAKQ,qBAAkC;ARs4B5C;;AQ34BE;EAUM,qBAAkC;ARq4B1C;;AQj4BI;;;;;;EAOQ,yBAAsD;ARm4BlE;;AQ14BI;;EAaI,yBFvBO;EEwBP,WNvBO;AFy5Bf;;AQh5BI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFg6Bf;;AQv5BI;;EA6BQ,qBAAkC;AR+3B9C;;AQ55BI;;EAiCQ,yBF3CG;EE4CH,qBAAgC;EAChC,WN5CG;AF46Bf;;AQn6BI;;EAuCQ,+BNhDG;AFi7Bf;;AQx6BI;;EA0CU,WNnDC;AFs7Bf;;AQ76BI;;EAgDM,qBAAkC;ARk4B5C;;AQh8BE;EAKQ,qBAAkC;AR+7B5C;;AQp8BE;EAUM,qBAAkC;AR87B1C;;AQ17BI;;;;;;EAOQ,yBAAsD;AR47BlE;;AQn8BI;;EAaI,yBFtBM;EEuBN,cN2De;AFg4BvB;;AQz8BI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AFu4BvB;;AQh9BI;;EA6BQ,qBAAkC;ARw7B9C;;AQr9BI;;EAiCQ,yBF1CE;EE2CF,qBAAgC;EAChC,cNsCW;AFm5BvB;;AQ59BI;;EAuCQ,4BNkCW;AFw5BvB;;AQj+BI;;EA0CU,cN+BS;AF65BvB;;AQt+BI;;EAgDM,qBAAkC;AR27B5C;;AQz/BE;EAKQ,qBAAkC;ARw/B5C;;AQ7/BE;EAUM,qBAAkC;ARu/B1C;;AQn/BI;;;;;;EAOQ,yBAAsD;ARq/BlE;;AQ5/BI;;EAaI,yBFpBS;EEqBT,WNvBO;AF2gCf;;AQlgCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFkhCf;;AQzgCI;;EA6BQ,qBAAkC;ARi/B9C;;AQ9gCI;;EAiCQ,yBFxCK;EEyCL,qBAAgC;EAChC,WN5CG;AF8hCf;;AQrhCI;;EAuCQ,+BNhDG;AFmiCf;;AQ1hCI;;EA0CU,WNnDC;AFwiCf;;AQ/hCI;;EAgDM,qBAAkC;ARo/B5C;;AQljCE;EAKQ,qBAAkC;ARijC5C;;AQtjCE;EAUM,qBAAkC;ARgjC1C;;AQ5iCI;;;;;;EAOQ,yBAAsD;AR8iClE;;AQrjCI;;EAaI,yBFlBQ;EEmBR,WNvBO;AFokCf;;AQ3jCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AF2kCf;;AQlkCI;;EA6BQ,qBAAkC;AR0iC9C;;AQvkCI;;EAiCQ,yBFtCI;EEuCJ,qBAAgC;EAChC,WN5CG;AFulCf;;AQ9kCI;;EAuCQ,+BNhDG;AF4lCf;;AQnlCI;;EA0CU,WNnDC;AFimCf;;AQxlCI;;EAgDM,qBAAkC;AR6iC5C;;AQ3mCE;EAKQ,qBAAkC;AR0mC5C;;AQ/mCE;EAUM,qBAAkC;ARymC1C;;AQrmCI;;;;;;EAOQ,yBAAsD;ARumClE;;AQ9mCI;;EAaI,yBNGS;EMFT,WNvBO;AF6nCf;;AQpnCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFooCf;;AQ3nCI;;EA6BQ,qBAAkC;ARmmC9C;;AQhoCI;;EAiCQ,yBNjBK;EMkBL,qBAAgC;EAChC,WN5CG;AFgpCf;;AQvoCI;;EAuCQ,+BNhDG;AFqpCf;;AQ5oCI;;EA0CU,WNnDC;AF0pCf;;AQjpCI;;EAgDM,qBAAkC;ARsmC5C;;AQpqCE;EAKQ,qBAAkC;ARmqC5C;;AQxqCE;EAUM,qBAAkC;ARkqC1C;;AQ9pCI;;;;;;EAOQ,yBAAsD;ARgqClE;;AQvqCI;;EAaI,yBNIS;EMHT,WNvBO;AFsrCf;;AQ7qCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AF6rCf;;AQprCI;;EA6BQ,qBAAkC;AR4pC9C;;AQzrCI;;EAiCQ,yBNhBK;EMiBL,qBAAgC;EAChC,WN5CG;AFysCf;;AQhsCI;;EAuCQ,+BNhDG;AF8sCf;;AQrsCI;;EA0CU,WNnDC;AFmtCf;;AQ1sCI;;EAgDM,qBAAkC;AR+pC5C;;AQ7tCE;EAKQ,qBAAkC;AR4tC5C;;AQjuCE;EAUM,qBAAkC;AR2tC1C;;AQvtCI;;;;;;EAOQ,yBAAsD;ARytClE;;AQhuCI;;EAaI,yBNKS;EMJT,WNvBO;AF+uCf;;AQtuCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFsvCf;;AQ7uCI;;EA6BQ,qBAAkC;ARqtC9C;;AQlvCI;;EAiCQ,yBNfK;EMgBL,qBAAgC;EAChC,WN5CG;AFkwCf;;AQzvCI;;EAuCQ,+BNhDG;AFuwCf;;AQ9vCI;;EA0CU,WNnDC;AF4wCf;;AQnwCI;;EAgDM,qBAAkC;ARwtC5C;;AQtxCE;EAKQ,qBAAkC;ARqxC5C;;AQ1xCE;EAUM,qBAAkC;ARoxC1C;;AQhxCI;;;;;;EAOQ,yBAAsD;ARkxClE;;AQzxCI;;EAaI,yBNMS;EMLT,WNvBO;AFwyCf;;AQ/xCI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AF+yCf;;AQtyCI;;EA6BQ,qBAAkC;AR8wC9C;;AQ3yCI;;EAiCQ,yBNdK;EMeL,qBAAgC;EAChC,WN5CG;AF2zCf;;AQlzCI;;EAuCQ,+BNhDG;AFg0Cf;;AQvzCI;;EA0CU,WNnDC;AFq0Cf;;AQ5zCI;;EAgDM,qBAAkC;ARixC5C;;AQ/0CE;EAKQ,qBAAkC;AR80C5C;;AQn1CE;EAUM,qBAAkC;AR60C1C;;AQz0CI;;;;;;EAOQ,yBAAsD;AR20ClE;;AQl1CI;;EAaI,yBNOS;EMNT,WNvBO;AFi2Cf;;AQx1CI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFw2Cf;;AQ/1CI;;EA6BQ,qBAAkC;ARu0C9C;;AQp2CI;;EAiCQ,yBNbK;EMcL,qBAAgC;EAChC,WN5CG;AFo3Cf;;AQ32CI;;EAuCQ,+BNhDG;AFy3Cf;;AQh3CI;;EA0CU,WNnDC;AF83Cf;;AQr3CI;;EAgDM,qBAAkC;AR00C5C;;AQx4CE;EAKQ,qBAAkC;ARu4C5C;;AQ54CE;EAUM,qBAAkC;ARs4C1C;;AQl4CI;;;;;;EAOQ,yBAAsD;ARo4ClE;;AQ34CI;;EAaI,yBNQS;EMPT,cN2De;AFw0CvB;;AQj5CI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFi6Cf;;AQx5CI;;EA6BQ,qBAAkC;ARg4C9C;;AQ75CI;;EAiCQ,yBNZK;EMaL,qBAAgC;EAChC,cNsCW;AF21CvB;;AQp6CI;;EAuCQ,4BNkCW;AFg2CvB;;AQz6CI;;EA0CU,cN+BS;AFq2CvB;;AQ96CI;;EAgDM,qBAAkC;ARm4C5C;;AQj8CE;EAKQ,qBAAkC;ARg8C5C;;AQr8CE;EAUM,qBAAkC;AR+7C1C;;AQ37CI;;;;;;EAOQ,yBAAsD;AR67ClE;;AQp8CI;;EAaI,yBNSS;EMRT,cN2De;AFi4CvB;;AQ18CI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AFw4CvB;;AQj9CI;;EA6BQ,qBAAkC;ARy7C9C;;AQt9CI;;EAiCQ,yBNXK;EMYL,qBAAgC;EAChC,cNsCW;AFo5CvB;;AQ79CI;;EAuCQ,4BNkCW;AFy5CvB;;AQl+CI;;EA0CU,cN+BS;AF85CvB;;AQv+CI;;EAgDM,qBAAkC;AR47C5C;;AQ1/CE;EAKQ,qBAAkC;ARy/C5C;;AQ9/CE;EAUM,qBAAkC;ARw/C1C;;AQp/CI;;;;;;EAOQ,yBAAsD;ARs/ClE;;AQ7/CI;;EAaI,yBNUS;EMTT,WNvBO;AF4gDf;;AQngDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFmhDf;;AQ1gDI;;EA6BQ,qBAAkC;ARk/C9C;;AQ/gDI;;EAiCQ,yBNVK;EMWL,qBAAgC;EAChC,WN5CG;AF+hDf;;AQthDI;;EAuCQ,+BNhDG;AFoiDf;;AQ3hDI;;EA0CU,WNnDC;AFyiDf;;AQhiDI;;EAgDM,qBAAkC;ARq/C5C;;AQnjDE;EAKQ,qBAAkC;ARkjD5C;;AQvjDE;EAUM,qBAAkC;ARijD1C;;AQ7iDI;;;;;;EAOQ,yBAAsD;AR+iDlE;;AQtjDI;;EAaI,yBNWS;EMVT,WNvBO;AFqkDf;;AQ5jDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AF4kDf;;AQnkDI;;EA6BQ,qBAAkC;AR2iD9C;;AQxkDI;;EAiCQ,yBNTK;EMUL,qBAAgC;EAChC,WN5CG;AFwlDf;;AQ/kDI;;EAuCQ,+BNhDG;AF6lDf;;AQplDI;;EA0CU,WNnDC;AFkmDf;;AQzlDI;;EAgDM,qBAAkC;AR8iD5C;;AQ5mDE;EAKQ,qBAAkC;AR2mD5C;;AQhnDE;EAUM,qBAAkC;AR0mD1C;;AQtmDI;;;;;;EAOQ,yBAAsD;ARwmDlE;;AQ/mDI;;EAaI,yBNYS;EMXT,WNvBO;AF8nDf;;AQrnDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFqoDf;;AQ5nDI;;EA6BQ,qBAAkC;ARomD9C;;AQjoDI;;EAiCQ,yBNRK;EMSL,qBAAgC;EAChC,WN5CG;AFipDf;;AQxoDI;;EAuCQ,+BNhDG;AFspDf;;AQ7oDI;;EA0CU,WNnDC;AF2pDf;;AQlpDI;;EAgDM,qBAAkC;ARumD5C;;AQrqDE;EAKQ,mBAAkC;ARoqD5C;;AQzqDE;EAUM,mBAAkC;ARmqD1C;;AQ/pDI;;;;;;EAOQ,uBAAsD;ARiqDlE;;AQxqDI;;EAaI,sBNtBO;EMuBP,cN2De;AFqmDvB;;AQ9qDI;;;EAmBQ,yBAAoC;EACpC,cNqDW;AF4mDvB;;AQrrDI;;EA6BQ,mBAAkC;AR6pD9C;;AQ1rDI;;EAiCQ,sBN1CG;EM2CH,qBAAgC;EAChC,cNsCW;AFwnDvB;;AQjsDI;;EAuCQ,4BNkCW;AF6nDvB;;AQtsDI;;EA0CU,cN+BS;AFkoDvB;;AQ3sDI;;EAgDM,mBAAkC;ARgqD5C;;AQ9tDE;EAKQ,qBAAkC;AR6tD5C;;AQluDE;EAUM,qBAAkC;AR4tD1C;;AQxtDI;;;;;;EAOQ,yBAAsD;AR0tDlE;;AQjuDI;;EAaI,yBNhBU;EMiBV,WNvBO;AFgvDf;;AQvuDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFuvDf;;AQ9uDI;;EA6BQ,qBAAkC;ARstD9C;;AQnvDI;;EAiCQ,yBNpCM;EMqCN,qBAAgC;EAChC,WN5CG;AFmwDf;;AQ1vDI;;EAuCQ,+BNhDG;AFwwDf;;AQ/vDI;;EA0CU,WNnDC;AF6wDf;;AQpwDI;;EAgDM,qBAAkC;ARytD5C;;AQvxDE;EAKQ,qBAAkC;ARsxD5C;;AQ3xDE;EAUM,qBAAkC;ARqxD1C;;AQjxDI;;;;;;EAOQ,yBAAsD;ARmxDlE;;AQ1xDI;;EAaI,yBNdU;EMeV,WNvBO;AFyyDf;;AQhyDI;;;EAmBQ,yBAAoC;EACpC,WN7BG;AFgzDf;;AQvyDI;;EA6BQ,qBAAkC;AR+wD9C;;AQ5yDI;;EAiCQ,yBNlCM;EMmCN,qBAAgC;EAChC,WN5CG;AF4zDf;;AQnzDI;;EAuCQ,+BNhDG;AFi0Df;;AQxzDI;;EA0CU,WNnDC;AFs0Df;;AQ7zDI;;EAgDM,qBAAkC;ARkxD5C;;AOnkDA;EAEI,yBLlQc;EKmQd,qBLrQc;AF00DlB;;AOxkDA;EAOI,yBAAsC;APqkD1C;;AO5kDA;EAWI,yBL3Qc;EK4Qd,qBL9Qc;AFm1DlB;;AOjlDA;EAeM,WLvRS;AF61Df;;AOrlDA;;EAoBI,yBLpRc;EKqRd,qBLvRc;EKwRd,YAAY;APskDhB;;AO5lDA;EAyBI,yBLzRc;EK0Rd,qBL5Rc;EK6Rd,YAAY;APukDhB;;AOlmDA;EA8BI,oCAA+C;EAC/C,cLpSc;AF42DlB;;AOvmDA;EAkCI,6BAA6B;EAC7B,WL3SW;AFo3Df;;AO5mDA;EAuCI,WL/SW;AFw3Df;;AS93DA;EACE,YP8qB8B;AFmtChC;;AS73DA;EAEI,YAAY;AT+3DhB;;ASj4DA;EAKI,WAAW;ATg4Df;;AS13DE;EAEI,mBPaW;AF+2DjB;;AS93DE;EAEI,mBPNY;AFs4DlB;;ASl4DE;EAEI,mBPoBW;AFg3DjB;;ASt4DE;EAEI,mBPsBW;AFk3DjB;;AS14DE;EAEI,mBPmBW;AFy3DjB;;AS94DE;EAEI,mBPiBW;AF+3DjB;;ASl5DE;EAEI,mBPXY;AF+5DlB;;ASt5DE;EAEI,mBPJY;AF45DlB;;ASl5DE;EAEI,mBHxBa;AN46DnB;;ASt5DE;EAEI,mBHvBQ;AN+6Dd;;AS15DE;EAEI,mBHrBS;ANi7Df;;AS95DE;EAEI,mBHpBQ;ANo7Dd;;ASl6DE;EAEI,mBHlBW;ANs7DjB;;ASt6DE;EAEI,mBHhBU;ANw7DhB;;AS16DE;EAEI,mBPKW;AFu6DjB;;AS96DE;EAEI,mBPMW;AF06DjB;;ASl7DE;EAEI,mBPOW;AF66DjB;;ASt7DE;EAEI,mBPQW;AFg7DjB;;AS17DE;EAEI,mBPSW;AFm7DjB;;AS97DE;EAEI,mBPUW;AFs7DjB;;ASl8DE;EAEI,mBPWW;AFy7DjB;;ASt8DE;EAEI,mBPYW;AF47DjB;;AS18DE;EAEI,mBPaW;AF+7DjB;;AS98DE;EAEI,mBPcW;AFk8DjB;;ASl9DE;EAEI,gBPpBS;AFw+Df;;ASt9DE;EAEI,mBPdY;AFs+DlB;;AS19DE;EAEI,mBPZY;AFw+DlB;;ASv9DA;EAEI,yBAAqC;EACrC,sBAAsB;ATy9D1B;;AU1/DE;;EAEE,qBAAc;AV6/DlB;;AU1/DE;;EAEE,qBAAc;AV6/DlB;;AU1/DE;;EAEE,yBAAkB;EAClB,qBAAc;AV6/DlB;;AU1gEE;;EAEE,qBAAc;AV6gElB;;AU1gEE;;EAEE,qBAAc;AV6gElB;;AU1gEE;;EAEE,yBAAkB;EAClB,qBAAc;AV6gElB;;AU1hEE;;EAEE,qBAAc;AV6hElB;;AU1hEE;;EAEE,qBAAc;AV6hElB;;AU1hEE;;EAEE,yBAAkB;EAClB,qBAAc;AV6hElB;;AU1iEE;;EAEE,qBAAc;AV6iElB;;AU1iEE;;EAEE,qBAAc;AV6iElB;;AU1iEE;;EAEE,yBAAkB;EAClB,qBAAc;AV6iElB;;AU1jEE;;EAEE,qBAAc;AV6jElB;;AU1jEE;;EAEE,qBAAc;AV6jElB;;AU1jEE;;EAEE,yBAAkB;EAClB,qBAAc;AV6jElB;;AU1kEE;;EAEE,qBAAc;AV6kElB;;AU1kEE;;EAEE,qBAAc;AV6kElB;;AU1kEE;;EAEE,yBAAkB;EAClB,qBAAc;AV6kElB;;AU1lEE;;EAEE,qBAAc;AV6lElB;;AU1lEE;;EAEE,qBAAc;AV6lElB;;AU1lEE;;EAEE,yBAAkB;EAClB,qBAAc;AV6lElB;;AU1mEE;;EAEE,qBAAc;AV6mElB;;AU1mEE;;EAEE,qBAAc;AV6mElB;;AU1mEE;;EAEE,yBAAkB;EAClB,qBAAc;AV6mElB;;AUvmEE;;EAEE,qBAAc;AV0mElB;;AUvmEE;;EAEE,qBAAc;AV0mElB;;AUvmEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0mElB;;AUvnEE;;EAEE,qBAAc;AV0nElB;;AUvnEE;;EAEE,qBAAc;AV0nElB;;AUvnEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0nElB;;AUvoEE;;EAEE,qBAAc;AV0oElB;;AUvoEE;;EAEE,qBAAc;AV0oElB;;AUvoEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0oElB;;AUvpEE;;EAEE,qBAAc;AV0pElB;;AUvpEE;;EAEE,qBAAc;AV0pElB;;AUvpEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0pElB;;AUvqEE;;EAEE,qBAAc;AV0qElB;;AUvqEE;;EAEE,qBAAc;AV0qElB;;AUvqEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0qElB;;AUvrEE;;EAEE,qBAAc;AV0rElB;;AUvrEE;;EAEE,qBAAc;AV0rElB;;AUvrEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0rElB;;AUvsEE;;EAEE,qBAAc;AV0sElB;;AUvsEE;;EAEE,qBAAc;AV0sElB;;AUvsEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0sElB;;AUvtEE;;EAEE,qBAAc;AV0tElB;;AUvtEE;;EAEE,qBAAc;AV0tElB;;AUvtEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0tElB;;AUvuEE;;EAEE,qBAAc;AV0uElB;;AUvuEE;;EAEE,qBAAc;AV0uElB;;AUvuEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0uElB;;AUvvEE;;EAEE,qBAAc;AV0vElB;;AUvvEE;;EAEE,qBAAc;AV0vElB;;AUvvEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0vElB;;AUvwEE;;EAEE,qBAAc;AV0wElB;;AUvwEE;;EAEE,qBAAc;AV0wElB;;AUvwEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0wElB;;AUvxEE;;EAEE,qBAAc;AV0xElB;;AUvxEE;;EAEE,qBAAc;AV0xElB;;AUvxEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0xElB;;AUvyEE;;EAEE,qBAAc;AV0yElB;;AUvyEE;;EAEE,qBAAc;AV0yElB;;AUvyEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0yElB;;AUvzEE;;EAEE,qBAAc;AV0zElB;;AUvzEE;;EAEE,qBAAc;AV0zElB;;AUvzEE;;EAEE,yBAAkB;EAClB,qBAAc;AV0zElB;;AUv0EE;;EAEE,qBAAc;AV00ElB;;AUv0EE;;EAEE,qBAAc;AV00ElB;;AUv0EE;;EAEE,yBAAkB;EAClB,qBAAc;AV00ElB;;AUv1EE;;EAEE,qBAAc;AV01ElB;;AUv1EE;;EAEE,qBAAc;AV01ElB;;AUv1EE;;EAEE,yBAAkB;EAClB,qBAAc;AV01ElB;;AUv2EE;;EAEE,kBAAc;AV02ElB;;AUv2EE;;EAEE,kBAAc;AV02ElB;;AUv2EE;;EAEE,sBAAkB;EAClB,kBAAc;AV02ElB;;AUv3EE;;EAEE,qBAAc;AV03ElB;;AUv3EE;;EAEE,qBAAc;AV03ElB;;AUv3EE;;EAEE,yBAAkB;EAClB,qBAAc;AV03ElB;;AUv4EE;;EAEE,qBAAc;AV04ElB;;AUv4EE;;EAEE,qBAAc;AV04ElB;;AUv4EE;;EAEE,yBAAkB;EAClB,qBAAc;AV04ElB;;AUt4EA;;EAIM,qBR7BY;AFo6ElB;;AWj7EA;EAEI,kBAAkB;AXm7EtB;;AWr7EA;ECHE,oLVuOmM;EUrOnM,kBAAkB;EAClB,gBV8O+B;EU7O/B,gBViP+B;EUhP/B,gBAAgB;EAChB,iBAAiB;EACjB,qBAAqB;EACrB,iBAAiB;EACjB,oBAAoB;EACpB,sBAAsB;EACtB,kBAAkB;EAClB,oBAAoB;EACpB,mBAAmB;EACnB,gBAAgB;ECGd,sBXmMgC;EYtF9B,mBAtCY;EH5Ed,sBTQW;ESPX,WTHW;ESIX,cAAc;EACd,gBTgqB+B;ES/pB/B,uBTqqB+B;ESpqB/B,kBAAkB;EAClB,kBAAkB;EAClB,qBAAqB;EACrB,aT2iBoC;AFq5DxC;;AWj9EA;EAqBI,yBTbc;EScd,yBTVc;ESWd,aAAa;EACb,YAAY;AXg8EhB;;AWx9EA;EA4BI,yBTpBc;ESqBd,sBL8K8B;EK7K9B,sBTmLgC;ESlLhC,WL2KuB;EK1KvB,eAAe;EACf,gBAAgB;EAChB,YAAY;EACZ,UAAU;EACV,iBAAiB;EACjB,iBAAiB;EACjB,kBAAkB;EAClB,kBAAkB;EAClB,MAAM;EAEN,yBAAiB;EAAjB,sBAAiB;EAAjB,qBAAiB;EAAjB,iBAAiB;EACjB,WAAW;AX+7Ef;;AW1+EA;EAgDM,yBAA8D;EAC9D,cAAyC;AX87E/C;;AW/+EA;EAsDI,iBAAiB;EACjB,SAAS;AX67Eb;;AWp/EA;EA2DI,SAAS;AX67Eb;;AWx/EA;EA+DI,SAAS;AX67Eb;;Ae3/EA;;EAEE,yBbKgB;EaJhB,sBTuMgC;EStMhC,sBb4MkC;Ea3MlC,WToMyB;ESnMzB,YAAY;EACZ,WAAW;Af8/Eb;;AergFA;;;;EAYI,yBAA8D;EAC9D,cAAyC;AfggF7C;;AgB7gFA;EAEI,mCAAiC;EACjC,cdqCa;AF0+EjB;;AgBlhFA;EAOI,mCAAoC;EACpC,cd6Ba;AFk/EjB;;AgBvhFA;EAYI,mCAAmC;EACnC,cdsBa;AFy/EjB;;AgB5hFA;EAiBI,mCAAsC;EACtC,cdNc;AFqhFlB;;AgBjiFA;EAsBI,mCAAoC;EACpC,cdea;AFggFjB;;AgBtiFA;EA0BM,mCAAoC;AhBghF1C;;AgB1iFA;EA8BM,yBdQW;AFwgFjB;;AgB3gFA;EAEI,yBdvBc;EcwBd,cd9Bc;AF2iFlB;;AgBhhFA;;EAOM,cdlCY;AFgjFlB;;AiB7hFA;EAGI,yBfCa;AF6hFjB;;AiBjiFA;EAOI,yBfIa;AF0hFjB;;AiBriFA;EAWI,yBfHa;AFiiFjB;;AiBziFA;EAeI,yBfFa;AFgiFjB;;AiB7iFA;EAmBI,yBfTa;AFuiFjB;;AiBzhFA;;EAEE,kBAAkB;AjB4hFpB;;AkBllFA;EACE,ahBujBsC;AF8hExC;;AkBtlFA;EAII,ahBojBoC;AFkiExC;;AkB1lFA;EAQI,ahBgjBoC;AFsiExC;;AkBhlFE;EAGM,mBhBeS;AFkkFjB;;AkB5kFE;EAEI,gBhBjBS;AF+lFf;;AkBhlFE;EAKM,mBhBKS;AF0kFjB;;AkBplFE;EASM,kMAA8M;AlB+kFtN;;AkB1kFE;EAGM,6BhBPS;AFklFjB;;AkBtkFE;EAGM,mBhBfS;AFslFjB;;AkBlkFE;EAEI,aAAa;EACb,WAAW;AlBokFjB;;AkBvkFE;EAMM,mBhB1BS;EgB2BT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBqkFxB;;AkB/kFE;EAeI,qBhBnCW;AFumFjB;;AkBnlFE;EAmBM,qBhBvCS;AF2mFjB;;AkB/jFE;EAGM,kChB/CS;EgBgDT,WhBzEO;AFyoFf;;AkB3jFE;EAGM,6CAA4C;AlB4jFpD;;AkB/jFE;EAOM,6CAA4C;AlB4jFpD;;AkBvjFE;EAEI,gBhB5FS;EgB6FT,qBhBpEW;AF6nFjB;;AkB5jFE;EAMM,mBhBvES;AFioFjB;;AkBrjFE;EAEI,chB9EW;AFqoFjB;;AkBnjFE;EAGM,mBhBrFS;AFyoFjB;;AkBvjFE;;EAQM,sBhBnHO;AFuqFf;;AkB5jFE;EAaQ,0ChB/FO;EgBgGP,yChBhGO;AFmpFjB;;AkBjkFE;EAkBQ,wChBpGO;EgBqGP,2ChBrGO;AFwpFjB;;AkB9iFE;EAGM,wChB7GS;AF4pFjB;;AkB1iFE;EAGM,mBhBrHS;AFgqFjB;;AkB9iFE;EAOM,6ChBzHS;AFoqFjB;;AkBljFE;EAWM,yBhB7HS;EgB8HT,0BhB9HS;AFyqFjB;;AkBtiFE;EAGM,mBhBtIS;EgBuIT,chBvIS;EgBwIT,sChBjKO;AFwsFf;;AkB5iFE;EASM,yDhBrKO;AF4sFf;;AkBliFE;EAGM,yBhBpJS;EgBqJT,kIhB9KO;AFitFf;;AkBviFE;EAQM,+FAAqG;EACrG,YAAY;AlBmiFpB;;AkB9hFE;EAEI,chBjKW;AFisFjB;;AkBntFE;EAGM,mBhBJU;AFwtFlB;;AkB/sFE;EAEI,gBhBjBS;AFkuFf;;AkBntFE;EAKM,mBhBdU;AFguFlB;;AkBvtFE;EASM,kMAA8M;AlBktFtN;;AkB7sFE;EAGM,+BhB1BU;AFwuFlB;;AkBzsFE;EAGM,mBhBlCU;AF4uFlB;;AkBrsFE;EAEI,aAAa;EACb,WAAW;AlBusFjB;;AkB1sFE;EAMM,mBhB7CU;EgB8CV,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBwsFxB;;AkBltFE;EAeI,qBhBtDY;AF6vFlB;;AkBttFE;EAmBM,qBhB1DU;AFiwFlB;;AkBlsFE;EAGM,oChBlEU;EgBmEV,WhBzEO;AF4wFf;;AkB9rFE;EAGM,6CAA4C;AlB+rFpD;;AkBlsFE;EAOM,6CAA4C;AlB+rFpD;;AkB1rFE;EAEI,gBhB5FS;EgB6FT,qBhBvFY;AFmxFlB;;AkB/rFE;EAMM,mBhB1FU;AFuxFlB;;AkBxrFE;EAEI,chBjGY;AF2xFlB;;AkBtrFE;EAGM,mBhBxGU;AF+xFlB;;AkB1rFE;;EAQM,sBhBnHO;AF0yFf;;AkB/rFE;EAaQ,4ChBlHQ;EgBmHR,2ChBnHQ;AFyyFlB;;AkBpsFE;EAkBQ,0ChBvHQ;EgBwHR,6ChBxHQ;AF8yFlB;;AkBjrFE;EAGM,0ChBhIU;AFkzFlB;;AkB7qFE;EAGM,mBhBxIU;AFszFlB;;AkBjrFE;EAOM,6ChB5IU;AF0zFlB;;AkBrrFE;EAWM,yBhBhJU;EgBiJV,0BhBjJU;AF+zFlB;;AkBzqFE;EAGM,mBhBzJU;EgB0JV,chB1JU;EgB2JV,sChBjKO;AF20Ff;;AkB/qFE;EASM,yDhBrKO;AF+0Ff;;AkBrqFE;EAGM,yBhBvKU;EgBwKV,kIhB9KO;AFo1Ff;;AkB1qFE;EAQM,+FAAqG;EACrG,YAAY;AlBsqFpB;;AkBjqFE;EAEI,chBpLY;AFu1FlB;;AkBt1FE;EAGM,mBhBsBS;AFi0FjB;;AkBl1FE;EAEI,gBhBjBS;AFq2Ff;;AkBt1FE;EAKM,mBhBYS;AFy0FjB;;AkB11FE;EASM,kMAA8M;AlBq1FtN;;AkBh1FE;EAGM,6BhBAS;AFi1FjB;;AkB50FE;EAGM,mBhBRS;AFq1FjB;;AkBx0FE;EAEI,aAAa;EACb,WAAW;AlB00FjB;;AkB70FE;EAMM,mBhBnBS;EgBoBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB20FxB;;AkBr1FE;EAeI,qBhB5BW;AFs2FjB;;AkBz1FE;EAmBM,qBhBhCS;AF02FjB;;AkBr0FE;EAGM,kChBxCS;EgByCT,WhBzEO;AF+4Ff;;AkBj0FE;EAGM,6CAA4C;AlBk0FpD;;AkBr0FE;EAOM,6CAA4C;AlBk0FpD;;AkB7zFE;EAEI,gBhB5FS;EgB6FT,qBhB7DW;AF43FjB;;AkBl0FE;EAMM,mBhBhES;AFg4FjB;;AkB3zFE;EAEI,chBvEW;AFo4FjB;;AkBzzFE;EAGM,mBhB9ES;AFw4FjB;;AkB7zFE;;EAQM,sBhBnHO;AF66Ff;;AkBl0FE;EAaQ,0ChBxFO;EgByFP,yChBzFO;AFk5FjB;;AkBv0FE;EAkBQ,wChB7FO;EgB8FP,2ChB9FO;AFu5FjB;;AkBpzFE;EAGM,wChBtGS;AF25FjB;;AkBhzFE;EAGM,mBhB9GS;AF+5FjB;;AkBpzFE;EAOM,6ChBlHS;AFm6FjB;;AkBxzFE;EAWM,yBhBtHS;EgBuHT,0BhBvHS;AFw6FjB;;AkB5yFE;EAGM,mBhB/HS;EgBgIT,chBhIS;EgBiIT,sChBjKO;AF88Ff;;AkBlzFE;EASM,yDhBrKO;AFk9Ff;;AkBxyFE;EAGM,yBhB7IS;EgB8IT,kIhB9KO;AFu9Ff;;AkB7yFE;EAQM,+FAAqG;EACrG,YAAY;AlByyFpB;;AkBpyFE;EAEI,chB1JW;AFg8FjB;;AkBz9FE;EAGM,mBhBwBS;AFk8FjB;;AkBr9FE;EAEI,gBhBjBS;AFw+Ff;;AkBz9FE;EAKM,mBhBcS;AF08FjB;;AkB79FE;EASM,kMAA8M;AlBw9FtN;;AkBn9FE;EAGM,8BhBES;AFk9FjB;;AkB/8FE;EAGM,mBhBNS;AFs9FjB;;AkB38FE;EAEI,aAAa;EACb,WAAW;AlB68FjB;;AkBh9FE;EAMM,mBhBjBS;EgBkBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB88FxB;;AkBx9FE;EAeI,qBhB1BW;AFu+FjB;;AkB59FE;EAmBM,qBhB9BS;AF2+FjB;;AkBx8FE;EAGM,mChBtCS;EgBuCT,WhBzEO;AFkhGf;;AkBp8FE;EAGM,6CAA4C;AlBq8FpD;;AkBx8FE;EAOM,6CAA4C;AlBq8FpD;;AkBh8FE;EAEI,gBhB5FS;EgB6FT,qBhB3DW;AF6/FjB;;AkBr8FE;EAMM,mBhB9DS;AFigGjB;;AkB97FE;EAEI,chBrEW;AFqgGjB;;AkB57FE;EAGM,mBhB5ES;AFygGjB;;AkBh8FE;;EAQM,sBhBnHO;AFgjGf;;AkBr8FE;EAaQ,2ChBtFO;EgBuFP,0ChBvFO;AFmhGjB;;AkB18FE;EAkBQ,yChB3FO;EgB4FP,4ChB5FO;AFwhGjB;;AkBv7FE;EAGM,yChBpGS;AF4hGjB;;AkBn7FE;EAGM,mBhB5GS;AFgiGjB;;AkBv7FE;EAOM,6ChBhHS;AFoiGjB;;AkB37FE;EAWM,yBhBpHS;EgBqHT,0BhBrHS;AFyiGjB;;AkB/6FE;EAGM,mBhB7HS;EgB8HT,chB9HS;EgB+HT,sChBjKO;AFilGf;;AkBr7FE;EASM,yDhBrKO;AFqlGf;;AkB36FE;EAGM,yBhB3IS;EgB4IT,kIhB9KO;AF0lGf;;AkBh7FE;EAQM,+FAAqG;EACrG,YAAY;AlB46FpB;;AkBv6FE;EAEI,chBxJW;AFikGjB;;AkB5lGE;EAGM,mBhBqBS;AFwkGjB;;AkBxlGE;EAEI,mBhBiEiB;AFyhGvB;;AkB5lGE;EAKM,mBhBWS;AFglGjB;;AkBhmGE;EASM,yLAA8M;AlB2lGtN;;AkBtlGE;EAGM,6BhBDS;AFwlGjB;;AkBllGE;EAGM,mBhBTS;AF4lGjB;;AkB9kGE;EAEI,aAAa;EACb,WAAW;AlBglGjB;;AkBnlGE;EAMM,mBhBpBS;EgBqBT,chB8Be;EgB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBilGxB;;AkB3lGE;EAeI,qBhB7BW;AF6mGjB;;AkB/lGE;EAmBM,qBhBjCS;AFinGjB;;AkB3kGE;EAGM,kChBzCS;EgB0CT,chBSe;AFmkGvB;;AkBvkGE;EAGM,6CAA4C;AlBwkGpD;;AkB3kGE;EAOM,6CAA4C;AlBwkGpD;;AkBnkGE;EAEI,mBhBViB;EgBWjB,qBhB9DW;AFmoGjB;;AkBxkGE;EAMM,mBhBjES;AFuoGjB;;AkBjkGE;EAEI,chBxEW;AF2oGjB;;AkB/jGE;EAGM,mBhB/ES;AF+oGjB;;AkBnkGE;;EAQM,yBhBjCe;AFimGvB;;AkBxkGE;EAaQ,0ChBzFO;EgB0FP,yChB1FO;AFypGjB;;AkB7kGE;EAkBQ,wChB9FO;EgB+FP,2ChB/FO;AF8pGjB;;AkB1jGE;EAGM,wChBvGS;AFkqGjB;;AkBtjGE;EAGM,mBhB/GS;AFsqGjB;;AkB1jGE;EAOM,6ChBnHS;AF0qGjB;;AkB9jGE;EAWM,yBhBvHS;EgBwHT,0BhBxHS;AF+qGjB;;AkBljGE;EAGM,mBhBhIS;EgBiIT,chBjIS;EgBkIT,4ChB/Ee;AFkoGvB;;AkBxjGE;EASM,4DhBnFe;AFsoGvB;;AkB9iGE;EAGM,yBhB9IS;EgB+IT,4HhB5Fe;AF2oGvB;;AkBnjGE;EAQM,yFAAqG;EACrG,YAAY;AlB+iGpB;;AkB1iGE;EAEI,chB3JW;AFusGjB;;AkB/tGE;EAGM,mBhBmBS;AF6sGjB;;AkB3tGE;EAEI,gBhBjBS;AF8uGf;;AkB/tGE;EAKM,mBhBSS;AFqtGjB;;AkBnuGE;EASM,kMAA8M;AlB8tGtN;;AkBztGE;EAGM,6BhBHS;AF6tGjB;;AkBrtGE;EAGM,mBhBXS;AFiuGjB;;AkBjtGE;EAEI,aAAa;EACb,WAAW;AlBmtGjB;;AkBttGE;EAMM,mBhBtBS;EgBuBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBotGxB;;AkB9tGE;EAeI,qBhB/BW;AFkvGjB;;AkBluGE;EAmBM,qBhBnCS;AFsvGjB;;AkB9sGE;EAGM,kChB3CS;EgB4CT,WhBzEO;AFwxGf;;AkB1sGE;EAGM,6CAA4C;AlB2sGpD;;AkB9sGE;EAOM,6CAA4C;AlB2sGpD;;AkBtsGE;EAEI,gBhB5FS;EgB6FT,qBhBhEW;AFwwGjB;;AkB3sGE;EAMM,mBhBnES;AF4wGjB;;AkBpsGE;EAEI,chB1EW;AFgxGjB;;AkBlsGE;EAGM,mBhBjFS;AFoxGjB;;AkBtsGE;;EAQM,sBhBnHO;AFszGf;;AkB3sGE;EAaQ,0ChB3FO;EgB4FP,yChB5FO;AF8xGjB;;AkBhtGE;EAkBQ,wChBhGO;EgBiGP,2ChBjGO;AFmyGjB;;AkB7rGE;EAGM,wChBzGS;AFuyGjB;;AkBzrGE;EAGM,mBhBjHS;AF2yGjB;;AkB7rGE;EAOM,6ChBrHS;AF+yGjB;;AkBjsGE;EAWM,yBhBzHS;EgB0HT,0BhB1HS;AFozGjB;;AkBrrGE;EAGM,mBhBlIS;EgBmIT,chBnIS;EgBoIT,sChBjKO;AFu1Gf;;AkB3rGE;EASM,yDhBrKO;AF21Gf;;AkBjrGE;EAGM,yBhBhJS;EgBiJT,kIhB9KO;AFg2Gf;;AkBtrGE;EAQM,+FAAqG;EACrG,YAAY;AlBkrGpB;;AkB7qGE;EAEI,chB7JW;AF40GjB;;AkBl2GE;EAGM,mBhBTU;AF42GlB;;AkB91GE;EAEI,mBhBiEiB;AF+xGvB;;AkBl2GE;EAKM,mBhBnBU;AFo3GlB;;AkBt2GE;EASM,yLAA8M;AlBi2GtN;;AkB51GE;EAGM,+BhB/BU;AF43GlB;;AkBx1GE;EAGM,mBhBvCU;AFg4GlB;;AkBp1GE;EAEI,aAAa;EACb,WAAW;AlBs1GjB;;AkBz1GE;EAMM,mBhBlDU;EgBmDV,chB8Be;EgB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBu1GxB;;AkBj2GE;EAeI,qBhB3DY;AFi5GlB;;AkBr2GE;EAmBM,qBhB/DU;AFq5GlB;;AkBj1GE;EAGM,oChBvEU;EgBwEV,chBSe;AFy0GvB;;AkB70GE;EAGM,6CAA4C;AlB80GpD;;AkBj1GE;EAOM,6CAA4C;AlB80GpD;;AkBz0GE;EAEI,mBhBViB;EgBWjB,qBhB5FY;AFu6GlB;;AkB90GE;EAMM,mBhB/FU;AF26GlB;;AkBv0GE;EAEI,chBtGY;AF+6GlB;;AkBr0GE;EAGM,mBhB7GU;AFm7GlB;;AkBz0GE;;EAQM,yBhBjCe;AFu2GvB;;AkB90GE;EAaQ,4ChBvHQ;EgBwHR,2ChBxHQ;AF67GlB;;AkBn1GE;EAkBQ,0ChB5HQ;EgB6HR,6ChB7HQ;AFk8GlB;;AkBh0GE;EAGM,0ChBrIU;AFs8GlB;;AkB5zGE;EAGM,mBhB7IU;AF08GlB;;AkBh0GE;EAOM,6ChBjJU;AF88GlB;;AkBp0GE;EAWM,yBhBrJU;EgBsJV,0BhBtJU;AFm9GlB;;AkBxzGE;EAGM,mBhB9JU;EgB+JV,chB/JU;EgBgKV,4ChB/Ee;AFw4GvB;;AkB9zGE;EASM,4DhBnFe;AF44GvB;;AkBpzGE;EAGM,yBhB5KU;EgB6KV,4HhB5Fe;AFi5GvB;;AkBzzGE;EAQM,yFAAqG;EACrG,YAAY;AlBqzGpB;;AkBhzGE;EAEI,chBzLY;AF2+GlB;;AkBr+GE;EAGM,mBhBFU;AFw+GlB;;AkBj+GE;EAEI,gBhBjBS;AFo/Gf;;AkBr+GE;EAKM,mBhBZU;AFg/GlB;;AkBz+GE;EASM,kMAA8M;AlBo+GtN;;AkB/9GE;EAGM,4BhBxBU;AFw/GlB;;AkB39GE;EAGM,mBhBhCU;AF4/GlB;;AkBv9GE;EAEI,aAAa;EACb,WAAW;AlBy9GjB;;AkB59GE;EAMM,mBhB3CU;EgB4CV,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB09GxB;;AkBp+GE;EAeI,qBhBpDY;AF6gHlB;;AkBx+GE;EAmBM,qBhBxDU;AFihHlB;;AkBp9GE;EAGM,iChBhEU;EgBiEV,WhBzEO;AF8hHf;;AkBh9GE;EAGM,6CAA4C;AlBi9GpD;;AkBp9GE;EAOM,6CAA4C;AlBi9GpD;;AkB58GE;EAEI,gBhB5FS;EgB6FT,qBhBrFY;AFmiHlB;;AkBj9GE;EAMM,mBhBxFU;AFuiHlB;;AkB18GE;EAEI,chB/FY;AF2iHlB;;AkBx8GE;EAGM,mBhBtGU;AF+iHlB;;AkB58GE;;EAQM,sBhBnHO;AF4jHf;;AkBj9GE;EAaQ,yChBhHQ;EgBiHR,wChBjHQ;AFyjHlB;;AkBt9GE;EAkBQ,uChBrHQ;EgBsHR,0ChBtHQ;AF8jHlB;;AkBn8GE;EAGM,uChB9HU;AFkkHlB;;AkB/7GE;EAGM,mBhBtIU;AFskHlB;;AkBn8GE;EAOM,6ChB1IU;AF0kHlB;;AkBv8GE;EAWM,yBhB9IU;EgB+IV,0BhB/IU;AF+kHlB;;AkB37GE;EAGM,mBhBvJU;EgBwJV,chBxJU;EgByJV,sChBjKO;AF6lHf;;AkBj8GE;EASM,yDhBrKO;AFimHf;;AkBv7GE;EAGM,yBhBrKU;EgBsKV,kIhB9KO;AFsmHf;;AkB57GE;EAQM,+FAAqG;EACrG,YAAY;AlBw7GpB;;AkBn7GE;EAEI,chBlLY;AFumHlB;;AkBxmHE;EAGM,mBZdW;ANunHnB;;AkBpmHE;EAEI,gBhBjBS;AFunHf;;AkBxmHE;EAKM,mBZxBW;AN+nHnB;;AkB5mHE;EASM,kMAA8M;AlBumHtN;;AkBlmHE;EAGM,8BZpCW;ANuoHnB;;AkB9lHE;EAGM,mBZ5CW;AN2oHnB;;AkB1lHE;EAEI,aAAa;EACb,WAAW;AlB4lHjB;;AkB/lHE;EAMM,mBZvDW;EYwDX,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB6lHxB;;AkBvmHE;EAeI,qBZhEa;AN4pHnB;;AkB3mHE;EAmBM,qBZpEW;ANgqHnB;;AkBvlHE;EAGM,mCZ5EW;EY6EX,WhBzEO;AFiqHf;;AkBnlHE;EAGM,6CAA4C;AlBolHpD;;AkBvlHE;EAOM,6CAA4C;AlBolHpD;;AkB/kHE;EAEI,gBhB5FS;EgB6FT,qBZjGa;ANkrHnB;;AkBplHE;EAMM,mBZpGW;ANsrHnB;;AkB7kHE;EAEI,cZ3Ga;AN0rHnB;;AkB3kHE;EAGM,mBZlHW;AN8rHnB;;AkB/kHE;;EAQM,sBhBnHO;AF+rHf;;AkBplHE;EAaQ,2CZ5HS;EY6HT,0CZ7HS;ANwsHnB;;AkBzlHE;EAkBQ,yCZjIS;EYkIT,4CZlIS;AN6sHnB;;AkBtkHE;EAGM,yCZ1IW;ANitHnB;;AkBlkHE;EAGM,mBZlJW;ANqtHnB;;AkBtkHE;EAOM,6CZtJW;ANytHnB;;AkB1kHE;EAWM,yBZ1JW;EY2JX,0BZ3JW;AN8tHnB;;AkB9jHE;EAGM,mBZnKW;EYoKX,cZpKW;EYqKX,sChBjKO;AFguHf;;AkBpkHE;EASM,yDhBrKO;AFouHf;;AkB1jHE;EAGM,yBZjLW;EYkLX,kIhB9KO;AFyuHf;;AkB/jHE;EAQM,+FAAqG;EACrG,YAAY;AlB2jHpB;;AkBtjHE;EAEI,cZ9La;ANsvHnB;;AkB3uHE;EAGM,mBZbM;ANyvHd;;AkBvuHE;EAEI,gBhBjBS;AF0vHf;;AkB3uHE;EAKM,mBZvBM;ANiwHd;;AkB/uHE;EASM,kMAA8M;AlB0uHtN;;AkBruHE;EAGM,2BZnCM;ANywHd;;AkBjuHE;EAGM,mBZ3CM;AN6wHd;;AkB7tHE;EAEI,aAAa;EACb,WAAW;AlB+tHjB;;AkBluHE;EAMM,mBZtDM;EYuDN,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBguHxB;;AkB1uHE;EAeI,qBZ/DQ;AN8xHd;;AkB9uHE;EAmBM,qBZnEM;ANkyHd;;AkB1tHE;EAGM,gCZ3EM;EY4EN,WhBzEO;AFoyHf;;AkBttHE;EAGM,6CAA4C;AlButHpD;;AkB1tHE;EAOM,6CAA4C;AlButHpD;;AkBltHE;EAEI,gBhB5FS;EgB6FT,qBZhGQ;ANozHd;;AkBvtHE;EAMM,mBZnGM;ANwzHd;;AkBhtHE;EAEI,cZ1GQ;AN4zHd;;AkB9sHE;EAGM,mBZjHM;ANg0Hd;;AkBltHE;;EAQM,sBhBnHO;AFk0Hf;;AkBvtHE;EAaQ,wCZ3HI;EY4HJ,uCZ5HI;AN00Hd;;AkB5tHE;EAkBQ,sCZhII;EYiIJ,yCZjII;AN+0Hd;;AkBzsHE;EAGM,sCZzIM;ANm1Hd;;AkBrsHE;EAGM,mBZjJM;ANu1Hd;;AkBzsHE;EAOM,6CZrJM;AN21Hd;;AkB7sHE;EAWM,yBZzJM;EY0JN,0BZ1JM;ANg2Hd;;AkBjsHE;EAGM,mBZlKM;EYmKN,cZnKM;EYoKN,sChBjKO;AFm2Hf;;AkBvsHE;EASM,yDhBrKO;AFu2Hf;;AkB7rHE;EAGM,yBZhLM;EYiLN,kIhB9KO;AF42Hf;;AkBlsHE;EAQM,+FAAqG;EACrG,YAAY;AlB8rHpB;;AkBzrHE;EAEI,cZ7LQ;ANw3Hd;;AkB92HE;EAGM,mBZXO;AN03Hf;;AkB12HE;EAEI,gBhBjBS;AF63Hf;;AkB92HE;EAKM,mBZrBO;ANk4Hf;;AkBl3HE;EASM,kMAA8M;AlB62HtN;;AkBx2HE;EAGM,8BZjCO;AN04Hf;;AkBp2HE;EAGM,mBZzCO;AN84Hf;;AkBh2HE;EAEI,aAAa;EACb,WAAW;AlBk2HjB;;AkBr2HE;EAMM,mBZpDO;EYqDP,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBm2HxB;;AkB72HE;EAeI,qBZ7DS;AN+5Hf;;AkBj3HE;EAmBM,qBZjEO;ANm6Hf;;AkB71HE;EAGM,mCZzEO;EY0EP,WhBzEO;AFu6Hf;;AkBz1HE;EAGM,6CAA4C;AlB01HpD;;AkB71HE;EAOM,6CAA4C;AlB01HpD;;AkBr1HE;EAEI,gBhB5FS;EgB6FT,qBZ9FS;ANq7Hf;;AkB11HE;EAMM,mBZjGO;ANy7Hf;;AkBn1HE;EAEI,cZxGS;AN67Hf;;AkBj1HE;EAGM,mBZ/GO;ANi8Hf;;AkBr1HE;;EAQM,sBhBnHO;AFq8Hf;;AkB11HE;EAaQ,2CZzHK;EY0HL,0CZ1HK;AN28Hf;;AkB/1HE;EAkBQ,yCZ9HK;EY+HL,4CZ/HK;ANg9Hf;;AkB50HE;EAGM,yCZvIO;ANo9Hf;;AkBx0HE;EAGM,mBZ/IO;ANw9Hf;;AkB50HE;EAOM,6CZnJO;AN49Hf;;AkBh1HE;EAWM,yBZvJO;EYwJP,0BZxJO;ANi+Hf;;AkBp0HE;EAGM,mBZhKO;EYiKP,cZjKO;EYkKP,sChBjKO;AFs+Hf;;AkB10HE;EASM,yDhBrKO;AF0+Hf;;AkBh0HE;EAGM,yBZ9KO;EY+KP,kIhB9KO;AF++Hf;;AkBr0HE;EAQM,+FAAqG;EACrG,YAAY;AlBi0HpB;;AkB5zHE;EAEI,cZ3LS;ANy/Hf;;AkBj/HE;EAGM,mBZVM;AN4/Hd;;AkB7+HE;EAEI,mBhBiEiB;AF86HvB;;AkBj/HE;EAKM,mBZpBM;ANogId;;AkBr/HE;EASM,yLAA8M;AlBg/HtN;;AkB3+HE;EAGM,6BZhCM;AN4gId;;AkBv+HE;EAGM,mBZxCM;ANghId;;AkBn+HE;EAEI,aAAa;EACb,WAAW;AlBq+HjB;;AkBx+HE;EAMM,mBZnDM;EYoDN,chB8Be;EgB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBs+HxB;;AkBh/HE;EAeI,qBZ5DQ;ANiiId;;AkBp/HE;EAmBM,qBZhEM;ANqiId;;AkBh+HE;EAGM,kCZxEM;EYyEN,chBSe;AFw9HvB;;AkB59HE;EAGM,6CAA4C;AlB69HpD;;AkBh+HE;EAOM,6CAA4C;AlB69HpD;;AkBx9HE;EAEI,mBhBViB;EgBWjB,qBZ7FQ;ANujId;;AkB79HE;EAMM,mBZhGM;AN2jId;;AkBt9HE;EAEI,cZvGQ;AN+jId;;AkBp9HE;EAGM,mBZ9GM;ANmkId;;AkBx9HE;;EAQM,yBhBjCe;AFs/HvB;;AkB79HE;EAaQ,0CZxHI;EYyHJ,yCZzHI;AN6kId;;AkBl+HE;EAkBQ,wCZ7HI;EY8HJ,2CZ9HI;ANklId;;AkB/8HE;EAGM,wCZtIM;ANslId;;AkB38HE;EAGM,mBZ9IM;AN0lId;;AkB/8HE;EAOM,6CZlJM;AN8lId;;AkBn9HE;EAWM,yBZtJM;EYuJN,0BZvJM;ANmmId;;AkBv8HE;EAGM,mBZ/JM;EYgKN,cZhKM;EYiKN,4ChB/Ee;AFuhIvB;;AkB78HE;EASM,4DhBnFe;AF2hIvB;;AkBn8HE;EAGM,yBZ7KM;EY8KN,4HhB5Fe;AFgiIvB;;AkBx8HE;EAQM,yFAAqG;EACrG,YAAY;AlBo8HpB;;AkB/7HE;EAEI,cZ1LQ;AN2nId;;AkBpnIE;EAGM,mBZRS;AN6nIjB;;AkBhnIE;EAEI,gBhBjBS;AFmoIf;;AkBpnIE;EAKM,mBZlBS;ANqoIjB;;AkBxnIE;EASM,kMAA8M;AlBmnItN;;AkB9mIE;EAGM,8BZ9BS;AN6oIjB;;AkB1mIE;EAGM,mBZtCS;ANipIjB;;AkBtmIE;EAEI,aAAa;EACb,WAAW;AlBwmIjB;;AkB3mIE;EAMM,mBZjDS;EYkDT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBymIxB;;AkBnnIE;EAeI,qBZ1DW;ANkqIjB;;AkBvnIE;EAmBM,qBZ9DS;ANsqIjB;;AkBnmIE;EAGM,mCZtES;EYuET,WhBzEO;AF6qIf;;AkB/lIE;EAGM,6CAA4C;AlBgmIpD;;AkBnmIE;EAOM,6CAA4C;AlBgmIpD;;AkB3lIE;EAEI,gBhB5FS;EgB6FT,qBZ3FW;ANwrIjB;;AkBhmIE;EAMM,mBZ9FS;AN4rIjB;;AkBzlIE;EAEI,cZrGW;ANgsIjB;;AkBvlIE;EAGM,mBZ5GS;ANosIjB;;AkB3lIE;;EAQM,sBhBnHO;AF2sIf;;AkBhmIE;EAaQ,2CZtHO;EYuHP,0CZvHO;AN8sIjB;;AkBrmIE;EAkBQ,yCZ3HO;EY4HP,4CZ5HO;ANmtIjB;;AkBllIE;EAGM,yCZpIS;ANutIjB;;AkB9kIE;EAGM,mBZ5IS;AN2tIjB;;AkBllIE;EAOM,6CZhJS;AN+tIjB;;AkBtlIE;EAWM,yBZpJS;EYqJT,0BZrJS;ANouIjB;;AkB1kIE;EAGM,mBZ7JS;EY8JT,cZ9JS;EY+JT,sChBjKO;AF4uIf;;AkBhlIE;EASM,yDhBrKO;AFgvIf;;AkBtkIE;EAGM,yBZ3KS;EY4KT,kIhB9KO;AFqvIf;;AkB3kIE;EAQM,+FAAqG;EACrG,YAAY;AlBukIpB;;AkBlkIE;EAEI,cZxLW;AN4vIjB;;AkBvvIE;EAGM,mBZNQ;AN8vIhB;;AkBnvIE;EAEI,gBhBjBS;AFswIf;;AkBvvIE;EAKM,mBZhBQ;ANswIhB;;AkB3vIE;EASM,kMAA8M;AlBsvItN;;AkBjvIE;EAGM,6BZ5BQ;AN8wIhB;;AkB7uIE;EAGM,mBZpCQ;ANkxIhB;;AkBzuIE;EAEI,aAAa;EACb,WAAW;AlB2uIjB;;AkB9uIE;EAMM,mBZ/CQ;EYgDR,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB4uIxB;;AkBtvIE;EAeI,qBZxDU;ANmyIhB;;AkB1vIE;EAmBM,qBZ5DQ;ANuyIhB;;AkBtuIE;EAGM,kCZpEQ;EYqER,WhBzEO;AFgzIf;;AkBluIE;EAGM,6CAA4C;AlBmuIpD;;AkBtuIE;EAOM,6CAA4C;AlBmuIpD;;AkB9tIE;EAEI,gBhB5FS;EgB6FT,qBZzFU;ANyzIhB;;AkBnuIE;EAMM,mBZ5FQ;AN6zIhB;;AkB5tIE;EAEI,cZnGU;ANi0IhB;;AkB1tIE;EAGM,mBZ1GQ;ANq0IhB;;AkB9tIE;;EAQM,sBhBnHO;AF80If;;AkBnuIE;EAaQ,0CZpHM;EYqHN,yCZrHM;AN+0IhB;;AkBxuIE;EAkBQ,wCZzHM;EY0HN,2CZ1HM;ANo1IhB;;AkBrtIE;EAGM,wCZlIQ;ANw1IhB;;AkBjtIE;EAGM,mBZ1IQ;AN41IhB;;AkBrtIE;EAOM,6CZ9IQ;ANg2IhB;;AkBztIE;EAWM,yBZlJQ;EYmJR,0BZnJQ;ANq2IhB;;AkB7sIE;EAGM,mBZ3JQ;EY4JR,cZ5JQ;EY6JR,sChBjKO;AF+2If;;AkBntIE;EASM,yDhBrKO;AFm3If;;AkBzsIE;EAGM,yBZzKQ;EY0KR,kIhB9KO;AFw3If;;AkB9sIE;EAQM,+FAAqG;EACrG,YAAY;AlB0sIpB;;AkBrsIE;EAEI,cZtLU;AN63IhB;;AkB13IE;EAGM,mBhBeS;AF42IjB;;AkBt3IE;EAEI,gBhBjBS;AFy4If;;AkB13IE;EAKM,mBhBKS;AFo3IjB;;AkB93IE;EASM,kMAA8M;AlBy3ItN;;AkBp3IE;EAGM,6BhBPS;AF43IjB;;AkBh3IE;EAGM,mBhBfS;AFg4IjB;;AkB52IE;EAEI,aAAa;EACb,WAAW;AlB82IjB;;AkBj3IE;EAMM,mBhB1BS;EgB2BT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB+2IxB;;AkBz3IE;EAeI,qBhBnCW;AFi5IjB;;AkB73IE;EAmBM,qBhBvCS;AFq5IjB;;AkBz2IE;EAGM,kChB/CS;EgBgDT,WhBzEO;AFm7If;;AkBr2IE;EAGM,6CAA4C;AlBs2IpD;;AkBz2IE;EAOM,6CAA4C;AlBs2IpD;;AkBj2IE;EAEI,gBhB5FS;EgB6FT,qBhBpEW;AFu6IjB;;AkBt2IE;EAMM,mBhBvES;AF26IjB;;AkB/1IE;EAEI,chB9EW;AF+6IjB;;AkB71IE;EAGM,mBhBrFS;AFm7IjB;;AkBj2IE;;EAQM,sBhBnHO;AFi9If;;AkBt2IE;EAaQ,0ChB/FO;EgBgGP,yChBhGO;AF67IjB;;AkB32IE;EAkBQ,wChBpGO;EgBqGP,2ChBrGO;AFk8IjB;;AkBx1IE;EAGM,wChB7GS;AFs8IjB;;AkBp1IE;EAGM,mBhBrHS;AF08IjB;;AkBx1IE;EAOM,6ChBzHS;AF88IjB;;AkB51IE;EAWM,yBhB7HS;EgB8HT,0BhB9HS;AFm9IjB;;AkBh1IE;EAGM,mBhBtIS;EgBuIT,chBvIS;EgBwIT,sChBjKO;AFk/If;;AkBt1IE;EASM,yDhBrKO;AFs/If;;AkB50IE;EAGM,yBhBpJS;EgBqJT,kIhB9KO;AF2/If;;AkBj1IE;EAQM,+FAAqG;EACrG,YAAY;AlB60IpB;;AkBx0IE;EAEI,chBjKW;AF2+IjB;;AkB7/IE;EAGM,mBhBgBS;AF8+IjB;;AkBz/IE;EAEI,gBhBjBS;AF4gJf;;AkB7/IE;EAKM,mBhBMS;AFs/IjB;;AkBjgJE;EASM,kMAA8M;AlB4/ItN;;AkBv/IE;EAGM,8BhBNS;AF8/IjB;;AkBn/IE;EAGM,mBhBdS;AFkgJjB;;AkB/+IE;EAEI,aAAa;EACb,WAAW;AlBi/IjB;;AkBp/IE;EAMM,mBhBzBS;EgB0BT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBk/IxB;;AkB5/IE;EAeI,qBhBlCW;AFmhJjB;;AkBhgJE;EAmBM,qBhBtCS;AFuhJjB;;AkB5+IE;EAGM,mChB9CS;EgB+CT,WhBzEO;AFsjJf;;AkBx+IE;EAGM,6CAA4C;AlBy+IpD;;AkB5+IE;EAOM,6CAA4C;AlBy+IpD;;AkBp+IE;EAEI,gBhB5FS;EgB6FT,qBhBnEW;AFyiJjB;;AkBz+IE;EAMM,mBhBtES;AF6iJjB;;AkBl+IE;EAEI,chB7EW;AFijJjB;;AkBh+IE;EAGM,mBhBpFS;AFqjJjB;;AkBp+IE;;EAQM,sBhBnHO;AFolJf;;AkBz+IE;EAaQ,2ChB9FO;EgB+FP,0ChB/FO;AF+jJjB;;AkB9+IE;EAkBQ,yChBnGO;EgBoGP,4ChBpGO;AFokJjB;;AkB39IE;EAGM,yChB5GS;AFwkJjB;;AkBv9IE;EAGM,mBhBpHS;AF4kJjB;;AkB39IE;EAOM,6ChBxHS;AFglJjB;;AkB/9IE;EAWM,yBhB5HS;EgB6HT,0BhB7HS;AFqlJjB;;AkBn9IE;EAGM,mBhBrIS;EgBsIT,chBtIS;EgBuIT,sChBjKO;AFqnJf;;AkBz9IE;EASM,yDhBrKO;AFynJf;;AkB/8IE;EAGM,yBhBnJS;EgBoJT,kIhB9KO;AF8nJf;;AkBp9IE;EAQM,+FAAqG;EACrG,YAAY;AlBg9IpB;;AkB38IE;EAEI,chBhKW;AF6mJjB;;AkBhoJE;EAGM,mBhBiBS;AFgnJjB;;AkB5nJE;EAEI,gBhBjBS;AF+oJf;;AkBhoJE;EAKM,mBhBOS;AFwnJjB;;AkBpoJE;EASM,kMAA8M;AlB+nJtN;;AkB1nJE;EAGM,8BhBLS;AFgoJjB;;AkBtnJE;EAGM,mBhBbS;AFooJjB;;AkBlnJE;EAEI,aAAa;EACb,WAAW;AlBonJjB;;AkBvnJE;EAMM,mBhBxBS;EgByBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBqnJxB;;AkB/nJE;EAeI,qBhBjCW;AFqpJjB;;AkBnoJE;EAmBM,qBhBrCS;AFypJjB;;AkB/mJE;EAGM,mChB7CS;EgB8CT,WhBzEO;AFyrJf;;AkB3mJE;EAGM,6CAA4C;AlB4mJpD;;AkB/mJE;EAOM,6CAA4C;AlB4mJpD;;AkBvmJE;EAEI,gBhB5FS;EgB6FT,qBhBlEW;AF2qJjB;;AkB5mJE;EAMM,mBhBrES;AF+qJjB;;AkBrmJE;EAEI,chB5EW;AFmrJjB;;AkBnmJE;EAGM,mBhBnFS;AFurJjB;;AkBvmJE;;EAQM,sBhBnHO;AFutJf;;AkB5mJE;EAaQ,2ChB7FO;EgB8FP,0ChB9FO;AFisJjB;;AkBjnJE;EAkBQ,yChBlGO;EgBmGP,4ChBnGO;AFssJjB;;AkB9lJE;EAGM,yChB3GS;AF0sJjB;;AkB1lJE;EAGM,mBhBnHS;AF8sJjB;;AkB9lJE;EAOM,6ChBvHS;AFktJjB;;AkBlmJE;EAWM,yBhB3HS;EgB4HT,0BhB5HS;AFutJjB;;AkBtlJE;EAGM,mBhBpIS;EgBqIT,chBrIS;EgBsIT,sChBjKO;AFwvJf;;AkB5lJE;EASM,yDhBrKO;AF4vJf;;AkBllJE;EAGM,yBhBlJS;EgBmJT,kIhB9KO;AFiwJf;;AkBvlJE;EAQM,+FAAqG;EACrG,YAAY;AlBmlJpB;;AkB9kJE;EAEI,chB/JW;AF+uJjB;;AkBnwJE;EAGM,mBhBkBS;AFkvJjB;;AkB/vJE;EAEI,gBhBjBS;AFkxJf;;AkBnwJE;EAKM,mBhBQS;AF0vJjB;;AkBvwJE;EASM,kMAA8M;AlBkwJtN;;AkB7vJE;EAGM,8BhBJS;AFkwJjB;;AkBzvJE;EAGM,mBhBZS;AFswJjB;;AkBrvJE;EAEI,aAAa;EACb,WAAW;AlBuvJjB;;AkB1vJE;EAMM,mBhBvBS;EgBwBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBwvJxB;;AkBlwJE;EAeI,qBhBhCW;AFuxJjB;;AkBtwJE;EAmBM,qBhBpCS;AF2xJjB;;AkBlvJE;EAGM,mChB5CS;EgB6CT,WhBzEO;AF4zJf;;AkB9uJE;EAGM,6CAA4C;AlB+uJpD;;AkBlvJE;EAOM,6CAA4C;AlB+uJpD;;AkB1uJE;EAEI,gBhB5FS;EgB6FT,qBhBjEW;AF6yJjB;;AkB/uJE;EAMM,mBhBpES;AFizJjB;;AkBxuJE;EAEI,chB3EW;AFqzJjB;;AkBtuJE;EAGM,mBhBlFS;AFyzJjB;;AkB1uJE;;EAQM,sBhBnHO;AF01Jf;;AkB/uJE;EAaQ,2ChB5FO;EgB6FP,0ChB7FO;AFm0JjB;;AkBpvJE;EAkBQ,yChBjGO;EgBkGP,4ChBlGO;AFw0JjB;;AkBjuJE;EAGM,yChB1GS;AF40JjB;;AkB7tJE;EAGM,mBhBlHS;AFg1JjB;;AkBjuJE;EAOM,6ChBtHS;AFo1JjB;;AkBruJE;EAWM,yBhB1HS;EgB2HT,0BhB3HS;AFy1JjB;;AkBztJE;EAGM,mBhBnIS;EgBoIT,chBpIS;EgBqIT,sChBjKO;AF23Jf;;AkB/tJE;EASM,yDhBrKO;AF+3Jf;;AkBrtJE;EAGM,yBhBjJS;EgBkJT,kIhB9KO;AFo4Jf;;AkB1tJE;EAQM,+FAAqG;EACrG,YAAY;AlBstJpB;;AkBjtJE;EAEI,chB9JW;AFi3JjB;;AkBt4JE;EAGM,mBhBmBS;AFo3JjB;;AkBl4JE;EAEI,gBhBjBS;AFq5Jf;;AkBt4JE;EAKM,mBhBSS;AF43JjB;;AkB14JE;EASM,kMAA8M;AlBq4JtN;;AkBh4JE;EAGM,6BhBHS;AFo4JjB;;AkB53JE;EAGM,mBhBXS;AFw4JjB;;AkBx3JE;EAEI,aAAa;EACb,WAAW;AlB03JjB;;AkB73JE;EAMM,mBhBtBS;EgBuBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB23JxB;;AkBr4JE;EAeI,qBhB/BW;AFy5JjB;;AkBz4JE;EAmBM,qBhBnCS;AF65JjB;;AkBr3JE;EAGM,kChB3CS;EgB4CT,WhBzEO;AF+7Jf;;AkBj3JE;EAGM,6CAA4C;AlBk3JpD;;AkBr3JE;EAOM,6CAA4C;AlBk3JpD;;AkB72JE;EAEI,gBhB5FS;EgB6FT,qBhBhEW;AF+6JjB;;AkBl3JE;EAMM,mBhBnES;AFm7JjB;;AkB32JE;EAEI,chB1EW;AFu7JjB;;AkBz2JE;EAGM,mBhBjFS;AF27JjB;;AkB72JE;;EAQM,sBhBnHO;AF69Jf;;AkBl3JE;EAaQ,0ChB3FO;EgB4FP,yChB5FO;AFq8JjB;;AkBv3JE;EAkBQ,wChBhGO;EgBiGP,2ChBjGO;AF08JjB;;AkBp2JE;EAGM,wChBzGS;AF88JjB;;AkBh2JE;EAGM,mBhBjHS;AFk9JjB;;AkBp2JE;EAOM,6ChBrHS;AFs9JjB;;AkBx2JE;EAWM,yBhBzHS;EgB0HT,0BhB1HS;AF29JjB;;AkB51JE;EAGM,mBhBlIS;EgBmIT,chBnIS;EgBoIT,sChBjKO;AF8/Jf;;AkBl2JE;EASM,yDhBrKO;AFkgKf;;AkBx1JE;EAGM,yBhBhJS;EgBiJT,kIhB9KO;AFugKf;;AkB71JE;EAQM,+FAAqG;EACrG,YAAY;AlBy1JpB;;AkBp1JE;EAEI,chB7JW;AFm/JjB;;AkBzgKE;EAGM,mBhBoBS;AFs/JjB;;AkBrgKE;EAEI,mBhBiEiB;AFs8JvB;;AkBzgKE;EAKM,mBhBUS;AF8/JjB;;AkB7gKE;EASM,yLAA8M;AlBwgKtN;;AkBngKE;EAGM,8BhBFS;AFsgKjB;;AkB//JE;EAGM,mBhBVS;AF0gKjB;;AkB3/JE;EAEI,aAAa;EACb,WAAW;AlB6/JjB;;AkBhgKE;EAMM,mBhBrBS;EgBsBT,chB8Be;EgB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB8/JxB;;AkBxgKE;EAeI,qBhB9BW;AF2hKjB;;AkB5gKE;EAmBM,qBhBlCS;AF+hKjB;;AkBx/JE;EAGM,mChB1CS;EgB2CT,chBSe;AFg/JvB;;AkBp/JE;EAGM,6CAA4C;AlBq/JpD;;AkBx/JE;EAOM,6CAA4C;AlBq/JpD;;AkBh/JE;EAEI,mBhBViB;EgBWjB,qBhB/DW;AFijKjB;;AkBr/JE;EAMM,mBhBlES;AFqjKjB;;AkB9+JE;EAEI,chBzEW;AFyjKjB;;AkB5+JE;EAGM,mBhBhFS;AF6jKjB;;AkBh/JE;;EAQM,yBhBjCe;AF8gKvB;;AkBr/JE;EAaQ,2ChB1FO;EgB2FP,0ChB3FO;AFukKjB;;AkB1/JE;EAkBQ,yChB/FO;EgBgGP,4ChBhGO;AF4kKjB;;AkBv+JE;EAGM,yChBxGS;AFglKjB;;AkBn+JE;EAGM,mBhBhHS;AFolKjB;;AkBv+JE;EAOM,6ChBpHS;AFwlKjB;;AkB3+JE;EAWM,yBhBxHS;EgByHT,0BhBzHS;AF6lKjB;;AkB/9JE;EAGM,mBhBjIS;EgBkIT,chBlIS;EgBmIT,4ChB/Ee;AF+iKvB;;AkBr+JE;EASM,4DhBnFe;AFmjKvB;;AkB39JE;EAGM,yBhB/IS;EgBgJT,4HhB5Fe;AFwjKvB;;AkBh+JE;EAQM,yFAAqG;EACrG,YAAY;AlB49JpB;;AkBv9JE;EAEI,chB5JW;AFqnKjB;;AkB5oKE;EAGM,mBhBqBS;AFwnKjB;;AkBxoKE;EAEI,mBhBiEiB;AFykKvB;;AkB5oKE;EAKM,mBhBWS;AFgoKjB;;AkBhpKE;EASM,yLAA8M;AlB2oKtN;;AkBtoKE;EAGM,6BhBDS;AFwoKjB;;AkBloKE;EAGM,mBhBTS;AF4oKjB;;AkB9nKE;EAEI,aAAa;EACb,WAAW;AlBgoKjB;;AkBnoKE;EAMM,mBhBpBS;EgBqBT,chB8Be;EgB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBioKxB;;AkB3oKE;EAeI,qBhB7BW;AF6pKjB;;AkB/oKE;EAmBM,qBhBjCS;AFiqKjB;;AkB3nKE;EAGM,kChBzCS;EgB0CT,chBSe;AFmnKvB;;AkBvnKE;EAGM,6CAA4C;AlBwnKpD;;AkB3nKE;EAOM,6CAA4C;AlBwnKpD;;AkBnnKE;EAEI,mBhBViB;EgBWjB,qBhB9DW;AFmrKjB;;AkBxnKE;EAMM,mBhBjES;AFurKjB;;AkBjnKE;EAEI,chBxEW;AF2rKjB;;AkB/mKE;EAGM,mBhB/ES;AF+rKjB;;AkBnnKE;;EAQM,yBhBjCe;AFipKvB;;AkBxnKE;EAaQ,0ChBzFO;EgB0FP,yChB1FO;AFysKjB;;AkB7nKE;EAkBQ,wChB9FO;EgB+FP,2ChB/FO;AF8sKjB;;AkB1mKE;EAGM,wChBvGS;AFktKjB;;AkBtmKE;EAGM,mBhB/GS;AFstKjB;;AkB1mKE;EAOM,6ChBnHS;AF0tKjB;;AkB9mKE;EAWM,yBhBvHS;EgBwHT,0BhBxHS;AF+tKjB;;AkBlmKE;EAGM,mBhBhIS;EgBiIT,chBjIS;EgBkIT,4ChB/Ee;AFkrKvB;;AkBxmKE;EASM,4DhBnFe;AFsrKvB;;AkB9lKE;EAGM,yBhB9IS;EgB+IT,4HhB5Fe;AF2rKvB;;AkBnmKE;EAQM,yFAAqG;EACrG,YAAY;AlB+lKpB;;AkB1lKE;EAEI,chB3JW;AFuvKjB;;AkB/wKE;EAGM,mBhBsBS;AF0vKjB;;AkB3wKE;EAEI,gBhBjBS;AF8xKf;;AkB/wKE;EAKM,mBhBYS;AFkwKjB;;AkBnxKE;EASM,kMAA8M;AlB8wKtN;;AkBzwKE;EAGM,6BhBAS;AF0wKjB;;AkBrwKE;EAGM,mBhBRS;AF8wKjB;;AkBjwKE;EAEI,aAAa;EACb,WAAW;AlBmwKjB;;AkBtwKE;EAMM,mBhBnBS;EgBoBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBowKxB;;AkB9wKE;EAeI,qBhB5BW;AF+xKjB;;AkBlxKE;EAmBM,qBhBhCS;AFmyKjB;;AkB9vKE;EAGM,kChBxCS;EgByCT,WhBzEO;AFw0Kf;;AkB1vKE;EAGM,6CAA4C;AlB2vKpD;;AkB9vKE;EAOM,6CAA4C;AlB2vKpD;;AkBtvKE;EAEI,gBhB5FS;EgB6FT,qBhB7DW;AFqzKjB;;AkB3vKE;EAMM,mBhBhES;AFyzKjB;;AkBpvKE;EAEI,chBvEW;AF6zKjB;;AkBlvKE;EAGM,mBhB9ES;AFi0KjB;;AkBtvKE;;EAQM,sBhBnHO;AFs2Kf;;AkB3vKE;EAaQ,0ChBxFO;EgByFP,yChBzFO;AF20KjB;;AkBhwKE;EAkBQ,wChB7FO;EgB8FP,2ChB9FO;AFg1KjB;;AkB7uKE;EAGM,wChBtGS;AFo1KjB;;AkBzuKE;EAGM,mBhB9GS;AFw1KjB;;AkB7uKE;EAOM,6ChBlHS;AF41KjB;;AkBjvKE;EAWM,yBhBtHS;EgBuHT,0BhBvHS;AFi2KjB;;AkBruKE;EAGM,mBhB/HS;EgBgIT,chBhIS;EgBiIT,sChBjKO;AFu4Kf;;AkB3uKE;EASM,yDhBrKO;AF24Kf;;AkBjuKE;EAGM,yBhB7IS;EgB8IT,kIhB9KO;AFg5Kf;;AkBtuKE;EAQM,+FAAqG;EACrG,YAAY;AlBkuKpB;;AkB7tKE;EAEI,chB1JW;AFy3KjB;;AkBl5KE;EAGM,mBhBuBS;AF43KjB;;AkB94KE;EAEI,gBhBjBS;AFi6Kf;;AkBl5KE;EAKM,mBhBaS;AFo4KjB;;AkBt5KE;EASM,kMAA8M;AlBi5KtN;;AkB54KE;EAGM,8BhBCS;AF44KjB;;AkBx4KE;EAGM,mBhBPS;AFg5KjB;;AkBp4KE;EAEI,aAAa;EACb,WAAW;AlBs4KjB;;AkBz4KE;EAMM,mBhBlBS;EgBmBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBu4KxB;;AkBj5KE;EAeI,qBhB3BW;AFi6KjB;;AkBr5KE;EAmBM,qBhB/BS;AFq6KjB;;AkBj4KE;EAGM,mChBvCS;EgBwCT,WhBzEO;AF28Kf;;AkB73KE;EAGM,6CAA4C;AlB83KpD;;AkBj4KE;EAOM,6CAA4C;AlB83KpD;;AkBz3KE;EAEI,gBhB5FS;EgB6FT,qBhB5DW;AFu7KjB;;AkB93KE;EAMM,mBhB/DS;AF27KjB;;AkBv3KE;EAEI,chBtEW;AF+7KjB;;AkBr3KE;EAGM,mBhB7ES;AFm8KjB;;AkBz3KE;;EAQM,sBhBnHO;AFy+Kf;;AkB93KE;EAaQ,2ChBvFO;EgBwFP,0ChBxFO;AF68KjB;;AkBn4KE;EAkBQ,yChB5FO;EgB6FP,4ChB7FO;AFk9KjB;;AkBh3KE;EAGM,yChBrGS;AFs9KjB;;AkB52KE;EAGM,mBhB7GS;AF09KjB;;AkBh3KE;EAOM,6ChBjHS;AF89KjB;;AkBp3KE;EAWM,yBhBrHS;EgBsHT,0BhBtHS;AFm+KjB;;AkBx2KE;EAGM,mBhB9HS;EgB+HT,chB/HS;EgBgIT,sChBjKO;AF0gLf;;AkB92KE;EASM,yDhBrKO;AF8gLf;;AkBp2KE;EAGM,yBhB5IS;EgB6IT,kIhB9KO;AFmhLf;;AkBz2KE;EAQM,+FAAqG;EACrG,YAAY;AlBq2KpB;;AkBh2KE;EAEI,chBzJW;AF2/KjB;;AkBrhLE;EAGM,mBhBwBS;AF8/KjB;;AkBjhLE;EAEI,gBhBjBS;AFoiLf;;AkBrhLE;EAKM,mBhBcS;AFsgLjB;;AkBzhLE;EASM,kMAA8M;AlBohLtN;;AkB/gLE;EAGM,8BhBES;AF8gLjB;;AkB3gLE;EAGM,mBhBNS;AFkhLjB;;AkBvgLE;EAEI,aAAa;EACb,WAAW;AlBygLjB;;AkB5gLE;EAMM,mBhBjBS;EgBkBT,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB0gLxB;;AkBphLE;EAeI,qBhB1BW;AFmiLjB;;AkBxhLE;EAmBM,qBhB9BS;AFuiLjB;;AkBpgLE;EAGM,mChBtCS;EgBuCT,WhBzEO;AF8kLf;;AkBhgLE;EAGM,6CAA4C;AlBigLpD;;AkBpgLE;EAOM,6CAA4C;AlBigLpD;;AkB5/KE;EAEI,gBhB5FS;EgB6FT,qBhB3DW;AFyjLjB;;AkBjgLE;EAMM,mBhB9DS;AF6jLjB;;AkB1/KE;EAEI,chBrEW;AFikLjB;;AkBx/KE;EAGM,mBhB5ES;AFqkLjB;;AkB5/KE;;EAQM,sBhBnHO;AF4mLf;;AkBjgLE;EAaQ,2ChBtFO;EgBuFP,0ChBvFO;AF+kLjB;;AkBtgLE;EAkBQ,yChB3FO;EgB4FP,4ChB5FO;AFolLjB;;AkBn/KE;EAGM,yChBpGS;AFwlLjB;;AkB/+KE;EAGM,mBhB5GS;AF4lLjB;;AkBn/KE;EAOM,6ChBhHS;AFgmLjB;;AkBv/KE;EAWM,yBhBpHS;EgBqHT,0BhBrHS;AFqmLjB;;AkB3+KE;EAGM,mBhB7HS;EgB8HT,chB9HS;EgB+HT,sChBjKO;AF6oLf;;AkBj/KE;EASM,yDhBrKO;AFipLf;;AkBv+KE;EAGM,yBhB3IS;EgB4IT,kIhB9KO;AFspLf;;AkB5+KE;EAQM,+FAAqG;EACrG,YAAY;AlBw+KpB;;AkBn+KE;EAEI,chBxJW;AF6nLjB;;AkBxpLE;EAGM,gBhBVO;AFmqLf;;AkBppLE;EAEI,mBhBiEiB;AFqlLvB;;AkBxpLE;EAKM,gBhBpBO;AF2qLf;;AkB5pLE;EASM,yLAA8M;AlBupLtN;;AkBlpLE;EAGM,+BhBhCO;AFmrLf;;AkB9oLE;EAGM,gBhBxCO;AFurLf;;AkB1oLE;EAEI,aAAa;EACb,WAAW;AlB4oLjB;;AkB/oLE;EAMM,gBhBnDO;EgBoDP,chB8Be;EgB7Bf,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlB6oLxB;;AkBvpLE;EAeI,kBhB5DS;AFwsLf;;AkB3pLE;EAmBM,kBhBhEO;AF4sLf;;AkBvoLE;EAGM,oChBxEO;EgByEP,chBSe;AF+nLvB;;AkBnoLE;EAGM,0CAA4C;AlBooLpD;;AkBvoLE;EAOM,0CAA4C;AlBooLpD;;AkB/nLE;EAEI,mBhBViB;EgBWjB,kBhB7FS;AF8tLf;;AkBpoLE;EAMM,gBhBhGO;AFkuLf;;AkB7nLE;EAEI,WhBvGS;AFsuLf;;AkB3nLE;EAGM,gBhB9GO;AF0uLf;;AkB/nLE;;EAQM,yBhBjCe;AF6pLvB;;AkBpoLE;EAaQ,4ChBxHK;EgByHL,2ChBzHK;AFovLf;;AkBzoLE;EAkBQ,0ChB7HK;EgB8HL,6ChB9HK;AFyvLf;;AkBtnLE;EAGM,0ChBtIO;AF6vLf;;AkBlnLE;EAGM,gBhB9IO;AFiwLf;;AkBtnLE;EAOM,uChBlJO;AFqwLf;;AkB1nLE;EAWM,sBhBtJO;EgBuJP,uBhBvJO;AF0wLf;;AkB9mLE;EAGM,gBhB/JO;EgBgKP,WhBhKO;EgBiKP,4ChB/Ee;AF8rLvB;;AkBpnLE;EASM,yDhBnFe;AFksLvB;;AkB1mLE;EAGM,sBhB7KO;EgB8KP,sHhB5Fe;AFusLvB;;AkB/mLE;EAQM,yFAAqG;EACrG,YAAY;AlB2mLpB;;AkBtmLE;EAEI,WhB1LS;AFkyLf;;AkB3xLE;EAGM,mBhBJU;AFgyLlB;;AkBvxLE;EAEI,gBhBjBS;AF0yLf;;AkB3xLE;EAKM,mBhBdU;AFwyLlB;;AkB/xLE;EASM,kMAA8M;AlB0xLtN;;AkBrxLE;EAGM,+BhB1BU;AFgzLlB;;AkBjxLE;EAGM,mBhBlCU;AFozLlB;;AkB7wLE;EAEI,aAAa;EACb,WAAW;AlB+wLjB;;AkBlxLE;EAMM,mBhB7CU;EgB8CV,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBgxLxB;;AkB1xLE;EAeI,qBhBtDY;AFq0LlB;;AkB9xLE;EAmBM,qBhB1DU;AFy0LlB;;AkB1wLE;EAGM,oChBlEU;EgBmEV,WhBzEO;AFo1Lf;;AkBtwLE;EAGM,6CAA4C;AlBuwLpD;;AkB1wLE;EAOM,6CAA4C;AlBuwLpD;;AkBlwLE;EAEI,gBhB5FS;EgB6FT,qBhBvFY;AF21LlB;;AkBvwLE;EAMM,mBhB1FU;AF+1LlB;;AkBhwLE;EAEI,chBjGY;AFm2LlB;;AkB9vLE;EAGM,mBhBxGU;AFu2LlB;;AkBlwLE;;EAQM,sBhBnHO;AFk3Lf;;AkBvwLE;EAaQ,4ChBlHQ;EgBmHR,2ChBnHQ;AFi3LlB;;AkB5wLE;EAkBQ,0ChBvHQ;EgBwHR,6ChBxHQ;AFs3LlB;;AkBzvLE;EAGM,0ChBhIU;AF03LlB;;AkBrvLE;EAGM,mBhBxIU;AF83LlB;;AkBzvLE;EAOM,6ChB5IU;AFk4LlB;;AkB7vLE;EAWM,yBhBhJU;EgBiJV,0BhBjJU;AFu4LlB;;AkBjvLE;EAGM,mBhBzJU;EgB0JV,chB1JU;EgB2JV,sChBjKO;AFm5Lf;;AkBvvLE;EASM,yDhBrKO;AFu5Lf;;AkB7uLE;EAGM,yBhBvKU;EgBwKV,kIhB9KO;AF45Lf;;AkBlvLE;EAQM,+FAAqG;EACrG,YAAY;AlB8uLpB;;AkBzuLE;EAEI,chBpLY;AF+5LlB;;AkB95LE;EAGM,mBhBFU;AFi6LlB;;AkB15LE;EAEI,gBhBjBS;AF66Lf;;AkB95LE;EAKM,mBhBZU;AFy6LlB;;AkBl6LE;EASM,kMAA8M;AlB65LtN;;AkBx5LE;EAGM,4BhBxBU;AFi7LlB;;AkBp5LE;EAGM,mBhBhCU;AFq7LlB;;AkBh5LE;EAEI,aAAa;EACb,WAAW;AlBk5LjB;;AkBr5LE;EAMM,mBhB3CU;EgB4CV,WhBpDO;EgBqDP,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AlBm5LxB;;AkB75LE;EAeI,qBhBpDY;AFs8LlB;;AkBj6LE;EAmBM,qBhBxDU;AF08LlB;;AkB74LE;EAGM,iChBhEU;EgBiEV,WhBzEO;AFu9Lf;;AkBz4LE;EAGM,6CAA4C;AlB04LpD;;AkB74LE;EAOM,6CAA4C;AlB04LpD;;AkBr4LE;EAEI,gBhB5FS;EgB6FT,qBhBrFY;AF49LlB;;AkB14LE;EAMM,mBhBxFU;AFg+LlB;;AkBn4LE;EAEI,chB/FY;AFo+LlB;;AkBj4LE;EAGM,mBhBtGU;AFw+LlB;;AkBr4LE;;EAQM,sBhBnHO;AFq/Lf;;AkB14LE;EAaQ,yChBhHQ;EgBiHR,wChBjHQ;AFk/LlB;;AkB/4LE;EAkBQ,uChBrHQ;EgBsHR,0ChBtHQ;AFu/LlB;;AkB53LE;EAGM,uChB9HU;AF2/LlB;;AkBx3LE;EAGM,mBhBtIU;AF+/LlB;;AkB53LE;EAOM,6ChB1IU;AFmgMlB;;AkBh4LE;EAWM,yBhB9IU;EgB+IV,0BhB/IU;AFwgMlB;;AkBp3LE;EAGM,mBhBvJU;EgBwJV,chBxJU;EgByJV,sChBjKO;AFshMf;;AkB13LE;EASM,yDhBrKO;AF0hMf;;AkBh3LE;EAGM,yBhBrKU;EgBsKV,kIhB9KO;AF+hMf;;AkBr3LE;EAQM,+FAAqG;EACrG,YAAY;AlBi3LpB;;AkB52LE;EAEI,chBlLY;AFgiMlB;;AmBnjMA;;;;;;;GnB4jMG;AmBhjMH;EACE,yBjBEgB;EiBDhB,sBjBuMkC;EiBtMlC,eAAe;EACf,cAAc;EACd,qBAAqB;EACrB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;EAChB,sEAAsE;EACtE,yBAAiB;EAAjB,sBAAiB;EAAjB,qBAAiB;EAAjB,iBAAiB;EACjB,sBAAsB;EACtB,UAAU;AnBkjMZ;;AmB/jMA;EAgBI,sBjByLgC;EiBxLhC,qBAAqB;EACrB,MAAM;EACN,+BAA+B;AnBmjMnC;;AmBtkMA;EAwBI,gDjBAa;AFkjMjB;;AmB1kMA;;;EA8BI,sBAAsB;EACtB,eAAe;EACf,mBAAmB;EACnB,eAAe;EACf,gBAAgB;EAChB,mBAAmB;EACnB,qBAAqB;EACrB,sBAAsB;AnBkjM1B;;AmBvlMA;;EA0CI,kBAAkB;EAClB,UAAU;AnBkjMd;;AmB7lMA;;EA8CM,mBjB7CY;EiB8CZ,cjBkCiB;AFkhMvB;;AmBnmMA;;EAoDQ,mBjB5BS;EiB6BT,WjBtDO;AF0mMf;;AmBzmMA;;EAoDQ,mBjB/CU;EiBgDV,WjBtDO;AFgnMf;;AmB/mMA;;EAoDQ,mBjBrBS;EiBsBT,WjBtDO;AFsnMf;;AmBrnMA;;EAoDQ,mBjBnBS;EiBoBT,WjBtDO;AF4nMf;;AmB3nMA;;EAoDQ,mBjBtBS;EiBuBT,cjB4Be;AFgjMvB;;AmBjoMA;;EAoDQ,mBjBxBS;EiByBT,WjBtDO;AFwoMf;;AmBvoMA;;EAoDQ,mBjBpDU;EiBqDV,cjB4Be;AF4jMvB;;AmB7oMA;;EAoDQ,mBjB7CU;EiB8CV,WjBtDO;AFopMf;;AmBnpMA;;EA2DQ,mBbhEW;EaiEX,WjB7DO;AF0pMf;;AmBzpMA;;EA2DQ,mBb/DM;EagEN,WjB7DO;AFgqMf;;AmB/pMA;;EA2DQ,mBb7DO;Ea8DP,WjB7DO;AFsqMf;;AmBrqMA;;EA2DQ,mBb5DM;Ea6DN,cjBqBe;AF0lMvB;;AmB3qMA;;EA2DQ,mBb1DS;Ea2DT,WjB7DO;AFkrMf;;AmBjrMA;;EA2DQ,mBbxDQ;EayDR,WjB7DO;AFwrMf;;AmBvrMA;;EA2DQ,mBjBnCS;EiBoCT,WjB7DO;AF8rMf;;AmB7rMA;;EA2DQ,mBjBlCS;EiBmCT,WjB7DO;AFosMf;;AmBnsMA;;EA2DQ,mBjBjCS;EiBkCT,WjB7DO;AF0sMf;;AmBzsMA;;EA2DQ,mBjBhCS;EiBiCT,WjB7DO;AFgtMf;;AmB/sMA;;EA2DQ,mBjB/BS;EiBgCT,WjB7DO;AFstMf;;AmBrtMA;;EA2DQ,mBjB9BS;EiB+BT,cjBqBe;AF0oMvB;;AmB3tMA;;EA2DQ,mBjB7BS;EiB8BT,cjBqBe;AFgpMvB;;AmBjuMA;;EA2DQ,mBjB5BS;EiB6BT,WjB7DO;AFwuMf;;AmBvuMA;;EA2DQ,mBjB3BS;EiB4BT,WjB7DO;AF8uMf;;AmB7uMA;;EA2DQ,mBjB1BS;EiB2BT,WjB7DO;AFovMf;;AmBnvMA;;EA2DQ,gBjB5DO;EiB6DP,cjBqBe;AFwqMvB;;AmBzvMA;;EA2DQ,mBjBtDU;EiBuDV,WjB7DO;AFgwMf;;AmB/vMA;;EA2DQ,mBjBpDU;EiBqDV,WjB7DO;AFswMf;;AmBrwMA;EAkEI,iCApEyC;EAqEzC,8BArEyC;AnB4wM7C;;AmB1wMA;EAuEI,kCAzEyC;EA0EzC,+BA1EyC;AnBixM7C;;AmB/wMA;;EA6EI,wBAAwB;EACxB,OAAO;EACP,SAAS;EACT,UAAU;EACV,kBAAkB;EAClB,MAAM;EACN,kBAAkB;EAClB,WAAW;AnBusMf;;AmB3xMA;;;EA2FM,kBAAkB;EAClB,gBAAgB;EAChB,oBAAoB;AnBssM1B;;AmBnyMA;;;EAqGM,kBAAkB;EAClB,gBAAgB;EAChB,oBAAoB;AnBosM1B;;AmB3yMA;;;EA+GM,kBAAkB;EAClB,yBAAyB;EACzB,oBAAoB;AnBksM1B;;AmBnzMA;EAwHI,eAAe;AnB+rMnB;;AmBvzMA;;;;;;;EA6HM,eAAe;EACf,yBAAyB;EACzB,WAAW;AnBosMjB;;AmBn0MA;EAoII,2BAA2B;AnBmsM/B;;AmBv0MA;EAyIM,gCAAgG;AnBksMtG;;AmB30MA;EA6IM,gCA/IuC;AnBi1M7C;;AmB/0MA;;EAyJI,kCA3JyC;EA4JzC,+BA5JyC;AnBu1M7C;;AmBr1MA;;EA+JI,iCAjKyC;EAkKzC,8BAlKyC;AnB61M7C;;AmBvrMA;EAEI,qBjBjKc;AF01MlB;;AmB3rMA;;EAMM,yBAAsC;EACtC,WjB5KS;EiB6KT,qBAAkC;AnB0rMxC;;AoB52MA;EACE,uBAAuB;EACvB,uBAAuB;EACvB,sBAAsB;ApB+2MxB;;AoB32MA;EACE,iBAAiB;ApB82MnB;;AoB32MA;EACE,SAAS;EACT,mBAAmB;EACnB,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,UAAU;EACV,kBAAkB;EAClB,UAAU;ApB82MZ;;AoB32MA;EACE,mBlBjBgB;EkBkBhB,0BlBhBgB;EkBiBhB,mBAAmB;ApB82MrB;;AoB12MA;EACE,gBAAgB;EAChB,kBAAkB;ApB62MpB;;AoBz2MA;EAEI,yBAAqC;ApB22MzC;;AoB72MA;;;;;;;EAWI,qBlBnCc;AF+4MlB;;AoBv3MA;;EAeI,yBlBrCc;AFk5MlB;;AoB53MA;EAkBI,yBAAsC;ApB82M1C;;AoBh4MA;EAqBI,yBAAoC;ApB+2MxC", "file": "adminlte.plugins.css", "sourcesContent": ["/*!\n *   AdminLTE v3.1.0-rc\n *     Only Plugins\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../mixins\";\n\n@import \"plugins\";\n", "/*!\n *   AdminLTE v3.1.0-rc\n *     Only Plugins\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n.fc-button {\n  background: #f8f9fa;\n  background-image: none;\n  border-bottom-color: #ddd;\n  border-color: #ddd;\n  color: #495057;\n}\n\n.fc-button:hover, .fc-button:active, .fc-button.hover {\n  background-color: #e9e9e9;\n}\n\n.fc-header-title h2 {\n  color: #666;\n  font-size: 15px;\n  line-height: 1.6em;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  border: 0;\n  width: 100%;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar,\n.fc-toolbar.fc-header-toolbar {\n  margin: 0;\n  padding: 1rem;\n}\n\n@media (max-width: 575.98px) {\n  .fc-toolbar {\n    flex-direction: column;\n  }\n  .fc-toolbar .fc-left {\n    order: 1;\n    margin-bottom: .5rem;\n  }\n  .fc-toolbar .fc-center {\n    order: 0;\n    margin-bottom: .375rem;\n  }\n  .fc-toolbar .fc-right {\n    order: 2;\n  }\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.fc-color-picker > li {\n  float: left;\n  font-size: 30px;\n  line-height: 30px;\n  margin-right: 5px;\n}\n\n.fc-color-picker > li .fa,\n.fc-color-picker > li .fas,\n.fc-color-picker > li .far,\n.fc-color-picker > li .fab,\n.fc-color-picker > li .fal,\n.fc-color-picker > li .fad,\n.fc-color-picker > li .svg-inline--fa,\n.fc-color-picker > li .ion {\n  transition: transform linear .3s;\n}\n\n.fc-color-picker > li .fa:hover,\n.fc-color-picker > li .fas:hover,\n.fc-color-picker > li .far:hover,\n.fc-color-picker > li .fab:hover,\n.fc-color-picker > li .fal:hover,\n.fc-color-picker > li .fad:hover,\n.fc-color-picker > li .svg-inline--fa:hover,\n.fc-color-picker > li .ion:hover {\n  transform: rotate(30deg);\n}\n\n#add-new-event {\n  transition: all linear .3s;\n}\n\n.external-event {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  border-radius: 0.25rem;\n  cursor: move;\n  font-weight: 700;\n  margin-bottom: 4px;\n  padding: 5px 10px;\n}\n\n.external-event:hover {\n  box-shadow: inset 0 0 90px rgba(0, 0, 0, 0.2);\n}\n\n.select2-container--default .select2-selection--single {\n  border: 1px solid #ced4da;\n  padding: 0.46875rem 0.75rem;\n  height: calc(2.25rem + 2px);\n}\n\n.select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-dropdown {\n  border: 1px solid #ced4da;\n}\n\n.select2-container--default .select2-results__option {\n  padding: 6px 12px;\n  user-select: none;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__rendered {\n  padding-left: 0;\n  height: auto;\n  margin-top: -3px;\n}\n\n.select2-container--default[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n  padding-right: 6px;\n  padding-left: 20px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow {\n  height: 31px;\n  right: 6px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow b {\n  margin-top: 0;\n}\n\n.select2-container--default .select2-dropdown .select2-search__field,\n.select2-container--default .select2-search--inline .select2-search__field {\n  border: 1px solid #ced4da;\n}\n\n.select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-dropdown.select2-dropdown--below {\n  border-top: 0;\n}\n\n.select2-container--default .select2-dropdown.select2-dropdown--above {\n  border-bottom: 0;\n}\n\n.select2-container--default .select2-results__option[aria-disabled='true'] {\n  color: #6c757d;\n}\n\n.select2-container--default .select2-results__option[aria-selected='true'] {\n  background-color: #dee2e6;\n}\n\n.select2-container--default .select2-results__option[aria-selected='true'], .select2-container--default .select2-results__option[aria-selected='true']:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-results__option--highlighted {\n  background-color: #5E2D9B;\n  color: #fff;\n}\n\n.select2-container--default .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-selection--multiple {\n  border: 1px solid #ced4da;\n  min-height: calc(2.25rem + 2px);\n}\n\n.select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding: 0 0.375rem 0.375rem;\n  margin-bottom: -0.375rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {\n  width: 100%;\n  margin-left: 0.375rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline .select2-search__field {\n  width: 100% !important;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {\n  border: 0;\n  margin-top: 6px;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #5E2D9B;\n  border-color: #006fe6;\n  color: #fff;\n  padding: 0 10px;\n  margin-top: .31rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n  float: right;\n  margin-left: 5px;\n  margin-right: -2px;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-search.select2-search--inline .select2-search__field, .select2-container--default .select2-selection--multiple.text-sm .select2-search.select2-search--inline .select2-search__field {\n  margin-top: 8px;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--multiple.text-sm .select2-selection__choice {\n  margin-top: .4rem;\n}\n\n.select2-container--default.select2-container--focus .select2-selection--single,\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-container--default.select2-container--focus .select2-search__field {\n  border: 0;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__rendered li {\n  padding-right: 10px;\n}\n\n.input-group-prepend ~ .select2-container--default .select2-selection {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.input-group > .select2-container--default:not(:last-child) .select2-selection {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.select2-container--bootstrap4.select2-container--focus .select2-selection {\n  box-shadow: none;\n}\n\nselect.form-control-sm ~ .select2-container--default {\n  font-size: 0.875rem;\n}\n\n.text-sm .select2-container--default .select2-selection--single,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single {\n  height: calc(1.8125rem + 2px);\n}\n\n.text-sm .select2-container--default .select2-selection--single .select2-selection__rendered,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__rendered {\n  margin-top: -.4rem;\n}\n\n.text-sm .select2-container--default .select2-selection--single .select2-selection__arrow,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__arrow {\n  top: -.12rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple {\n  min-height: calc(1.8125rem + 2px);\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding: 0 0.25rem 0.25rem;\n  margin-top: -0.1rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {\n  margin-left: 0.25rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {\n  margin-top: 6px;\n}\n\n.maximized-card .select2-dropdown {\n  z-index: 9999;\n}\n\n.select2-primary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-primary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-primary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-primary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-primary .select2-search--inline .select2-search__field:focus,\n.select2-primary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-primary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-primary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-primary .select2-results__option--highlighted,\n.select2-primary .select2-container--default .select2-results__option--highlighted {\n  background-color: #5E2D9B;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected]:hover,\n.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple:focus,\n.select2-primary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #5E2D9B;\n  border-color: #006fe6;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-primary.select2-container--focus .select2-selection--multiple,\n.select2-primary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-secondary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-secondary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-secondary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-secondary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-secondary .select2-search--inline .select2-search__field:focus,\n.select2-secondary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-secondary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-secondary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .select2-secondary .select2-results__option--highlighted,\n.select2-secondary .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected]:hover,\n.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple:focus,\n.select2-secondary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary.select2-container--focus .select2-selection--multiple,\n.select2-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.select2-success + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-success + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-success.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-success .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-success .select2-search--inline .select2-search__field:focus,\n.select2-success .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-success .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-success .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #71dd8a;\n}\n\n.select2-container--default .select2-success .select2-results__option--highlighted,\n.select2-success .select2-container--default .select2-results__option--highlighted {\n  background-color: #28a745;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-success .select2-results__option--highlighted[aria-selected]:hover,\n.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #259b40;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple:focus,\n.select2-success .select2-container--default .select2-selection--multiple:focus {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #28a745;\n  border-color: #23923d;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-success.select2-container--focus .select2-selection--multiple,\n.select2-success .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #71dd8a;\n}\n\n.select2-info + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-info + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-info.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-info .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-info .select2-search--inline .select2-search__field:focus,\n.select2-info .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-info .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-info .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #63d9ec;\n}\n\n.select2-container--default .select2-info .select2-results__option--highlighted,\n.select2-info .select2-container--default .select2-results__option--highlighted {\n  background-color: #17a2b8;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-info .select2-results__option--highlighted[aria-selected]:hover,\n.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1596aa;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple:focus,\n.select2-info .select2-container--default .select2-selection--multiple:focus {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #17a2b8;\n  border-color: #148ea1;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-info.select2-container--focus .select2-selection--multiple,\n.select2-info .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #63d9ec;\n}\n\n.select2-warning + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-warning + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-warning.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-warning .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-warning .select2-search--inline .select2-search__field:focus,\n.select2-warning .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-warning .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-warning .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #ffe187;\n}\n\n.select2-container--default .select2-warning .select2-results__option--highlighted,\n.select2-warning .select2-container--default .select2-results__option--highlighted {\n  background-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected]:hover,\n.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7b900;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple:focus,\n.select2-warning .select2-container--default .select2-selection--multiple:focus {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ffc107;\n  border-color: #edb100;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning.select2-container--focus .select2-selection--multiple,\n.select2-warning .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ffe187;\n}\n\n.select2-danger + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-danger + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-danger.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-danger .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-danger .select2-search--inline .select2-search__field:focus,\n.select2-danger .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-danger .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-danger .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #efa2a9;\n}\n\n.select2-container--default .select2-danger .select2-results__option--highlighted,\n.select2-danger .select2-container--default .select2-results__option--highlighted {\n  background-color: #dc3545;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected]:hover,\n.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #da2839;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple:focus,\n.select2-danger .select2-container--default .select2-selection--multiple:focus {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #dc3545;\n  border-color: #d32535;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-danger.select2-container--focus .select2-selection--multiple,\n.select2-danger .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #efa2a9;\n}\n\n.select2-light + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.select2-light + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .select2-light.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-light .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-light .select2-search--inline .select2-search__field:focus,\n.select2-light .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-light .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-light .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .select2-light .select2-results__option--highlighted,\n.select2-light .select2-container--default .select2-results__option--highlighted {\n  background-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-light .select2-results__option--highlighted[aria-selected]:hover,\n.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #eff1f4;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple:focus,\n.select2-light .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light.select2-container--focus .select2-selection--multiple,\n.select2-light .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.select2-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-dark .select2-search--inline .select2-search__field:focus,\n.select2-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .select2-dark .select2-results__option--highlighted,\n.select2-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected]:hover,\n.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple:focus,\n.select2-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-dark.select2-container--focus .select2-selection--multiple,\n.select2-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.select2-lightblue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #99c5de;\n}\n\n.select2-lightblue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #99c5de;\n}\n\n.select2-container--default .select2-lightblue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lightblue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lightblue .select2-search--inline .select2-search__field:focus,\n.select2-lightblue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-lightblue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-lightblue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #99c5de;\n}\n\n.select2-container--default .select2-lightblue .select2-results__option--highlighted,\n.select2-lightblue .select2-container--default .select2-results__option--highlighted {\n  background-color: #3c8dbc;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected]:hover,\n.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #3884b0;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple:focus,\n.select2-lightblue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #99c5de;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3c8dbc;\n  border-color: #367fa9;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue.select2-container--focus .select2-selection--multiple,\n.select2-lightblue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #99c5de;\n}\n\n.select2-navy + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #005ebf;\n}\n\n.select2-navy + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #005ebf;\n}\n\n.select2-container--default .select2-navy.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-navy .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-navy .select2-search--inline .select2-search__field:focus,\n.select2-navy .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-navy .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-navy .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #005ebf;\n}\n\n.select2-container--default .select2-navy .select2-results__option--highlighted,\n.select2-navy .select2-container--default .select2-results__option--highlighted {\n  background-color: #001f3f;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected]:hover,\n.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #001730;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple:focus,\n.select2-navy .select2-container--default .select2-selection--multiple:focus {\n  border-color: #005ebf;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #001f3f;\n  border-color: #001226;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-navy.select2-container--focus .select2-selection--multiple,\n.select2-navy .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #005ebf;\n}\n\n.select2-olive + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #87cfaf;\n}\n\n.select2-olive + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #87cfaf;\n}\n\n.select2-container--default .select2-olive.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-olive .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-olive .select2-search--inline .select2-search__field:focus,\n.select2-olive .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-olive .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-olive .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #87cfaf;\n}\n\n.select2-container--default .select2-olive .select2-results__option--highlighted,\n.select2-olive .select2-container--default .select2-results__option--highlighted {\n  background-color: #3d9970;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected]:hover,\n.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #398e68;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple:focus,\n.select2-olive .select2-container--default .select2-selection--multiple:focus {\n  border-color: #87cfaf;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3d9970;\n  border-color: #368763;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-olive.select2-container--focus .select2-selection--multiple,\n.select2-olive .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #87cfaf;\n}\n\n.select2-lime + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #81ffb8;\n}\n\n.select2-lime + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #81ffb8;\n}\n\n.select2-container--default .select2-lime.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lime .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lime .select2-search--inline .select2-search__field:focus,\n.select2-lime .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-lime .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-lime .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #81ffb8;\n}\n\n.select2-container--default .select2-lime .select2-results__option--highlighted,\n.select2-lime .select2-container--default .select2-results__option--highlighted {\n  background-color: #01ff70;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected]:hover,\n.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #00f169;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple:focus,\n.select2-lime .select2-container--default .select2-selection--multiple:focus {\n  border-color: #81ffb8;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #01ff70;\n  border-color: #00e765;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime.select2-container--focus .select2-selection--multiple,\n.select2-lime .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #81ffb8;\n}\n\n.select2-fuchsia + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f88adf;\n}\n\n.select2-fuchsia + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f88adf;\n}\n\n.select2-container--default .select2-fuchsia.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-fuchsia .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-fuchsia .select2-search--inline .select2-search__field:focus,\n.select2-fuchsia .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-fuchsia .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-fuchsia .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f88adf;\n}\n\n.select2-container--default .select2-fuchsia .select2-results__option--highlighted,\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted {\n  background-color: #f012be;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected]:hover,\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e40eb4;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple:focus,\n.select2-fuchsia .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f88adf;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f012be;\n  border-color: #db0ead;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia.select2-container--focus .select2-selection--multiple,\n.select2-fuchsia .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f88adf;\n}\n\n.select2-maroon + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f083ab;\n}\n\n.select2-maroon + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f083ab;\n}\n\n.select2-container--default .select2-maroon.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-maroon .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-maroon .select2-search--inline .select2-search__field:focus,\n.select2-maroon .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-maroon .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-maroon .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f083ab;\n}\n\n.select2-container--default .select2-maroon .select2-results__option--highlighted,\n.select2-maroon .select2-container--default .select2-results__option--highlighted {\n  background-color: #d81b60;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected]:hover,\n.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #ca195a;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple:focus,\n.select2-maroon .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f083ab;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #d81b60;\n  border-color: #c11856;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon.select2-container--focus .select2-selection--multiple,\n.select2-maroon .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f083ab;\n}\n\n.select2-blue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-blue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-blue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-blue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-blue .select2-search--inline .select2-search__field:focus,\n.select2-blue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-blue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-blue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-blue .select2-results__option--highlighted,\n.select2-blue .select2-container--default .select2-results__option--highlighted {\n  background-color: #5E2D9B;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected]:hover,\n.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple:focus,\n.select2-blue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #5E2D9B;\n  border-color: #006fe6;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-blue.select2-container--focus .select2-selection--multiple,\n.select2-blue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-indigo + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-indigo + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-container--default .select2-indigo.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-indigo .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-indigo .select2-search--inline .select2-search__field:focus,\n.select2-indigo .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-indigo .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-indigo .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b389f9;\n}\n\n.select2-container--default .select2-indigo .select2-results__option--highlighted,\n.select2-indigo .select2-container--default .select2-results__option--highlighted {\n  background-color: #6610f2;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected]:hover,\n.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #5f0de6;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple:focus,\n.select2-indigo .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b389f9;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6610f2;\n  border-color: #5b0cdd;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo.select2-container--focus .select2-selection--multiple,\n.select2-indigo .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b389f9;\n}\n\n.select2-purple + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-purple + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .select2-purple.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-purple .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-purple .select2-search--inline .select2-search__field:focus,\n.select2-purple .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-purple .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-purple .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b8a2e0;\n}\n\n.select2-container--default .select2-purple .select2-results__option--highlighted,\n.select2-purple .select2-container--default .select2-results__option--highlighted {\n  background-color: #6f42c1;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected]:hover,\n.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #683cb8;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple:focus,\n.select2-purple .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6f42c1;\n  border-color: #643ab0;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-purple.select2-container--focus .select2-selection--multiple,\n.select2-purple .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b8a2e0;\n}\n\n.select2-pink + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-pink + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .select2-pink.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-pink .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-pink .select2-search--inline .select2-search__field:focus,\n.select2-pink .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-pink .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-pink .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f6b0d0;\n}\n\n.select2-container--default .select2-pink .select2-results__option--highlighted,\n.select2-pink .select2-container--default .select2-results__option--highlighted {\n  background-color: #e83e8c;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected]:hover,\n.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e63084;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple:focus,\n.select2-pink .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e83e8c;\n  border-color: #e5277e;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-pink.select2-container--focus .select2-selection--multiple,\n.select2-pink .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f6b0d0;\n}\n\n.select2-red + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-red + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-red.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-red .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-red .select2-search--inline .select2-search__field:focus,\n.select2-red .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-red .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-red .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #efa2a9;\n}\n\n.select2-container--default .select2-red .select2-results__option--highlighted,\n.select2-red .select2-container--default .select2-results__option--highlighted {\n  background-color: #dc3545;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-red .select2-results__option--highlighted[aria-selected]:hover,\n.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #da2839;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple:focus,\n.select2-red .select2-container--default .select2-selection--multiple:focus {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #dc3545;\n  border-color: #d32535;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-red.select2-container--focus .select2-selection--multiple,\n.select2-red .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #efa2a9;\n}\n\n.select2-orange + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-orange + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-container--default .select2-orange.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-orange .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-orange .select2-search--inline .select2-search__field:focus,\n.select2-orange .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-orange .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-orange .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #fec392;\n}\n\n.select2-container--default .select2-orange .select2-results__option--highlighted,\n.select2-orange .select2-container--default .select2-results__option--highlighted {\n  background-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected]:hover,\n.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #fd7605;\n  color: #fff;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple:focus,\n.select2-orange .select2-container--default .select2-selection--multiple:focus {\n  border-color: #fec392;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fd7e14;\n  border-color: #f57102;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange.select2-container--focus .select2-selection--multiple,\n.select2-orange .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #fec392;\n}\n\n.select2-yellow + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-yellow + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-yellow.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-yellow .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-yellow .select2-search--inline .select2-search__field:focus,\n.select2-yellow .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-yellow .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-yellow .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #ffe187;\n}\n\n.select2-container--default .select2-yellow .select2-results__option--highlighted,\n.select2-yellow .select2-container--default .select2-results__option--highlighted {\n  background-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected]:hover,\n.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7b900;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple:focus,\n.select2-yellow .select2-container--default .select2-selection--multiple:focus {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ffc107;\n  border-color: #edb100;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow.select2-container--focus .select2-selection--multiple,\n.select2-yellow .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ffe187;\n}\n\n.select2-green + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-green + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-green.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-green .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-green .select2-search--inline .select2-search__field:focus,\n.select2-green .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-green .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-green .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #71dd8a;\n}\n\n.select2-container--default .select2-green .select2-results__option--highlighted,\n.select2-green .select2-container--default .select2-results__option--highlighted {\n  background-color: #28a745;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-green .select2-results__option--highlighted[aria-selected]:hover,\n.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #259b40;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple:focus,\n.select2-green .select2-container--default .select2-selection--multiple:focus {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #28a745;\n  border-color: #23923d;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-green.select2-container--focus .select2-selection--multiple,\n.select2-green .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #71dd8a;\n}\n\n.select2-teal + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-teal + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .select2-teal.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-teal .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-teal .select2-search--inline .select2-search__field:focus,\n.select2-teal .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-teal .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-teal .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #7eeaca;\n}\n\n.select2-container--default .select2-teal .select2-results__option--highlighted,\n.select2-teal .select2-container--default .select2-results__option--highlighted {\n  background-color: #20c997;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected]:hover,\n.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1ebc8d;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple:focus,\n.select2-teal .select2-container--default .select2-selection--multiple:focus {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #20c997;\n  border-color: #1cb386;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-teal.select2-container--focus .select2-selection--multiple,\n.select2-teal .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #7eeaca;\n}\n\n.select2-cyan + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-cyan + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-cyan.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-cyan .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-cyan .select2-search--inline .select2-search__field:focus,\n.select2-cyan .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-cyan .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-cyan .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #63d9ec;\n}\n\n.select2-container--default .select2-cyan .select2-results__option--highlighted,\n.select2-cyan .select2-container--default .select2-results__option--highlighted {\n  background-color: #17a2b8;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected]:hover,\n.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1596aa;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple:focus,\n.select2-cyan .select2-container--default .select2-selection--multiple:focus {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #17a2b8;\n  border-color: #148ea1;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan.select2-container--focus .select2-selection--multiple,\n.select2-cyan .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #63d9ec;\n}\n\n.select2-white + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.select2-white + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .select2-white.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-white .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-white .select2-search--inline .select2-search__field:focus,\n.select2-white .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-white .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-white .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .select2-white .select2-results__option--highlighted,\n.select2-white .select2-container--default .select2-results__option--highlighted {\n  background-color: #fff;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-white .select2-results__option--highlighted[aria-selected]:hover,\n.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7f7f7;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple:focus,\n.select2-white .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fff;\n  border-color: #f2f2f2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white.select2-container--focus .select2-selection--multiple,\n.select2-white .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.select2-gray + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-gray + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-gray.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray .select2-search--inline .select2-search__field:focus,\n.select2-gray .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-gray .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-gray .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .select2-gray .select2-results__option--highlighted,\n.select2-gray .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected]:hover,\n.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple:focus,\n.select2-gray .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-gray.select2-container--focus .select2-selection--multiple,\n.select2-gray .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.select2-gray-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-gray-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray-dark .select2-search--inline .select2-search__field:focus,\n.select2-gray-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-gray-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-gray-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark .select2-results__option--highlighted,\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected]:hover,\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple:focus,\n.select2-gray-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark.select2-container--focus .select2-selection--multiple,\n.select2-gray-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-selection {\n  background-color: #343a40;\n  border-color: #6c757d;\n}\n\n.dark-mode .select2-container--disabled .select2-selection--single {\n  background-color: #454d55;\n}\n\n.dark-mode .select2-selection--single {\n  background-color: #343a40;\n  border-color: #6c757d;\n}\n\n.dark-mode .select2-selection--single .select2-selection__rendered {\n  color: #fff;\n}\n\n.dark-mode .select2-dropdown .select2-search__field,\n.dark-mode .select2-search--inline .select2-search__field {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: white;\n}\n\n.dark-mode .select2-dropdown {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: white;\n}\n\n.dark-mode .select2-results__option[aria-selected=\"true\"] {\n  background-color: #3f474e !important;\n  color: #dee2e6;\n}\n\n.dark-mode .select2-container .select2-search--inline .select2-search__field {\n  background-color: transparent;\n  color: #fff;\n}\n\n.dark-mode .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {\n  color: #fff;\n}\n\n.slider .tooltip.in {\n  opacity: 0.9;\n}\n\n.slider.slider-vertical {\n  height: 100%;\n}\n\n.slider.slider-horizontal {\n  width: 100%;\n}\n\n.slider-primary .slider .slider-selection {\n  background: #5E2D9B;\n}\n\n.slider-secondary .slider .slider-selection {\n  background: #6c757d;\n}\n\n.slider-success .slider .slider-selection {\n  background: #28a745;\n}\n\n.slider-info .slider .slider-selection {\n  background: #17a2b8;\n}\n\n.slider-warning .slider .slider-selection {\n  background: #ffc107;\n}\n\n.slider-danger .slider .slider-selection {\n  background: #dc3545;\n}\n\n.slider-light .slider .slider-selection {\n  background: #f8f9fa;\n}\n\n.slider-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.slider-lightblue .slider .slider-selection {\n  background: #3c8dbc;\n}\n\n.slider-navy .slider .slider-selection {\n  background: #001f3f;\n}\n\n.slider-olive .slider .slider-selection {\n  background: #3d9970;\n}\n\n.slider-lime .slider .slider-selection {\n  background: #01ff70;\n}\n\n.slider-fuchsia .slider .slider-selection {\n  background: #f012be;\n}\n\n.slider-maroon .slider .slider-selection {\n  background: #d81b60;\n}\n\n.slider-blue .slider .slider-selection {\n  background: #5E2D9B;\n}\n\n.slider-indigo .slider .slider-selection {\n  background: #6610f2;\n}\n\n.slider-purple .slider .slider-selection {\n  background: #6f42c1;\n}\n\n.slider-pink .slider .slider-selection {\n  background: #e83e8c;\n}\n\n.slider-red .slider .slider-selection {\n  background: #dc3545;\n}\n\n.slider-orange .slider .slider-selection {\n  background: #fd7e14;\n}\n\n.slider-yellow .slider .slider-selection {\n  background: #ffc107;\n}\n\n.slider-green .slider .slider-selection {\n  background: #28a745;\n}\n\n.slider-teal .slider .slider-selection {\n  background: #20c997;\n}\n\n.slider-cyan .slider .slider-selection {\n  background: #17a2b8;\n}\n\n.slider-white .slider .slider-selection {\n  background: #fff;\n}\n\n.slider-gray .slider .slider-selection {\n  background: #6c757d;\n}\n\n.slider-gray-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.dark-mode .slider-track {\n  background-color: #4b545c;\n  background-image: none;\n}\n\n.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #5E2D9B;\n}\n\n.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #5E2D9B;\n}\n\n.icheck-primary > input:first-child:checked + label::before,\n.icheck-primary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #5E2D9B;\n  border-color: #5E2D9B;\n}\n\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-secondary > input:first-child:checked + label::before,\n.icheck-secondary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.icheck-success > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-success > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-success > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-success > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-success > input:first-child:checked + label::before,\n.icheck-success > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.icheck-info > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-info > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-info > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-info > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-info > input:first-child:checked + label::before,\n.icheck-info > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-warning > input:first-child:checked + label::before,\n.icheck-warning > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-danger > input:first-child:checked + label::before,\n.icheck-danger > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.icheck-light > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-light > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.icheck-light > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-light > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.icheck-light > input:first-child:checked + label::before,\n.icheck-light > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-dark > input:first-child:checked + label::before,\n.icheck-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3c8dbc;\n}\n\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3c8dbc;\n}\n\n.icheck-lightblue > input:first-child:checked + label::before,\n.icheck-lightblue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3c8dbc;\n  border-color: #3c8dbc;\n}\n\n.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #001f3f;\n}\n\n.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #001f3f;\n}\n\n.icheck-navy > input:first-child:checked + label::before,\n.icheck-navy > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #001f3f;\n  border-color: #001f3f;\n}\n\n.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3d9970;\n}\n\n.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3d9970;\n}\n\n.icheck-olive > input:first-child:checked + label::before,\n.icheck-olive > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3d9970;\n  border-color: #3d9970;\n}\n\n.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #01ff70;\n}\n\n.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #01ff70;\n}\n\n.icheck-lime > input:first-child:checked + label::before,\n.icheck-lime > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #01ff70;\n  border-color: #01ff70;\n}\n\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f012be;\n}\n\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f012be;\n}\n\n.icheck-fuchsia > input:first-child:checked + label::before,\n.icheck-fuchsia > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f012be;\n  border-color: #f012be;\n}\n\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #d81b60;\n}\n\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #d81b60;\n}\n\n.icheck-maroon > input:first-child:checked + label::before,\n.icheck-maroon > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #d81b60;\n  border-color: #d81b60;\n}\n\n.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #5E2D9B;\n}\n\n.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #5E2D9B;\n}\n\n.icheck-blue > input:first-child:checked + label::before,\n.icheck-blue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #5E2D9B;\n  border-color: #5E2D9B;\n}\n\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.icheck-indigo > input:first-child:checked + label::before,\n.icheck-indigo > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6610f2;\n  border-color: #6610f2;\n}\n\n.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.icheck-purple > input:first-child:checked + label::before,\n.icheck-purple > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n}\n\n.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.icheck-pink > input:first-child:checked + label::before,\n.icheck-pink > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n}\n\n.icheck-red > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-red > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-red > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-red > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-red > input:first-child:checked + label::before,\n.icheck-red > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.icheck-orange > input:first-child:checked + label::before,\n.icheck-orange > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n}\n\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-yellow > input:first-child:checked + label::before,\n.icheck-yellow > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.icheck-green > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-green > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-green > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-green > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-green > input:first-child:checked + label::before,\n.icheck-green > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.icheck-teal > input:first-child:checked + label::before,\n.icheck-teal > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #20c997;\n  border-color: #20c997;\n}\n\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-cyan > input:first-child:checked + label::before,\n.icheck-cyan > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.icheck-white > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-white > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.icheck-white > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-white > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.icheck-white > input:first-child:checked + label::before,\n.icheck-white > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fff;\n  border-color: #fff;\n}\n\n.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-gray > input:first-child:checked + label::before,\n.icheck-gray > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-gray-dark > input:first-child:checked + label::before,\n.icheck-gray-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.dark-mode [class*=\"icheck-\"] > input:first-child:not(:checked) + input[type=\"hidden\"] + label::before,\n.dark-mode [class*=\"icheck-\"] > input:first-child:not(:checked) + label::before {\n  border-color: #6c757d;\n}\n\n.mapael .map {\n  position: relative;\n}\n\n.mapael .mapTooltip {\n  font-family: \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  background-color: #000;\n  color: #fff;\n  display: block;\n  max-width: 200px;\n  padding: 0.25rem 0.5rem;\n  position: absolute;\n  text-align: center;\n  word-wrap: break-word;\n  z-index: 1070;\n}\n\n.mapael .myLegend {\n  background-color: #f8f9fa;\n  border: 1px solid #adb5bd;\n  padding: 10px;\n  width: 600px;\n}\n\n.mapael .zoomButton {\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  border-radius: 0.25rem;\n  color: #444;\n  cursor: pointer;\n  font-weight: 700;\n  height: 16px;\n  left: 10px;\n  line-height: 14px;\n  padding-left: 1px;\n  position: absolute;\n  text-align: center;\n  top: 0;\n  user-select: none;\n  width: 16px;\n}\n\n.mapael .zoomButton:hover, .mapael .zoomButton:active, .mapael .zoomButton.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.mapael .zoomReset {\n  line-height: 12px;\n  top: 10px;\n}\n\n.mapael .zoomIn {\n  top: 30px;\n}\n\n.mapael .zoomOut {\n  top: 50px;\n}\n\n.jqvmap-zoomin,\n.jqvmap-zoomout {\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  border-radius: 0.25rem;\n  color: #444;\n  height: 15px;\n  width: 15px;\n}\n\n.jqvmap-zoomin:hover, .jqvmap-zoomin:active, .jqvmap-zoomin.hover,\n.jqvmap-zoomout:hover,\n.jqvmap-zoomout:active,\n.jqvmap-zoomout.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.swal2-icon.swal2-info {\n  border-color: ligthen(#17a2b8, 20%);\n  color: #17a2b8;\n}\n\n.swal2-icon.swal2-warning {\n  border-color: ligthen(#ffc107, 20%);\n  color: #ffc107;\n}\n\n.swal2-icon.swal2-error {\n  border-color: ligthen(#dc3545, 20%);\n  color: #dc3545;\n}\n\n.swal2-icon.swal2-question {\n  border-color: ligthen(#6c757d, 20%);\n  color: #6c757d;\n}\n\n.swal2-icon.swal2-success {\n  border-color: ligthen(#28a745, 20%);\n  color: #28a745;\n}\n\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: ligthen(#28a745, 20%);\n}\n\n.swal2-icon.swal2-success [class^='swal2-success-line'] {\n  background-color: #28a745;\n}\n\n.dark-mode .swal2-popup {\n  background-color: #343a40;\n  color: #e9ecef;\n}\n\n.dark-mode .swal2-popup .swal2-content,\n.dark-mode .swal2-popup .swal2-title {\n  color: #e9ecef;\n}\n\n#toast-container .toast {\n  background-color: #5E2D9B;\n}\n\n#toast-container .toast-success {\n  background-color: #28a745;\n}\n\n#toast-container .toast-error {\n  background-color: #dc3545;\n}\n\n#toast-container .toast-info {\n  background-color: #17a2b8;\n}\n\n#toast-container .toast-warning {\n  background-color: #ffc107;\n}\n\n.toast-bottom-full-width .toast,\n.toast-top-full-width .toast {\n  max-width: inherit;\n}\n\n.pace {\n  z-index: 1048;\n}\n\n.pace .pace-progress {\n  z-index: 1049;\n}\n\n.pace .pace-activity {\n  z-index: 1050;\n}\n\n.pace-primary .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-barber-shop-primary .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-primary .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-barber-shop-primary .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-primary .pace .pace-progress::after {\n  color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-bounce-primary .pace .pace-activity {\n  background: #5E2D9B;\n}\n\n.pace-center-atom-primary .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-primary .pace-progress::before {\n  background: #5E2D9B;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-primary .pace-activity {\n  border-color: #5E2D9B;\n}\n\n.pace-center-atom-primary .pace-activity::after, .pace-center-atom-primary .pace-activity::before {\n  border-color: #5E2D9B;\n}\n\n.pace-center-circle-primary .pace .pace-progress {\n  background: rgba(0, 123, 255, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-primary .pace .pace-activity {\n  border-color: #5E2D9B transparent transparent;\n}\n\n.pace-center-radar-primary .pace .pace-activity::before {\n  border-color: #5E2D9B transparent transparent;\n}\n\n.pace-center-simple-primary .pace {\n  background: #fff;\n  border-color: #5E2D9B;\n}\n\n.pace-center-simple-primary .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-material-primary .pace {\n  color: #5E2D9B;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity {\n  background: #5E2D9B;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::after,\n.pace-corner-indicator-primary .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::before {\n  border-right-color: rgba(0, 123, 255, 0.2);\n  border-left-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::after {\n  border-top-color: rgba(0, 123, 255, 0.2);\n  border-bottom-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-fill-left-primary .pace .pace-progress {\n  background-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-flash-primary .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-flash-primary .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #5E2D9B, 0 0 5px #5E2D9B;\n}\n\n.pace-flash-primary .pace .pace-activity {\n  border-top-color: #5E2D9B;\n  border-left-color: #5E2D9B;\n}\n\n.pace-loading-bar-primary .pace .pace-progress {\n  background: #5E2D9B;\n  color: #5E2D9B;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-primary .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #5E2D9B, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-primary .pace .pace-progress {\n  background-color: #5E2D9B;\n  box-shadow: inset -1px 0 #5E2D9B, inset 0 -1px #5E2D9B, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-primary .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-primary .pace-progress {\n  color: #5E2D9B;\n}\n\n.pace-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-secondary .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-secondary .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-secondary .pace .pace-progress::after {\n  color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-bounce-secondary .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-center-atom-secondary .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-secondary .pace-progress::before {\n  background: #6c757d;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-secondary .pace-activity {\n  border-color: #6c757d;\n}\n\n.pace-center-atom-secondary .pace-activity::after, .pace-center-atom-secondary .pace-activity::before {\n  border-color: #6c757d;\n}\n\n.pace-center-circle-secondary .pace .pace-progress {\n  background: rgba(108, 117, 125, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-secondary .pace .pace-activity {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-radar-secondary .pace .pace-activity::before {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-simple-secondary .pace {\n  background: #fff;\n  border-color: #6c757d;\n}\n\n.pace-center-simple-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-material-secondary .pace {\n  color: #6c757d;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::after,\n.pace-corner-indicator-secondary .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::before {\n  border-right-color: rgba(108, 117, 125, 0.2);\n  border-left-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::after {\n  border-top-color: rgba(108, 117, 125, 0.2);\n  border-bottom-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-fill-left-secondary .pace .pace-progress {\n  background-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-flash-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-flash-secondary .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;\n}\n\n.pace-flash-secondary .pace .pace-activity {\n  border-top-color: #6c757d;\n  border-left-color: #6c757d;\n}\n\n.pace-loading-bar-secondary .pace .pace-progress {\n  background: #6c757d;\n  color: #6c757d;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-secondary .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-secondary .pace .pace-progress {\n  background-color: #6c757d;\n  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-secondary .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-secondary .pace-progress {\n  color: #6c757d;\n}\n\n.pace-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-success .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-success .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-success .pace .pace-progress::after {\n  color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-bounce-success .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-center-atom-success .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-success .pace-progress::before {\n  background: #28a745;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-success .pace-activity {\n  border-color: #28a745;\n}\n\n.pace-center-atom-success .pace-activity::after, .pace-center-atom-success .pace-activity::before {\n  border-color: #28a745;\n}\n\n.pace-center-circle-success .pace .pace-progress {\n  background: rgba(40, 167, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-success .pace .pace-activity {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-radar-success .pace .pace-activity::before {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-simple-success .pace {\n  background: #fff;\n  border-color: #28a745;\n}\n\n.pace-center-simple-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-material-success .pace {\n  color: #28a745;\n}\n\n.pace-corner-indicator-success .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-corner-indicator-success .pace .pace-activity::after,\n.pace-corner-indicator-success .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-success .pace .pace-activity::before {\n  border-right-color: rgba(40, 167, 69, 0.2);\n  border-left-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-corner-indicator-success .pace .pace-activity::after {\n  border-top-color: rgba(40, 167, 69, 0.2);\n  border-bottom-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-fill-left-success .pace .pace-progress {\n  background-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-flash-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-flash-success .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;\n}\n\n.pace-flash-success .pace .pace-activity {\n  border-top-color: #28a745;\n  border-left-color: #28a745;\n}\n\n.pace-loading-bar-success .pace .pace-progress {\n  background: #28a745;\n  color: #28a745;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-success .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-success .pace .pace-progress {\n  background-color: #28a745;\n  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-success .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-success .pace-progress {\n  color: #28a745;\n}\n\n.pace-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-info .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-info .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-info .pace .pace-progress::after {\n  color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-bounce-info .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-center-atom-info .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-info .pace-progress::before {\n  background: #17a2b8;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-info .pace-activity {\n  border-color: #17a2b8;\n}\n\n.pace-center-atom-info .pace-activity::after, .pace-center-atom-info .pace-activity::before {\n  border-color: #17a2b8;\n}\n\n.pace-center-circle-info .pace .pace-progress {\n  background: rgba(23, 162, 184, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-info .pace .pace-activity {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-radar-info .pace .pace-activity::before {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-simple-info .pace {\n  background: #fff;\n  border-color: #17a2b8;\n}\n\n.pace-center-simple-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-material-info .pace {\n  color: #17a2b8;\n}\n\n.pace-corner-indicator-info .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-corner-indicator-info .pace .pace-activity::after,\n.pace-corner-indicator-info .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-info .pace .pace-activity::before {\n  border-right-color: rgba(23, 162, 184, 0.2);\n  border-left-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-corner-indicator-info .pace .pace-activity::after {\n  border-top-color: rgba(23, 162, 184, 0.2);\n  border-bottom-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-fill-left-info .pace .pace-progress {\n  background-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-flash-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-flash-info .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;\n}\n\n.pace-flash-info .pace .pace-activity {\n  border-top-color: #17a2b8;\n  border-left-color: #17a2b8;\n}\n\n.pace-loading-bar-info .pace .pace-progress {\n  background: #17a2b8;\n  color: #17a2b8;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-info .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-info .pace .pace-progress {\n  background-color: #17a2b8;\n  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-info .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-info .pace-progress {\n  color: #17a2b8;\n}\n\n.pace-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-warning .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-warning .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-warning .pace .pace-progress::after {\n  color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-bounce-warning .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-center-atom-warning .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-warning .pace-progress::before {\n  background: #ffc107;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-warning .pace-activity {\n  border-color: #ffc107;\n}\n\n.pace-center-atom-warning .pace-activity::after, .pace-center-atom-warning .pace-activity::before {\n  border-color: #ffc107;\n}\n\n.pace-center-circle-warning .pace .pace-progress {\n  background: rgba(255, 193, 7, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-warning .pace .pace-activity {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-radar-warning .pace .pace-activity::before {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-simple-warning .pace {\n  background: #1f2d3d;\n  border-color: #ffc107;\n}\n\n.pace-center-simple-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-material-warning .pace {\n  color: #ffc107;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::after,\n.pace-corner-indicator-warning .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::before {\n  border-right-color: rgba(255, 193, 7, 0.2);\n  border-left-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::after {\n  border-top-color: rgba(255, 193, 7, 0.2);\n  border-bottom-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-fill-left-warning .pace .pace-progress {\n  background-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-flash-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-flash-warning .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;\n}\n\n.pace-flash-warning .pace .pace-activity {\n  border-top-color: #ffc107;\n  border-left-color: #ffc107;\n}\n\n.pace-loading-bar-warning .pace .pace-progress {\n  background: #ffc107;\n  color: #ffc107;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-warning .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-warning .pace .pace-progress {\n  background-color: #ffc107;\n  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-warning .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-warning .pace-progress {\n  color: #ffc107;\n}\n\n.pace-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-danger .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-danger .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-danger .pace .pace-progress::after {\n  color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-bounce-danger .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-center-atom-danger .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-danger .pace-progress::before {\n  background: #dc3545;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-danger .pace-activity {\n  border-color: #dc3545;\n}\n\n.pace-center-atom-danger .pace-activity::after, .pace-center-atom-danger .pace-activity::before {\n  border-color: #dc3545;\n}\n\n.pace-center-circle-danger .pace .pace-progress {\n  background: rgba(220, 53, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-danger .pace .pace-activity {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-radar-danger .pace .pace-activity::before {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-simple-danger .pace {\n  background: #fff;\n  border-color: #dc3545;\n}\n\n.pace-center-simple-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-material-danger .pace {\n  color: #dc3545;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::after,\n.pace-corner-indicator-danger .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::before {\n  border-right-color: rgba(220, 53, 69, 0.2);\n  border-left-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::after {\n  border-top-color: rgba(220, 53, 69, 0.2);\n  border-bottom-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-fill-left-danger .pace .pace-progress {\n  background-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-flash-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-flash-danger .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;\n}\n\n.pace-flash-danger .pace .pace-activity {\n  border-top-color: #dc3545;\n  border-left-color: #dc3545;\n}\n\n.pace-loading-bar-danger .pace .pace-progress {\n  background: #dc3545;\n  color: #dc3545;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-danger .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-danger .pace .pace-progress {\n  background-color: #dc3545;\n  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-danger .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-danger .pace-progress {\n  color: #dc3545;\n}\n\n.pace-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-barber-shop-light .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-barber-shop-light .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-light .pace .pace-progress::after {\n  color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-bounce-light .pace .pace-activity {\n  background: #f8f9fa;\n}\n\n.pace-center-atom-light .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-light .pace-progress::before {\n  background: #f8f9fa;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-light .pace-activity {\n  border-color: #f8f9fa;\n}\n\n.pace-center-atom-light .pace-activity::after, .pace-center-atom-light .pace-activity::before {\n  border-color: #f8f9fa;\n}\n\n.pace-center-circle-light .pace .pace-progress {\n  background: rgba(248, 249, 250, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-light .pace .pace-activity {\n  border-color: #f8f9fa transparent transparent;\n}\n\n.pace-center-radar-light .pace .pace-activity::before {\n  border-color: #f8f9fa transparent transparent;\n}\n\n.pace-center-simple-light .pace {\n  background: #1f2d3d;\n  border-color: #f8f9fa;\n}\n\n.pace-center-simple-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-material-light .pace {\n  color: #f8f9fa;\n}\n\n.pace-corner-indicator-light .pace .pace-activity {\n  background: #f8f9fa;\n}\n\n.pace-corner-indicator-light .pace .pace-activity::after,\n.pace-corner-indicator-light .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-light .pace .pace-activity::before {\n  border-right-color: rgba(248, 249, 250, 0.2);\n  border-left-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-corner-indicator-light .pace .pace-activity::after {\n  border-top-color: rgba(248, 249, 250, 0.2);\n  border-bottom-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-fill-left-light .pace .pace-progress {\n  background-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-flash-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-flash-light .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #f8f9fa, 0 0 5px #f8f9fa;\n}\n\n.pace-flash-light .pace .pace-activity {\n  border-top-color: #f8f9fa;\n  border-left-color: #f8f9fa;\n}\n\n.pace-loading-bar-light .pace .pace-progress {\n  background: #f8f9fa;\n  color: #f8f9fa;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-light .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #f8f9fa, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-light .pace .pace-progress {\n  background-color: #f8f9fa;\n  box-shadow: inset -1px 0 #f8f9fa, inset 0 -1px #f8f9fa, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-light .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-light .pace-progress {\n  color: #f8f9fa;\n}\n\n.pace-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-dark .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-dark .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-dark .pace .pace-progress::after {\n  color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-bounce-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-center-atom-dark .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-dark .pace-progress::before {\n  background: #343a40;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-dark .pace-activity {\n  border-color: #343a40;\n}\n\n.pace-center-atom-dark .pace-activity::after, .pace-center-atom-dark .pace-activity::before {\n  border-color: #343a40;\n}\n\n.pace-center-circle-dark .pace .pace-progress {\n  background: rgba(52, 58, 64, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-dark .pace .pace-activity {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-radar-dark .pace .pace-activity::before {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-simple-dark .pace {\n  background: #fff;\n  border-color: #343a40;\n}\n\n.pace-center-simple-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-material-dark .pace {\n  color: #343a40;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::after,\n.pace-corner-indicator-dark .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::before {\n  border-right-color: rgba(52, 58, 64, 0.2);\n  border-left-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::after {\n  border-top-color: rgba(52, 58, 64, 0.2);\n  border-bottom-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-fill-left-dark .pace .pace-progress {\n  background-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-flash-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-flash-dark .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;\n}\n\n.pace-flash-dark .pace .pace-activity {\n  border-top-color: #343a40;\n  border-left-color: #343a40;\n}\n\n.pace-loading-bar-dark .pace .pace-progress {\n  background: #343a40;\n  color: #343a40;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-dark .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-dark .pace .pace-progress {\n  background-color: #343a40;\n  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-dark .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-dark .pace-progress {\n  color: #343a40;\n}\n\n.pace-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-barber-shop-lightblue .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-barber-shop-lightblue .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-lightblue .pace .pace-progress::after {\n  color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-bounce-lightblue .pace .pace-activity {\n  background: #3c8dbc;\n}\n\n.pace-center-atom-lightblue .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-lightblue .pace-progress::before {\n  background: #3c8dbc;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-lightblue .pace-activity {\n  border-color: #3c8dbc;\n}\n\n.pace-center-atom-lightblue .pace-activity::after, .pace-center-atom-lightblue .pace-activity::before {\n  border-color: #3c8dbc;\n}\n\n.pace-center-circle-lightblue .pace .pace-progress {\n  background: rgba(60, 141, 188, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-lightblue .pace .pace-activity {\n  border-color: #3c8dbc transparent transparent;\n}\n\n.pace-center-radar-lightblue .pace .pace-activity::before {\n  border-color: #3c8dbc transparent transparent;\n}\n\n.pace-center-simple-lightblue .pace {\n  background: #fff;\n  border-color: #3c8dbc;\n}\n\n.pace-center-simple-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-material-lightblue .pace {\n  color: #3c8dbc;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity {\n  background: #3c8dbc;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::after,\n.pace-corner-indicator-lightblue .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::before {\n  border-right-color: rgba(60, 141, 188, 0.2);\n  border-left-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::after {\n  border-top-color: rgba(60, 141, 188, 0.2);\n  border-bottom-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-fill-left-lightblue .pace .pace-progress {\n  background-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-flash-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-flash-lightblue .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #3c8dbc, 0 0 5px #3c8dbc;\n}\n\n.pace-flash-lightblue .pace .pace-activity {\n  border-top-color: #3c8dbc;\n  border-left-color: #3c8dbc;\n}\n\n.pace-loading-bar-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n  color: #3c8dbc;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-lightblue .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #3c8dbc, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-lightblue .pace .pace-progress {\n  background-color: #3c8dbc;\n  box-shadow: inset -1px 0 #3c8dbc, inset 0 -1px #3c8dbc, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-lightblue .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-lightblue .pace-progress {\n  color: #3c8dbc;\n}\n\n.pace-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-barber-shop-navy .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-barber-shop-navy .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-navy .pace .pace-progress::after {\n  color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-bounce-navy .pace .pace-activity {\n  background: #001f3f;\n}\n\n.pace-center-atom-navy .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-navy .pace-progress::before {\n  background: #001f3f;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-navy .pace-activity {\n  border-color: #001f3f;\n}\n\n.pace-center-atom-navy .pace-activity::after, .pace-center-atom-navy .pace-activity::before {\n  border-color: #001f3f;\n}\n\n.pace-center-circle-navy .pace .pace-progress {\n  background: rgba(0, 31, 63, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-navy .pace .pace-activity {\n  border-color: #001f3f transparent transparent;\n}\n\n.pace-center-radar-navy .pace .pace-activity::before {\n  border-color: #001f3f transparent transparent;\n}\n\n.pace-center-simple-navy .pace {\n  background: #fff;\n  border-color: #001f3f;\n}\n\n.pace-center-simple-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-material-navy .pace {\n  color: #001f3f;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity {\n  background: #001f3f;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::after,\n.pace-corner-indicator-navy .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::before {\n  border-right-color: rgba(0, 31, 63, 0.2);\n  border-left-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::after {\n  border-top-color: rgba(0, 31, 63, 0.2);\n  border-bottom-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-fill-left-navy .pace .pace-progress {\n  background-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-flash-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-flash-navy .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #001f3f, 0 0 5px #001f3f;\n}\n\n.pace-flash-navy .pace .pace-activity {\n  border-top-color: #001f3f;\n  border-left-color: #001f3f;\n}\n\n.pace-loading-bar-navy .pace .pace-progress {\n  background: #001f3f;\n  color: #001f3f;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-navy .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #001f3f, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-navy .pace .pace-progress {\n  background-color: #001f3f;\n  box-shadow: inset -1px 0 #001f3f, inset 0 -1px #001f3f, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-navy .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-navy .pace-progress {\n  color: #001f3f;\n}\n\n.pace-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-barber-shop-olive .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-barber-shop-olive .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-olive .pace .pace-progress::after {\n  color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-bounce-olive .pace .pace-activity {\n  background: #3d9970;\n}\n\n.pace-center-atom-olive .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-olive .pace-progress::before {\n  background: #3d9970;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-olive .pace-activity {\n  border-color: #3d9970;\n}\n\n.pace-center-atom-olive .pace-activity::after, .pace-center-atom-olive .pace-activity::before {\n  border-color: #3d9970;\n}\n\n.pace-center-circle-olive .pace .pace-progress {\n  background: rgba(61, 153, 112, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-olive .pace .pace-activity {\n  border-color: #3d9970 transparent transparent;\n}\n\n.pace-center-radar-olive .pace .pace-activity::before {\n  border-color: #3d9970 transparent transparent;\n}\n\n.pace-center-simple-olive .pace {\n  background: #fff;\n  border-color: #3d9970;\n}\n\n.pace-center-simple-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-material-olive .pace {\n  color: #3d9970;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity {\n  background: #3d9970;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::after,\n.pace-corner-indicator-olive .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::before {\n  border-right-color: rgba(61, 153, 112, 0.2);\n  border-left-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::after {\n  border-top-color: rgba(61, 153, 112, 0.2);\n  border-bottom-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-fill-left-olive .pace .pace-progress {\n  background-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-flash-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-flash-olive .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #3d9970, 0 0 5px #3d9970;\n}\n\n.pace-flash-olive .pace .pace-activity {\n  border-top-color: #3d9970;\n  border-left-color: #3d9970;\n}\n\n.pace-loading-bar-olive .pace .pace-progress {\n  background: #3d9970;\n  color: #3d9970;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-olive .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #3d9970, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-olive .pace .pace-progress {\n  background-color: #3d9970;\n  box-shadow: inset -1px 0 #3d9970, inset 0 -1px #3d9970, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-olive .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-olive .pace-progress {\n  color: #3d9970;\n}\n\n.pace-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-barber-shop-lime .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-barber-shop-lime .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-lime .pace .pace-progress::after {\n  color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-bounce-lime .pace .pace-activity {\n  background: #01ff70;\n}\n\n.pace-center-atom-lime .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-lime .pace-progress::before {\n  background: #01ff70;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-lime .pace-activity {\n  border-color: #01ff70;\n}\n\n.pace-center-atom-lime .pace-activity::after, .pace-center-atom-lime .pace-activity::before {\n  border-color: #01ff70;\n}\n\n.pace-center-circle-lime .pace .pace-progress {\n  background: rgba(1, 255, 112, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-lime .pace .pace-activity {\n  border-color: #01ff70 transparent transparent;\n}\n\n.pace-center-radar-lime .pace .pace-activity::before {\n  border-color: #01ff70 transparent transparent;\n}\n\n.pace-center-simple-lime .pace {\n  background: #1f2d3d;\n  border-color: #01ff70;\n}\n\n.pace-center-simple-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-material-lime .pace {\n  color: #01ff70;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity {\n  background: #01ff70;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::after,\n.pace-corner-indicator-lime .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::before {\n  border-right-color: rgba(1, 255, 112, 0.2);\n  border-left-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::after {\n  border-top-color: rgba(1, 255, 112, 0.2);\n  border-bottom-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-fill-left-lime .pace .pace-progress {\n  background-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-flash-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-flash-lime .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #01ff70, 0 0 5px #01ff70;\n}\n\n.pace-flash-lime .pace .pace-activity {\n  border-top-color: #01ff70;\n  border-left-color: #01ff70;\n}\n\n.pace-loading-bar-lime .pace .pace-progress {\n  background: #01ff70;\n  color: #01ff70;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-lime .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #01ff70, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-lime .pace .pace-progress {\n  background-color: #01ff70;\n  box-shadow: inset -1px 0 #01ff70, inset 0 -1px #01ff70, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-lime .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-lime .pace-progress {\n  color: #01ff70;\n}\n\n.pace-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-barber-shop-fuchsia .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-barber-shop-fuchsia .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-fuchsia .pace .pace-progress::after {\n  color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-bounce-fuchsia .pace .pace-activity {\n  background: #f012be;\n}\n\n.pace-center-atom-fuchsia .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-fuchsia .pace-progress::before {\n  background: #f012be;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-fuchsia .pace-activity {\n  border-color: #f012be;\n}\n\n.pace-center-atom-fuchsia .pace-activity::after, .pace-center-atom-fuchsia .pace-activity::before {\n  border-color: #f012be;\n}\n\n.pace-center-circle-fuchsia .pace .pace-progress {\n  background: rgba(240, 18, 190, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-fuchsia .pace .pace-activity {\n  border-color: #f012be transparent transparent;\n}\n\n.pace-center-radar-fuchsia .pace .pace-activity::before {\n  border-color: #f012be transparent transparent;\n}\n\n.pace-center-simple-fuchsia .pace {\n  background: #fff;\n  border-color: #f012be;\n}\n\n.pace-center-simple-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-material-fuchsia .pace {\n  color: #f012be;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity {\n  background: #f012be;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::after,\n.pace-corner-indicator-fuchsia .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::before {\n  border-right-color: rgba(240, 18, 190, 0.2);\n  border-left-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::after {\n  border-top-color: rgba(240, 18, 190, 0.2);\n  border-bottom-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-fill-left-fuchsia .pace .pace-progress {\n  background-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-flash-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-flash-fuchsia .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #f012be, 0 0 5px #f012be;\n}\n\n.pace-flash-fuchsia .pace .pace-activity {\n  border-top-color: #f012be;\n  border-left-color: #f012be;\n}\n\n.pace-loading-bar-fuchsia .pace .pace-progress {\n  background: #f012be;\n  color: #f012be;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-fuchsia .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #f012be, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-fuchsia .pace .pace-progress {\n  background-color: #f012be;\n  box-shadow: inset -1px 0 #f012be, inset 0 -1px #f012be, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-fuchsia .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-fuchsia .pace-progress {\n  color: #f012be;\n}\n\n.pace-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-barber-shop-maroon .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-barber-shop-maroon .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-maroon .pace .pace-progress::after {\n  color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-bounce-maroon .pace .pace-activity {\n  background: #d81b60;\n}\n\n.pace-center-atom-maroon .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-maroon .pace-progress::before {\n  background: #d81b60;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-maroon .pace-activity {\n  border-color: #d81b60;\n}\n\n.pace-center-atom-maroon .pace-activity::after, .pace-center-atom-maroon .pace-activity::before {\n  border-color: #d81b60;\n}\n\n.pace-center-circle-maroon .pace .pace-progress {\n  background: rgba(216, 27, 96, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-maroon .pace .pace-activity {\n  border-color: #d81b60 transparent transparent;\n}\n\n.pace-center-radar-maroon .pace .pace-activity::before {\n  border-color: #d81b60 transparent transparent;\n}\n\n.pace-center-simple-maroon .pace {\n  background: #fff;\n  border-color: #d81b60;\n}\n\n.pace-center-simple-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-material-maroon .pace {\n  color: #d81b60;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity {\n  background: #d81b60;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::after,\n.pace-corner-indicator-maroon .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::before {\n  border-right-color: rgba(216, 27, 96, 0.2);\n  border-left-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::after {\n  border-top-color: rgba(216, 27, 96, 0.2);\n  border-bottom-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-fill-left-maroon .pace .pace-progress {\n  background-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-flash-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-flash-maroon .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #d81b60, 0 0 5px #d81b60;\n}\n\n.pace-flash-maroon .pace .pace-activity {\n  border-top-color: #d81b60;\n  border-left-color: #d81b60;\n}\n\n.pace-loading-bar-maroon .pace .pace-progress {\n  background: #d81b60;\n  color: #d81b60;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-maroon .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #d81b60, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-maroon .pace .pace-progress {\n  background-color: #d81b60;\n  box-shadow: inset -1px 0 #d81b60, inset 0 -1px #d81b60, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-maroon .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-maroon .pace-progress {\n  color: #d81b60;\n}\n\n.pace-blue .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-barber-shop-blue .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-blue .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-barber-shop-blue .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-blue .pace .pace-progress::after {\n  color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-bounce-blue .pace .pace-activity {\n  background: #5E2D9B;\n}\n\n.pace-center-atom-blue .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-blue .pace-progress::before {\n  background: #5E2D9B;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-blue .pace-activity {\n  border-color: #5E2D9B;\n}\n\n.pace-center-atom-blue .pace-activity::after, .pace-center-atom-blue .pace-activity::before {\n  border-color: #5E2D9B;\n}\n\n.pace-center-circle-blue .pace .pace-progress {\n  background: rgba(0, 123, 255, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-blue .pace .pace-activity {\n  border-color: #5E2D9B transparent transparent;\n}\n\n.pace-center-radar-blue .pace .pace-activity::before {\n  border-color: #5E2D9B transparent transparent;\n}\n\n.pace-center-simple-blue .pace {\n  background: #fff;\n  border-color: #5E2D9B;\n}\n\n.pace-center-simple-blue .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-material-blue .pace {\n  color: #5E2D9B;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity {\n  background: #5E2D9B;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::after,\n.pace-corner-indicator-blue .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::before {\n  border-right-color: rgba(0, 123, 255, 0.2);\n  border-left-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::after {\n  border-top-color: rgba(0, 123, 255, 0.2);\n  border-bottom-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-fill-left-blue .pace .pace-progress {\n  background-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-flash-blue .pace .pace-progress {\n  background: #5E2D9B;\n}\n\n.pace-flash-blue .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #5E2D9B, 0 0 5px #5E2D9B;\n}\n\n.pace-flash-blue .pace .pace-activity {\n  border-top-color: #5E2D9B;\n  border-left-color: #5E2D9B;\n}\n\n.pace-loading-bar-blue .pace .pace-progress {\n  background: #5E2D9B;\n  color: #5E2D9B;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-blue .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #5E2D9B, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-blue .pace .pace-progress {\n  background-color: #5E2D9B;\n  box-shadow: inset -1px 0 #5E2D9B, inset 0 -1px #5E2D9B, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-blue .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-blue .pace-progress {\n  color: #5E2D9B;\n}\n\n.pace-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-barber-shop-indigo .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-barber-shop-indigo .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-indigo .pace .pace-progress::after {\n  color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-bounce-indigo .pace .pace-activity {\n  background: #6610f2;\n}\n\n.pace-center-atom-indigo .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-indigo .pace-progress::before {\n  background: #6610f2;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-indigo .pace-activity {\n  border-color: #6610f2;\n}\n\n.pace-center-atom-indigo .pace-activity::after, .pace-center-atom-indigo .pace-activity::before {\n  border-color: #6610f2;\n}\n\n.pace-center-circle-indigo .pace .pace-progress {\n  background: rgba(102, 16, 242, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-indigo .pace .pace-activity {\n  border-color: #6610f2 transparent transparent;\n}\n\n.pace-center-radar-indigo .pace .pace-activity::before {\n  border-color: #6610f2 transparent transparent;\n}\n\n.pace-center-simple-indigo .pace {\n  background: #fff;\n  border-color: #6610f2;\n}\n\n.pace-center-simple-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-material-indigo .pace {\n  color: #6610f2;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity {\n  background: #6610f2;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::after,\n.pace-corner-indicator-indigo .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::before {\n  border-right-color: rgba(102, 16, 242, 0.2);\n  border-left-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::after {\n  border-top-color: rgba(102, 16, 242, 0.2);\n  border-bottom-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-fill-left-indigo .pace .pace-progress {\n  background-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-flash-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-flash-indigo .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6610f2, 0 0 5px #6610f2;\n}\n\n.pace-flash-indigo .pace .pace-activity {\n  border-top-color: #6610f2;\n  border-left-color: #6610f2;\n}\n\n.pace-loading-bar-indigo .pace .pace-progress {\n  background: #6610f2;\n  color: #6610f2;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-indigo .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6610f2, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-indigo .pace .pace-progress {\n  background-color: #6610f2;\n  box-shadow: inset -1px 0 #6610f2, inset 0 -1px #6610f2, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-indigo .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-indigo .pace-progress {\n  color: #6610f2;\n}\n\n.pace-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-barber-shop-purple .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-barber-shop-purple .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-purple .pace .pace-progress::after {\n  color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-bounce-purple .pace .pace-activity {\n  background: #6f42c1;\n}\n\n.pace-center-atom-purple .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-purple .pace-progress::before {\n  background: #6f42c1;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-purple .pace-activity {\n  border-color: #6f42c1;\n}\n\n.pace-center-atom-purple .pace-activity::after, .pace-center-atom-purple .pace-activity::before {\n  border-color: #6f42c1;\n}\n\n.pace-center-circle-purple .pace .pace-progress {\n  background: rgba(111, 66, 193, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-purple .pace .pace-activity {\n  border-color: #6f42c1 transparent transparent;\n}\n\n.pace-center-radar-purple .pace .pace-activity::before {\n  border-color: #6f42c1 transparent transparent;\n}\n\n.pace-center-simple-purple .pace {\n  background: #fff;\n  border-color: #6f42c1;\n}\n\n.pace-center-simple-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-material-purple .pace {\n  color: #6f42c1;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity {\n  background: #6f42c1;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::after,\n.pace-corner-indicator-purple .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::before {\n  border-right-color: rgba(111, 66, 193, 0.2);\n  border-left-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::after {\n  border-top-color: rgba(111, 66, 193, 0.2);\n  border-bottom-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-fill-left-purple .pace .pace-progress {\n  background-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-flash-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-flash-purple .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6f42c1, 0 0 5px #6f42c1;\n}\n\n.pace-flash-purple .pace .pace-activity {\n  border-top-color: #6f42c1;\n  border-left-color: #6f42c1;\n}\n\n.pace-loading-bar-purple .pace .pace-progress {\n  background: #6f42c1;\n  color: #6f42c1;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-purple .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6f42c1, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-purple .pace .pace-progress {\n  background-color: #6f42c1;\n  box-shadow: inset -1px 0 #6f42c1, inset 0 -1px #6f42c1, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-purple .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-purple .pace-progress {\n  color: #6f42c1;\n}\n\n.pace-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-barber-shop-pink .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-barber-shop-pink .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-pink .pace .pace-progress::after {\n  color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-bounce-pink .pace .pace-activity {\n  background: #e83e8c;\n}\n\n.pace-center-atom-pink .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-pink .pace-progress::before {\n  background: #e83e8c;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-pink .pace-activity {\n  border-color: #e83e8c;\n}\n\n.pace-center-atom-pink .pace-activity::after, .pace-center-atom-pink .pace-activity::before {\n  border-color: #e83e8c;\n}\n\n.pace-center-circle-pink .pace .pace-progress {\n  background: rgba(232, 62, 140, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-pink .pace .pace-activity {\n  border-color: #e83e8c transparent transparent;\n}\n\n.pace-center-radar-pink .pace .pace-activity::before {\n  border-color: #e83e8c transparent transparent;\n}\n\n.pace-center-simple-pink .pace {\n  background: #fff;\n  border-color: #e83e8c;\n}\n\n.pace-center-simple-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-material-pink .pace {\n  color: #e83e8c;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity {\n  background: #e83e8c;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::after,\n.pace-corner-indicator-pink .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::before {\n  border-right-color: rgba(232, 62, 140, 0.2);\n  border-left-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::after {\n  border-top-color: rgba(232, 62, 140, 0.2);\n  border-bottom-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-fill-left-pink .pace .pace-progress {\n  background-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-flash-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-flash-pink .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #e83e8c, 0 0 5px #e83e8c;\n}\n\n.pace-flash-pink .pace .pace-activity {\n  border-top-color: #e83e8c;\n  border-left-color: #e83e8c;\n}\n\n.pace-loading-bar-pink .pace .pace-progress {\n  background: #e83e8c;\n  color: #e83e8c;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-pink .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #e83e8c, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-pink .pace .pace-progress {\n  background-color: #e83e8c;\n  box-shadow: inset -1px 0 #e83e8c, inset 0 -1px #e83e8c, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-pink .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-pink .pace-progress {\n  color: #e83e8c;\n}\n\n.pace-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-red .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-red .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-red .pace .pace-progress::after {\n  color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-bounce-red .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-center-atom-red .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-red .pace-progress::before {\n  background: #dc3545;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-red .pace-activity {\n  border-color: #dc3545;\n}\n\n.pace-center-atom-red .pace-activity::after, .pace-center-atom-red .pace-activity::before {\n  border-color: #dc3545;\n}\n\n.pace-center-circle-red .pace .pace-progress {\n  background: rgba(220, 53, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-red .pace .pace-activity {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-radar-red .pace .pace-activity::before {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-simple-red .pace {\n  background: #fff;\n  border-color: #dc3545;\n}\n\n.pace-center-simple-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-material-red .pace {\n  color: #dc3545;\n}\n\n.pace-corner-indicator-red .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-corner-indicator-red .pace .pace-activity::after,\n.pace-corner-indicator-red .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-red .pace .pace-activity::before {\n  border-right-color: rgba(220, 53, 69, 0.2);\n  border-left-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-corner-indicator-red .pace .pace-activity::after {\n  border-top-color: rgba(220, 53, 69, 0.2);\n  border-bottom-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-fill-left-red .pace .pace-progress {\n  background-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-flash-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-flash-red .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;\n}\n\n.pace-flash-red .pace .pace-activity {\n  border-top-color: #dc3545;\n  border-left-color: #dc3545;\n}\n\n.pace-loading-bar-red .pace .pace-progress {\n  background: #dc3545;\n  color: #dc3545;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-red .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-red .pace .pace-progress {\n  background-color: #dc3545;\n  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-red .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-red .pace-progress {\n  color: #dc3545;\n}\n\n.pace-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-barber-shop-orange .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-barber-shop-orange .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-orange .pace .pace-progress::after {\n  color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-bounce-orange .pace .pace-activity {\n  background: #fd7e14;\n}\n\n.pace-center-atom-orange .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-orange .pace-progress::before {\n  background: #fd7e14;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-orange .pace-activity {\n  border-color: #fd7e14;\n}\n\n.pace-center-atom-orange .pace-activity::after, .pace-center-atom-orange .pace-activity::before {\n  border-color: #fd7e14;\n}\n\n.pace-center-circle-orange .pace .pace-progress {\n  background: rgba(253, 126, 20, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-orange .pace .pace-activity {\n  border-color: #fd7e14 transparent transparent;\n}\n\n.pace-center-radar-orange .pace .pace-activity::before {\n  border-color: #fd7e14 transparent transparent;\n}\n\n.pace-center-simple-orange .pace {\n  background: #1f2d3d;\n  border-color: #fd7e14;\n}\n\n.pace-center-simple-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-material-orange .pace {\n  color: #fd7e14;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity {\n  background: #fd7e14;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::after,\n.pace-corner-indicator-orange .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::before {\n  border-right-color: rgba(253, 126, 20, 0.2);\n  border-left-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::after {\n  border-top-color: rgba(253, 126, 20, 0.2);\n  border-bottom-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-fill-left-orange .pace .pace-progress {\n  background-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-flash-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-flash-orange .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #fd7e14, 0 0 5px #fd7e14;\n}\n\n.pace-flash-orange .pace .pace-activity {\n  border-top-color: #fd7e14;\n  border-left-color: #fd7e14;\n}\n\n.pace-loading-bar-orange .pace .pace-progress {\n  background: #fd7e14;\n  color: #fd7e14;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-orange .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #fd7e14, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-orange .pace .pace-progress {\n  background-color: #fd7e14;\n  box-shadow: inset -1px 0 #fd7e14, inset 0 -1px #fd7e14, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-orange .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-orange .pace-progress {\n  color: #fd7e14;\n}\n\n.pace-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-yellow .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-yellow .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-yellow .pace .pace-progress::after {\n  color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-bounce-yellow .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-center-atom-yellow .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-yellow .pace-progress::before {\n  background: #ffc107;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-yellow .pace-activity {\n  border-color: #ffc107;\n}\n\n.pace-center-atom-yellow .pace-activity::after, .pace-center-atom-yellow .pace-activity::before {\n  border-color: #ffc107;\n}\n\n.pace-center-circle-yellow .pace .pace-progress {\n  background: rgba(255, 193, 7, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-yellow .pace .pace-activity {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-radar-yellow .pace .pace-activity::before {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-simple-yellow .pace {\n  background: #1f2d3d;\n  border-color: #ffc107;\n}\n\n.pace-center-simple-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-material-yellow .pace {\n  color: #ffc107;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::after,\n.pace-corner-indicator-yellow .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::before {\n  border-right-color: rgba(255, 193, 7, 0.2);\n  border-left-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::after {\n  border-top-color: rgba(255, 193, 7, 0.2);\n  border-bottom-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-fill-left-yellow .pace .pace-progress {\n  background-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-flash-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-flash-yellow .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;\n}\n\n.pace-flash-yellow .pace .pace-activity {\n  border-top-color: #ffc107;\n  border-left-color: #ffc107;\n}\n\n.pace-loading-bar-yellow .pace .pace-progress {\n  background: #ffc107;\n  color: #ffc107;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-yellow .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-yellow .pace .pace-progress {\n  background-color: #ffc107;\n  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-yellow .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-yellow .pace-progress {\n  color: #ffc107;\n}\n\n.pace-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-green .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-green .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-green .pace .pace-progress::after {\n  color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-bounce-green .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-center-atom-green .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-green .pace-progress::before {\n  background: #28a745;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-green .pace-activity {\n  border-color: #28a745;\n}\n\n.pace-center-atom-green .pace-activity::after, .pace-center-atom-green .pace-activity::before {\n  border-color: #28a745;\n}\n\n.pace-center-circle-green .pace .pace-progress {\n  background: rgba(40, 167, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-green .pace .pace-activity {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-radar-green .pace .pace-activity::before {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-simple-green .pace {\n  background: #fff;\n  border-color: #28a745;\n}\n\n.pace-center-simple-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-material-green .pace {\n  color: #28a745;\n}\n\n.pace-corner-indicator-green .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-corner-indicator-green .pace .pace-activity::after,\n.pace-corner-indicator-green .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-green .pace .pace-activity::before {\n  border-right-color: rgba(40, 167, 69, 0.2);\n  border-left-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-corner-indicator-green .pace .pace-activity::after {\n  border-top-color: rgba(40, 167, 69, 0.2);\n  border-bottom-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-fill-left-green .pace .pace-progress {\n  background-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-flash-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-flash-green .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;\n}\n\n.pace-flash-green .pace .pace-activity {\n  border-top-color: #28a745;\n  border-left-color: #28a745;\n}\n\n.pace-loading-bar-green .pace .pace-progress {\n  background: #28a745;\n  color: #28a745;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-green .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-green .pace .pace-progress {\n  background-color: #28a745;\n  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-green .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-green .pace-progress {\n  color: #28a745;\n}\n\n.pace-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-barber-shop-teal .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-barber-shop-teal .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-teal .pace .pace-progress::after {\n  color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-bounce-teal .pace .pace-activity {\n  background: #20c997;\n}\n\n.pace-center-atom-teal .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-teal .pace-progress::before {\n  background: #20c997;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-teal .pace-activity {\n  border-color: #20c997;\n}\n\n.pace-center-atom-teal .pace-activity::after, .pace-center-atom-teal .pace-activity::before {\n  border-color: #20c997;\n}\n\n.pace-center-circle-teal .pace .pace-progress {\n  background: rgba(32, 201, 151, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-teal .pace .pace-activity {\n  border-color: #20c997 transparent transparent;\n}\n\n.pace-center-radar-teal .pace .pace-activity::before {\n  border-color: #20c997 transparent transparent;\n}\n\n.pace-center-simple-teal .pace {\n  background: #fff;\n  border-color: #20c997;\n}\n\n.pace-center-simple-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-material-teal .pace {\n  color: #20c997;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity {\n  background: #20c997;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::after,\n.pace-corner-indicator-teal .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::before {\n  border-right-color: rgba(32, 201, 151, 0.2);\n  border-left-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::after {\n  border-top-color: rgba(32, 201, 151, 0.2);\n  border-bottom-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-fill-left-teal .pace .pace-progress {\n  background-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-flash-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-flash-teal .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #20c997, 0 0 5px #20c997;\n}\n\n.pace-flash-teal .pace .pace-activity {\n  border-top-color: #20c997;\n  border-left-color: #20c997;\n}\n\n.pace-loading-bar-teal .pace .pace-progress {\n  background: #20c997;\n  color: #20c997;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-teal .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #20c997, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-teal .pace .pace-progress {\n  background-color: #20c997;\n  box-shadow: inset -1px 0 #20c997, inset 0 -1px #20c997, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-teal .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-teal .pace-progress {\n  color: #20c997;\n}\n\n.pace-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-cyan .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-cyan .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-cyan .pace .pace-progress::after {\n  color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-bounce-cyan .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-center-atom-cyan .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-cyan .pace-progress::before {\n  background: #17a2b8;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-cyan .pace-activity {\n  border-color: #17a2b8;\n}\n\n.pace-center-atom-cyan .pace-activity::after, .pace-center-atom-cyan .pace-activity::before {\n  border-color: #17a2b8;\n}\n\n.pace-center-circle-cyan .pace .pace-progress {\n  background: rgba(23, 162, 184, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-cyan .pace .pace-activity {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-radar-cyan .pace .pace-activity::before {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-simple-cyan .pace {\n  background: #fff;\n  border-color: #17a2b8;\n}\n\n.pace-center-simple-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-material-cyan .pace {\n  color: #17a2b8;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::after,\n.pace-corner-indicator-cyan .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::before {\n  border-right-color: rgba(23, 162, 184, 0.2);\n  border-left-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::after {\n  border-top-color: rgba(23, 162, 184, 0.2);\n  border-bottom-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-fill-left-cyan .pace .pace-progress {\n  background-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-flash-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-flash-cyan .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;\n}\n\n.pace-flash-cyan .pace .pace-activity {\n  border-top-color: #17a2b8;\n  border-left-color: #17a2b8;\n}\n\n.pace-loading-bar-cyan .pace .pace-progress {\n  background: #17a2b8;\n  color: #17a2b8;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-cyan .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-cyan .pace .pace-progress {\n  background-color: #17a2b8;\n  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-cyan .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-cyan .pace-progress {\n  color: #17a2b8;\n}\n\n.pace-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-barber-shop-white .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-barber-shop-white .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-white .pace .pace-progress::after {\n  color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-bounce-white .pace .pace-activity {\n  background: #fff;\n}\n\n.pace-center-atom-white .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-white .pace-progress::before {\n  background: #fff;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-white .pace-activity {\n  border-color: #fff;\n}\n\n.pace-center-atom-white .pace-activity::after, .pace-center-atom-white .pace-activity::before {\n  border-color: #fff;\n}\n\n.pace-center-circle-white .pace .pace-progress {\n  background: rgba(255, 255, 255, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-white .pace .pace-activity {\n  border-color: #fff transparent transparent;\n}\n\n.pace-center-radar-white .pace .pace-activity::before {\n  border-color: #fff transparent transparent;\n}\n\n.pace-center-simple-white .pace {\n  background: #1f2d3d;\n  border-color: #fff;\n}\n\n.pace-center-simple-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-material-white .pace {\n  color: #fff;\n}\n\n.pace-corner-indicator-white .pace .pace-activity {\n  background: #fff;\n}\n\n.pace-corner-indicator-white .pace .pace-activity::after,\n.pace-corner-indicator-white .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-white .pace .pace-activity::before {\n  border-right-color: rgba(255, 255, 255, 0.2);\n  border-left-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-corner-indicator-white .pace .pace-activity::after {\n  border-top-color: rgba(255, 255, 255, 0.2);\n  border-bottom-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-fill-left-white .pace .pace-progress {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-flash-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-flash-white .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #fff, 0 0 5px #fff;\n}\n\n.pace-flash-white .pace .pace-activity {\n  border-top-color: #fff;\n  border-left-color: #fff;\n}\n\n.pace-loading-bar-white .pace .pace-progress {\n  background: #fff;\n  color: #fff;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-white .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #fff, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-white .pace .pace-progress {\n  background-color: #fff;\n  box-shadow: inset -1px 0 #fff, inset 0 -1px #fff, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-white .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-white .pace-progress {\n  color: #fff;\n}\n\n.pace-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-gray .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-gray .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-gray .pace .pace-progress::after {\n  color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-bounce-gray .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-center-atom-gray .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-gray .pace-progress::before {\n  background: #6c757d;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-gray .pace-activity {\n  border-color: #6c757d;\n}\n\n.pace-center-atom-gray .pace-activity::after, .pace-center-atom-gray .pace-activity::before {\n  border-color: #6c757d;\n}\n\n.pace-center-circle-gray .pace .pace-progress {\n  background: rgba(108, 117, 125, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-gray .pace .pace-activity {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-radar-gray .pace .pace-activity::before {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-simple-gray .pace {\n  background: #fff;\n  border-color: #6c757d;\n}\n\n.pace-center-simple-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-material-gray .pace {\n  color: #6c757d;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::after,\n.pace-corner-indicator-gray .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::before {\n  border-right-color: rgba(108, 117, 125, 0.2);\n  border-left-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::after {\n  border-top-color: rgba(108, 117, 125, 0.2);\n  border-bottom-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-fill-left-gray .pace .pace-progress {\n  background-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-flash-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-flash-gray .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;\n}\n\n.pace-flash-gray .pace .pace-activity {\n  border-top-color: #6c757d;\n  border-left-color: #6c757d;\n}\n\n.pace-loading-bar-gray .pace .pace-progress {\n  background: #6c757d;\n  color: #6c757d;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-gray .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-gray .pace .pace-progress {\n  background-color: #6c757d;\n  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-gray .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-gray .pace-progress {\n  color: #6c757d;\n}\n\n.pace-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-gray-dark .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-gray-dark .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-gray-dark .pace .pace-progress::after {\n  color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-bounce-gray-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-center-atom-gray-dark .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-gray-dark .pace-progress::before {\n  background: #343a40;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-gray-dark .pace-activity {\n  border-color: #343a40;\n}\n\n.pace-center-atom-gray-dark .pace-activity::after, .pace-center-atom-gray-dark .pace-activity::before {\n  border-color: #343a40;\n}\n\n.pace-center-circle-gray-dark .pace .pace-progress {\n  background: rgba(52, 58, 64, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-gray-dark .pace .pace-activity {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-radar-gray-dark .pace .pace-activity::before {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-simple-gray-dark .pace {\n  background: #fff;\n  border-color: #343a40;\n}\n\n.pace-center-simple-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-material-gray-dark .pace {\n  color: #343a40;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::after,\n.pace-corner-indicator-gray-dark .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::before {\n  border-right-color: rgba(52, 58, 64, 0.2);\n  border-left-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::after {\n  border-top-color: rgba(52, 58, 64, 0.2);\n  border-bottom-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-fill-left-gray-dark .pace .pace-progress {\n  background-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-flash-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-flash-gray-dark .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;\n}\n\n.pace-flash-gray-dark .pace .pace-activity {\n  border-top-color: #343a40;\n  border-left-color: #343a40;\n}\n\n.pace-loading-bar-gray-dark .pace .pace-progress {\n  background: #343a40;\n  color: #343a40;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-gray-dark .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-gray-dark .pace .pace-progress {\n  background-color: #343a40;\n  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-gray-dark .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-gray-dark .pace-progress {\n  color: #343a40;\n}\n\n/**\n  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.\n  *\n  * @version v3.4 (MODDED)\n  * @homepage https://bttstrp.github.io/bootstrap-switch\n  * <AUTHOR> Larentis <<EMAIL>> (http://larentis.eu)\n  * @license MIT\n  */\n.bootstrap-switch {\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  direction: ltr;\n  display: inline-block;\n  line-height: .5rem;\n  overflow: hidden;\n  position: relative;\n  text-align: left;\n  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;\n  user-select: none;\n  vertical-align: middle;\n  z-index: 0;\n}\n\n.bootstrap-switch .bootstrap-switch-container {\n  border-radius: 0.25rem;\n  display: inline-block;\n  top: 0;\n  transform: translate3d(0, 0, 0);\n}\n\n.bootstrap-switch:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on,\n.bootstrap-switch .bootstrap-switch-handle-off,\n.bootstrap-switch .bootstrap-switch-label {\n  box-sizing: border-box;\n  cursor: pointer;\n  display: table-cell;\n  font-size: 1rem;\n  font-weight: 500;\n  line-height: 1.2rem;\n  padding: .25rem .5rem;\n  vertical-align: middle;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on,\n.bootstrap-switch .bootstrap-switch-handle-off {\n  text-align: center;\n  z-index: 1;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default {\n  background: #e9ecef;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {\n  background: #5E2D9B;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-secondary,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-secondary {\n  background: #6c757d;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {\n  background: #28a745;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {\n  background: #17a2b8;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {\n  background: #ffc107;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {\n  background: #dc3545;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-light,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-light {\n  background: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-dark,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lightblue,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lightblue {\n  background: #3c8dbc;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-navy,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-navy {\n  background: #001f3f;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-olive,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-olive {\n  background: #3d9970;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lime,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lime {\n  background: #01ff70;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-fuchsia,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-fuchsia {\n  background: #f012be;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-maroon,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-maroon {\n  background: #d81b60;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-blue,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-blue {\n  background: #5E2D9B;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-indigo,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-indigo {\n  background: #6610f2;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-purple,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-purple {\n  background: #6f42c1;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-pink,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-pink {\n  background: #e83e8c;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-red,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-red {\n  background: #dc3545;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-orange,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-orange {\n  background: #fd7e14;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-yellow,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-yellow {\n  background: #ffc107;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-green,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-green {\n  background: #28a745;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-teal,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-teal {\n  background: #20c997;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-cyan,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-cyan {\n  background: #17a2b8;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-white,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-white {\n  background: #fff;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray {\n  background: #6c757d;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray-dark,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on {\n  border-bottom-left-radius: 0.1rem;\n  border-top-left-radius: 0.1rem;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-off {\n  border-bottom-right-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n\n.bootstrap-switch input[type='radio'],\n.bootstrap-switch input[type='checkbox'] {\n  filter: alpha(opacity=0);\n  left: 0;\n  margin: 0;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  visibility: hidden;\n  z-index: -1;\n}\n\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-label {\n  font-size: .875rem;\n  line-height: 1.5;\n  padding: .1rem .3rem;\n}\n\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-label {\n  font-size: .875rem;\n  line-height: 1.5;\n  padding: .2rem .4rem;\n}\n\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-label {\n  font-size: 1.25rem;\n  line-height: 1.3333333rem;\n  padding: .3rem .5rem;\n}\n\n.bootstrap-switch.bootstrap-switch-disabled, .bootstrap-switch.bootstrap-switch-readonly, .bootstrap-switch.bootstrap-switch-indeterminate {\n  cursor: default;\n}\n\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-label {\n  cursor: default;\n  filter: alpha(opacity=50);\n  opacity: .5;\n}\n\n.bootstrap-switch.bootstrap-switch-animate .bootstrap-switch-container {\n  transition: margin-left .5s;\n}\n\n.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-on {\n  border-radius: 0 0.1rem 0.1rem 0;\n}\n\n.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-off {\n  border-radius: 0.1rem 0 0 0.1rem;\n}\n\n.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-label,\n.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {\n  border-bottom-right-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n\n.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-label,\n.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {\n  border-bottom-left-radius: 0.1rem;\n  border-top-left-radius: 0.1rem;\n}\n\n.dark-mode .bootstrap-switch {\n  border-color: #6c757d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default {\n  background-color: #3a4047;\n  color: #fff;\n  border-color: #454d55;\n}\n\n.jqstooltip {\n  height: auto !important;\n  padding: 5px !important;\n  width: auto !important;\n}\n\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: #f8f9fa;\n  border: 1px dashed #dee2e6;\n  margin-bottom: 10px;\n}\n\n.chart {\n  overflow: hidden;\n  position: relative;\n}\n\n.dark-mode .irs--flat .irs-line {\n  background-color: #4b545c;\n}\n\n.dark-mode .jsgrid-edit-row > .jsgrid-cell,\n.dark-mode .jsgrid-filter-row > .jsgrid-cell,\n.dark-mode .jsgrid-grid-body, .dark-mode .jsgrid-grid-header,\n.dark-mode .jsgrid-header-row > .jsgrid-header-cell,\n.dark-mode .jsgrid-insert-row > .jsgrid-cell,\n.dark-mode .jsgrid-row > .jsgrid-cell,\n.dark-mode .jsgrid-alt-row > .jsgrid-cell {\n  border-color: #6c757d;\n}\n\n.dark-mode .jsgrid-header-row > .jsgrid-header-cell,\n.dark-mode .jsgrid-row > .jsgrid-cell {\n  background-color: #343a40;\n}\n\n.dark-mode .jsgrid-alt-row > .jsgrid-cell {\n  background-color: #3a4047;\n}\n\n.dark-mode .jsgrid-selected-row > .jsgrid-cell {\n  background-color: #3f474e;\n}\n\n/*# sourceMappingURL=adminlte.plugins.css.map */", "//\n// Plugin: Full Calendar\n//\n\n// Buttons\n.fc-button {\n  background: $gray-100;\n  background-image: none;\n  border-bottom-color: #ddd;\n  border-color: #ddd;\n  color: $gray-700;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: #e9e9e9;\n  }\n}\n\n// Calendar title\n.fc-header-title h2 {\n  color: #666;\n  font-size: 15px;\n  line-height: 1.6em;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n// Calendar table header cells\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  border: 0;\n  width: 100%;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar,\n.fc-toolbar.fc-header-toolbar {\n  margin: 0;\n  padding: 1rem;\n}\n\n@include media-breakpoint-down(xs) {\n  .fc-toolbar {\n    flex-direction: column;\n\n    .fc-left {\n      order: 1;\n      margin-bottom: .5rem;\n    }\n\n    .fc-center {\n      order: 0;\n      margin-bottom: .375rem;\n    }\n\n    .fc-right {\n      order: 2;\n    }\n  }\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n\n  > li {\n    float: left;\n    font-size: 30px;\n    line-height: 30px;\n    margin-right: 5px;\n\n    .fa,\n    .fas,\n    .far,\n    .fab,\n    .fal,\n    .fad,\n    .svg-inline--fa,\n    .ion {\n      transition: transform linear .3s;\n\n      &:hover {\n        @include rotate(30deg);\n      }\n    }\n  }\n}\n\n#add-new-event {\n  transition: all linear .3s;\n}\n\n.external-event {\n  @include box-shadow($card-shadow);\n\n  border-radius: $border-radius;\n  cursor: move;\n  font-weight: 700;\n  margin-bottom: 4px;\n  padding: 5px 10px;\n\n  &:hover {\n    @include box-shadow(inset 0 0 90px rgba(0, 0, 0, 0.2));\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n), $grays);\n\n$blue:    #5E2D9B !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1f2d3d !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n  0: 0,\n  1: ($spacer * .25),\n  2: ($spacer * .5),\n  3: $spacer,\n  4: ($spacer * 1.5),\n  5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n  25: 25%,\n  50: 50%,\n  75: 75%,\n  100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer / 2) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  ($spacer / 2) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width / 2) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #f5f5f5, $start: #eee, $stop: $white) {\n  background-color: $color;\n  background-image: gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n}\n\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge(\n  (\n    \"lightblue\": $lightblue,\n    \"navy\": $navy,\n    \"olive\": $olive,\n    \"lime\": $lime,\n    \"fuchsia\": $fuchsia,\n    \"maroon\": $maroon,\n  ),\n  $colors\n);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: .5rem !default;\n$sidebar-padding-y: 0 !default;\n$sidebar-custom-height: 4rem !default;\n$sidebar-custom-height-lg: 6rem !default;\n$sidebar-custom-height-xl: 8rem !default;\n$sidebar-custom-padding-x: .85rem !default;\n$sidebar-custom-padding-y: .5rem !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// When to show the smaller logo\n$screen-header-collapse: map-get($grid-breakpoints, md) !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-link-padding-x: $navbar-padding-x !default;\n$main-header-brand-padding-y: $navbar-brand-padding-y !default;\n$main-header-brand-padding-x: $navbar-padding-x !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: rgba(255, 255, 255, .2) !default;\n$main-header-dark-form-control-focused-bg: rgba(255, 255, 255, .6) !default;\n$main-header-dark-form-control-focused-color: $gray-800 !default;\n$main-header-dark-form-control-border: 0 !default;\n$main-header-dark-form-control-focused-border: 0 !default;\n$main-header-dark-placeholder-color: rgba(255, 255, 255, .6) !default;\n\n$main-header-light-form-control-bg: darken($gray-100, 2%) !default;\n$main-header-light-form-control-focused-bg: $gray-200 !default;\n$main-header-light-form-control-focused-color: $gray-800 !default;\n$main-header-light-form-control-border: 0 !default;\n$main-header-light-form-control-focused-border: 0 !default;\n$main-header-light-placeholder-color: rgba(0, 0, 0, .6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: rgba(255, 255, 255, .1) !default;\n$sidebar-dark-color: #c2c7d0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #c2c7d0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: rgba(255, 255, 255, .9) !default;\n$sidebar-dark-header-color: $white !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: #000 !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n$sidebar-light-header-color: $gray-800 !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n$progress-bar-sm-border-radius: 1px !default;\n$progress-bar-xs-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-height: 250px !default;\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// CHAT WIDGET\n// --------------------------------------------------------\n$attachment-border-radius: 3px !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-sidebar-mini-links: 010 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: .3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n\n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge(\n  (\n    1: unquote(\"0 1px 3px \" + rgba($black, .12) + \", 0 1px 2px \" + rgba($black, .24)),\n    2: unquote(\"0 3px 6px \" + rgba($black, .16) + \", 0 3px 6px \" + rgba($black, .23)),\n    3: unquote(\"0 10px 20px \" + rgba($black, .19) + \", 0 6px 6px \" + rgba($black, .23)),\n    4: unquote(\"0 14px 28px \" + rgba($black, .25) + \", 0 10px 10px \" + rgba($black, .22)),\n    5: unquote(\"0 19px 38px \" + rgba($black, .3) + \", 0 15px 12px \" + rgba($black, .22)),\n  ),\n  $elevations\n);\n\n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0 !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n", "//\n// Plugin: Select2\n//\n\n//Signle select\n// .select2-container--default,\n// .select2-selection {\n//   &.select2-container--focus,\n//   &:focus,\n//   &:active {\n//     outline: none;\n//   }\n// }\n\n.select2-container--default {\n  .select2-selection--single {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n    padding: ($input-padding-y * 1.25) $input-padding-x;\n    height: $input-height;\n  }\n\n  &.select2-container--open {\n    .select2-selection--single {\n      border-color: lighten($primary, 25%);\n    }\n  }\n\n  & .select2-dropdown {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n  }\n\n  & .select2-results__option {\n    padding: 6px 12px;\n    user-select: none;\n  }\n\n  & .select2-selection--single .select2-selection__rendered {\n    padding-left: 0;\n    //padding-right: 0;\n    height: auto;\n    margin-top: -3px;\n  }\n\n  &[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n    padding-right: 6px;\n    padding-left: 20px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow {\n    height: 31px;\n    right: 6px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow b {\n    margin-top: 0;\n  }\n\n  .select2-dropdown,\n  .select2-search--inline {\n    .select2-search__field {\n      border: $input-border-width solid $input-border-color;\n\n      &:focus {\n        outline: none;\n        border: $input-border-width solid $input-focus-border-color;\n      }\n    }\n  }\n\n  .select2-dropdown {\n    &.select2-dropdown--below {\n      border-top: 0;\n    }\n\n    &.select2-dropdown--above {\n      border-bottom: 0;\n    }\n  }\n\n  .select2-results__option {\n    &[aria-disabled='true'] {\n      color: $gray-600;\n    }\n\n    &[aria-selected='true'] {\n      $color: $gray-300;\n\n      background-color: $color;\n\n      &,\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .select2-results__option--highlighted {\n    $color: $primary;\n    background-color: $color;\n    color: color-yiq($color);\n\n    &[aria-selected] {\n      $color: darken($color, 3%);\n\n      &,\n      &:hover {\n        background-color: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  //Multiple select\n  & {\n    .select2-selection--multiple {\n      border: $input-border-width solid $input-border-color;\n      min-height: $input-height;\n\n      &:focus {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-selection__rendered {\n        padding: 0 $input-padding-x / 2 $input-padding-y;\n        margin-bottom: -$input-padding-x / 2;\n\n        li:first-child.select2-search.select2-search--inline {\n          width: 100%;\n          margin-left: $input-padding-x / 2;\n\n          .select2-search__field {\n            width: 100% !important;\n          }\n        }\n\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            border: 0;\n            margin-top: 6px;\n          }\n        }\n      }\n\n      .select2-selection__choice {\n        background-color: $primary;\n        border-color: darken($primary, 5%);\n        color: color-yiq($primary);\n        padding: 0 10px;\n        margin-top: .31rem;\n      }\n\n      .select2-selection__choice__remove {\n        color: rgba(255, 255, 255, 0.7);\n        float: right;\n        margin-left: 5px;\n        margin-right: -2px;\n\n        &:hover {\n          color: $white;\n        }\n      }\n\n      .text-sm &,\n      &.text-sm {\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 8px;\n          }\n        }\n\n        .select2-selection__choice {\n          margin-top: .4rem;\n        }\n      }\n    }\n\n    &.select2-container--focus {\n      .select2-selection--single,\n      .select2-selection--multiple {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-search__field {\n        border: 0;\n      }\n    }\n  }\n\n  & .select2-selection--single .select2-selection__rendered li {\n    padding-right: 10px;\n  }\n\n  .input-group-prepend ~ & {\n    .select2-selection {\n      border-bottom-left-radius: 0;\n      border-top-left-radius: 0;\n    }\n  }\n\n  .input-group > &:not(:last-child) {\n    .select2-selection {\n      border-bottom-right-radius: 0;\n      border-top-right-radius: 0;\n    }\n  }\n}\n\n// Select2 Bootstrap4 Theme overrides\n.select2-container--bootstrap4 {\n  &.select2-container--focus .select2-selection {\n    box-shadow: none;\n  }\n}\n\n// text-sm / form-control-sm override\nselect.form-control-sm ~ {\n  .select2-container--default {\n    font-size: $font-size-sm;\n  }\n}\n\n.text-sm,\nselect.form-control-sm ~ {\n  .select2-container--default {\n    .select2-selection--single {\n      height: $input-height-sm;\n\n      .select2-selection__rendered {\n        margin-top: -.4rem;\n      }\n\n      .select2-selection__arrow {\n        top: -.12rem;\n      }\n    }\n\n    .select2-selection--multiple {\n      min-height: $input-height-sm;\n\n      .select2-selection__rendered {\n        padding: 0 $input-padding-x-sm / 2 $input-padding-y-sm;\n        margin-top: -($input-padding-x-sm / 5);\n\n        li:first-child.select2-search.select2-search--inline {\n          margin-left: $input-padding-x-sm / 2;\n        }\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 6px;\n          }\n        }\n      }\n    }\n  }\n}\n\n// Dropdown Fix inside maximized card\n.maximized-card .select2-dropdown {\n  z-index: 9999;\n}\n\n// Background colors (theme colors)\n@each $name, $color in $theme-colors {\n  @include select2-variant($name, $color);\n}\n\n// Background colors (colors)\n@each $name, $color in $colors {\n  @include select2-variant($name, $color);\n}\n\n.dark-mode {\n  .select2-selection {\n    background-color: $dark;\n    border-color: $gray-600;\n  }\n\n  .select2-container--disabled .select2-selection--single {\n    background-color: lighten($dark, 7.5%);\n  }\n\n  .select2-selection--single {\n    background-color: $dark;\n    border-color: $gray-600;\n\n    .select2-selection__rendered {\n      color: $white;\n    }\n  }\n  .select2-dropdown .select2-search__field,\n  .select2-search--inline .select2-search__field {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: white;\n  }\n  .select2-dropdown {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: white;\n  }\n  .select2-results__option[aria-selected=\"true\"] {\n    background-color: lighten($dark, 5%) !important;\n    color: $gray-300;\n  }\n  .select2-container .select2-search--inline .select2-search__field {\n    background-color: transparent;\n    color: $white;\n  }\n\n  .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {\n    color: $white;\n  }\n}\n", "//\n// General: Mixins\n//\n\n// Select2 Variant\n@mixin select2-variant($name, $color) {\n  .select2-#{$name} {\n\n    + .select2-container--default {\n      &.select2-container--open {\n        .select2-selection--single {\n          border-color: lighten($color, 25%);\n        }\n      }\n\n      &.select2-container--focus .select2-selection--single {\n        border-color: lighten($color, 25%);\n      }\n    }\n\n    .select2-container--default &,\n    .select2-container--default {\n      &.select2-dropdown,\n      .select2-dropdown,\n      .select2-search--inline {\n        .select2-search__field {\n          &:focus {\n            border: $input-border-width solid lighten($color, 25%);\n          }\n        }\n      }\n\n      .select2-results__option--highlighted {\n        background-color: $color;\n        color: color-yiq($color);\n\n        &[aria-selected] {\n          &,\n          &:hover {\n            background-color: darken($color, 3%);\n            color: color-yiq(darken($color, 3%));\n          }\n        }\n      }\n\n      //Multiple select\n      & {\n        .select2-selection--multiple {\n          &:focus {\n            border-color: lighten($color, 25%);\n          }\n\n          .select2-selection__choice {\n            background-color: $color;\n            border-color: darken($color, 5%);\n            color: color-yiq($color);\n          }\n\n          .select2-selection__choice__remove {\n            color: rgba(color-yiq($color), 0.7);\n\n            &:hover {\n              color: color-yiq($color);\n            }\n          }\n        }\n\n        &.select2-container--focus .select2-selection--multiple {\n          border-color: lighten($color, 25%);\n        }\n      }\n    }\n  }\n}\n", "//\n// Plugin: Bootstrap Slider\n//\n\n// Tooltip fix\n.slider .tooltip.in {\n  opacity: $tooltip-opacity;\n}\n\n// Style override\n.slider {\n  &.slider-vertical {\n    height: 100%;\n  }\n  &.slider-horizontal {\n    width: 100%;\n  }\n}\n\n// Colors\n@each $name, $color in $theme-colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n\n@each $name, $color in $colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n\n.dark-mode {\n  .slider-track {\n    background-color: lighten($dark, 10%);\n    background-image: none;\n  }\n}\n", "//\n// Plugin: iCheck Bootstrap\n//\n\n// iCheck colors (theme colors)\n@each $name, $color in $theme-colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n\n// iCheck colors (colors)\n@each $name, $color in $colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n\n.dark-mode {\n  [class*=\"icheck-\"] > input:first-child:not(:checked) {\n    + input[type=\"hidden\"] + label::before,\n    + label::before {\n      border-color: $gray-600;\n    }\n  }\n}\n", "//\n// Plugins: jQ<PERSON>y <PERSON>\n//\n\n.mapael {\n  .map {\n    position: relative;\n  }\n\n  .mapTooltip {\n    @include reset-text();\n    @include border-radius($tooltip-border-radius);\n    @include font-size($tooltip-font-size);\n    background-color: $tooltip-bg;\n    color: $tooltip-color;\n    display: block;\n    max-width: $tooltip-max-width;\n    padding: $tooltip-padding-y $tooltip-padding-x;\n    position: absolute;\n    text-align: center;\n    word-wrap: break-word;\n    z-index: $zindex-tooltip;\n  }\n\n  .myLegend {\n    background-color: $gray-100;\n    border: 1px solid $gray-500;\n    padding: 10px;\n    width: 600px;\n  }\n\n  .zoomButton {\n    background-color: $button-default-background-color;\n    border: 1px solid $button-default-border-color;\n    border-radius: $btn-border-radius;\n    color: $button-default-color;\n    cursor: pointer;\n    font-weight: 700;\n    height: 16px;\n    left: 10px;\n    line-height: 14px;\n    padding-left: 1px;\n    position: absolute;\n    text-align: center;\n    top: 0;\n\n    user-select: none;\n    width: 16px;\n\n    &:hover,\n    &:active,\n    &.hover {\n      background-color: darken($button-default-background-color, 5%);\n      color: darken($button-default-color, 10%);\n    }\n  }\n\n  .zoomReset {\n    line-height: 12px;\n    top: 10px;\n  }\n\n  .zoomIn {\n    top: 30px;\n  }\n\n  .zoomOut {\n    top: 50px;\n  }\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "//\n// Plugins: JQVMap\n//\n\n// Zoom Button size fixes\n.jqvmap-zoomin,\n.jqvmap-zoomout {\n  background-color: $button-default-background-color;\n  border: 1px solid $button-default-border-color;\n  border-radius: $btn-border-radius;\n  color: $button-default-color;\n  height: 15px;\n  width: 15px;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken($button-default-background-color, 5%);\n    color: darken($button-default-color, 10%);\n  }\n}\n", "//\n// Plugin: SweetAlert2\n//\n\n// Icon Colors\n.swal2-icon {\n  &.swal2-info {\n    border-color: ligthen($info, 20%);\n    color: $info;\n  }\n\n  &.swal2-warning {\n    border-color: ligthen($warning, 20%);\n    color: $warning;\n  }\n\n  &.swal2-error {\n    border-color: ligthen($danger, 20%);\n    color: $danger;\n  }\n\n  &.swal2-question {\n    border-color: ligthen($secondary, 20%);\n    color: $secondary;\n  }\n\n  &.swal2-success {\n    border-color: ligthen($success, 20%);\n    color: $success;\n\n    .swal2-success-ring {\n      border-color: ligthen($success, 20%);\n    }\n\n    [class^='swal2-success-line'] {\n      background-color: $success;\n    }\n  }\n}\n\n.dark-mode {\n  .swal2-popup {\n    background-color: $dark;\n    color: $gray-200;\n\n    .swal2-content,\n    .swal2-title {\n      color: $gray-200;\n    }\n  }\n}\n", "//\n// Plugin: Toastr\n//\n\n// Background to FontAwesome Icons\n// #toast-container > .toast {\n//     background-image: none !important;\n// }\n// #toast-container > .toast .toast-message:before {\n//     font-family: 'Font Awesome 5 Free';\n//     font-size: 24px;\n//     font-weight: 900;\n//     line-height: 18px;\n//     float: left;\n//     color: $white;\n//     padding-right: 0.5em;\n//     margin: auto 0.5em auto -1.5em;\n// }\n// #toast-container > .toast-warning .toast-message:before {\n//     content: \"\\f06a\";\n// }\n// #toast-container > .toast-error .toast-message:before {\n//     content: \"\\f071\";\n// }\n// #toast-container > .toast-info .toast-message:before {\n//     content: \"\\f05a\";\n// }\n// #toast-container > .toast-success .toast-message:before {\n//     content: \"\\f058\";\n// }\n\n\n#toast-container {\n  // Background color\n  .toast {\n    background-color: $primary;\n  }\n\n  .toast-success {\n    background-color: $success;\n  }\n\n  .toast-error {\n    background-color: $danger;\n  }\n\n  .toast-info {\n    background-color: $info;\n  }\n\n  .toast-warning {\n    background-color: $warning;\n  }\n}\n\n// full width fix\n.toast-bottom-full-width .toast,\n.toast-top-full-width .toast {\n  max-width: inherit;\n}\n", "//\n// Plugin: Pace\n//\n\n.pace {\n  z-index: $zindex-main-sidebar + 10;\n\n  .pace-progress {\n    z-index: $zindex-main-sidebar + 11;\n  }\n\n  .pace-activity {\n    z-index: $zindex-main-sidebar + 12;\n  }\n}\n\n// Mixin\n@mixin pace-variant($name, $color) {\n  .pace-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-barber-shop-#{$name} {\n    .pace {\n      background: color-yiq($color);\n\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-activity {\n        background-image: linear-gradient(45deg, rgba(color-yiq($color), 0.2) 25%, transparent 25%, transparent 50%, rgba(color-yiq($color), 0.2) 50%, rgba(color-yiq($color), 0.2) 75%, transparent 75%, transparent);\n      }\n    }\n  }\n\n  .pace-big-counter-#{$name} {\n    .pace {\n      .pace-progress::after {\n        color: rgba($color, .19999999999999996);\n      }\n    }\n  }\n\n  .pace-bounce-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-center-atom-#{$name} {\n    .pace-progress {\n      height: 100px;\n      width: 80px;\n\n      &::before {\n        background: $color;\n        color: color-yiq($color);\n        font-size: .8rem;\n        line-height: .7rem;\n        padding-top: 17%;\n      }\n    }\n\n    .pace-activity {\n      border-color: $color;\n\n      &::after,\n      &::before {\n        border-color: $color;\n      }\n    }\n  }\n\n  .pace-center-circle-#{$name} {\n    .pace {\n      .pace-progress {\n        background: rgba($color, .8);\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .pace-center-radar-#{$name} {\n    .pace {\n      .pace-activity {\n        border-color: $color transparent transparent;\n      }\n\n      .pace-activity::before {\n        border-color: $color transparent transparent;\n      }\n    }\n  }\n\n  .pace-center-simple-#{$name} {\n    .pace {\n      background: color-yiq($color);\n      border-color: $color;\n\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-material-#{$name} {\n    .pace {\n      color: $color;\n    }\n  }\n\n  .pace-corner-indicator-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n\n      .pace-activity::after,\n      .pace-activity::before {\n        border: 5px solid color-yiq($color);\n      }\n\n\n      .pace-activity::before {\n          border-right-color: rgba($color, .2);\n          border-left-color: rgba($color, .2);\n      }\n\n      .pace-activity::after {\n          border-top-color: rgba($color, .2);\n          border-bottom-color: rgba($color, .2);\n      }\n    }\n  }\n\n  .pace-fill-left-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: rgba($color, 0.19999999999999996);\n      }\n    }\n  }\n\n  .pace-flash-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-progress-inner {\n        box-shadow: 0 0 10px $color, 0 0 5px $color;\n      }\n\n      .pace-activity {\n        border-top-color: $color;\n        border-left-color: $color;\n      }\n    }\n  }\n\n  .pace-loading-bar-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n        color: $color;\n        box-shadow: 120px 0 color-yiq($color), 240px 0 color-yiq($color);\n      }\n\n      .pace-activity {\n        box-shadow: inset 0 0 0 2px $color, inset 0 0 0 7px color-yiq($color);\n      }\n    }\n  }\n\n  .pace-mac-osx-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: $color;\n        box-shadow: inset -1px 0 $color, inset 0 -1px $color, inset 0 2px rgba(color-yiq($color), 0.5), inset 0 6px rgba(color-yiq($color), .3);\n      }\n\n      .pace-activity {\n        background-image: radial-gradient(rgba(color-yiq($color), .65) 0%, rgba(color-yiq($color), .15) 100%);\n        height: 12px;\n      }\n    }\n  }\n\n  .pace-progress-color-#{$name} {\n    .pace-progress {\n      color: $color;\n    }\n  }\n}\n\n\n@each $name, $color in $theme-colors {\n  @include pace-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include pace-variant($name, $color);\n}\n\n", "/**\n  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.\n  *\n  * @version v3.4 (MODDED)\n  * @homepage https://bttstrp.github.io/bootstrap-switch\n  * <AUTHOR> <<EMAIL>> (http://larentis.eu)\n  * @license MIT\n  */\n\n$bootstrap-switch-border-radius: $btn-border-radius;\n$bootstrap-switch-handle-border-radius: .1rem;\n\n.bootstrap-switch {\n  border: $input-border-width solid $input-border-color;\n  border-radius: $bootstrap-switch-border-radius;\n  cursor: pointer;\n  direction: ltr;\n  display: inline-block;\n  line-height: .5rem;\n  overflow: hidden;\n  position: relative;\n  text-align: left;\n  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;\n  user-select: none;\n  vertical-align: middle;\n  z-index: 0;\n\n  .bootstrap-switch-container {\n    border-radius: $bootstrap-switch-border-radius;\n    display: inline-block;\n    top: 0;\n    transform: translate3d(0, 0, 0);\n\n  }\n\n  &:focus-within {\n    box-shadow: $input-btn-focus-box-shadow;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off,\n  .bootstrap-switch-label {\n    box-sizing: border-box;\n    cursor: pointer;\n    display: table-cell;\n    font-size: 1rem;\n    font-weight: 500;\n    line-height: 1.2rem;\n    padding: .25rem .5rem;\n    vertical-align: middle;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off {\n    text-align: center;\n    z-index: 1;\n\n    &.bootstrap-switch-default {\n      background: $gray-200;\n      color: color-yiq($gray-200);\n    }\n\n    @each $name, $color in $theme-colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n\n    @each $name, $color in $colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .bootstrap-switch-handle-on {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  .bootstrap-switch-handle-off {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  input[type='radio'],\n  input[type='checkbox'] {\n    filter: alpha(opacity=0);\n    left: 0;\n    margin: 0;\n    opacity: 0;\n    position: absolute;\n    top: 0;\n    visibility: hidden;\n    z-index: -1;\n  }\n\n  &.bootstrap-switch-mini {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .1rem .3rem;\n    }\n  }\n\n  &.bootstrap-switch-small {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .2rem .4rem;\n    }\n  }\n\n  &.bootstrap-switch-large {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: 1.25rem;\n      line-height: 1.3333333rem;\n      padding: .3rem .5rem;\n    }\n  }\n\n  &.bootstrap-switch-disabled,\n  &.bootstrap-switch-readonly,\n  &.bootstrap-switch-indeterminate {\n    cursor: default;\n\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      cursor: default;\n      filter: alpha(opacity=50);\n      opacity: .5;\n    }\n  }\n\n  &.bootstrap-switch-animate .bootstrap-switch-container {\n    transition: margin-left .5s;\n  }\n\n  &.bootstrap-switch-inverse {\n    .bootstrap-switch-handle-on {\n      border-radius: 0 $bootstrap-switch-handle-border-radius $bootstrap-switch-handle-border-radius 0;\n    }\n\n    .bootstrap-switch-handle-off {\n      border-radius: $bootstrap-switch-handle-border-radius 0 0 $bootstrap-switch-handle-border-radius;\n    }\n  }\n\n  // &.bootstrap-switch-focused {\n  //   border-color: $input-btn-focus-color;\n  //   box-shadow: $input-btn-focus-box-shadow;\n  //   outline: 0;\n  // }\n\n  &.bootstrap-switch-on .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  &.bootstrap-switch-off .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n}\n\n.dark-mode {\n  .bootstrap-switch {\n    border-color: $gray-600;\n\n    .bootstrap-switch-handle-off.bootstrap-switch-default,\n    .bootstrap-switch-handle-on.bootstrap-switch-default {\n      background-color: lighten($dark, 2.5%);\n      color: $white;\n      border-color: lighten($dark, 7.5%);\n    }\n  }\n}\n", "//\n// Plugins: Miscellaneous\n// Old plugin codes\n//\n\n// _fix for sparkline tooltip\n.jqstooltip {\n  height: auto !important;\n  padding: 5px !important;\n  width: auto !important;\n}\n\n// jQueryUI\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: $gray-100;\n  border: 1px dashed $gray-300;\n  margin-bottom: 10px;\n}\n\n// Charts\n.chart {\n  overflow: hidden;\n  position: relative;\n}\n\n\n.dark-mode {\n  .irs--flat .irs-line {\n    background-color: lighten($dark, 10%);\n  }\n  .jsgrid-edit-row > .jsgrid-cell,\n  .jsgrid-filter-row > .jsgrid-cell,\n  .jsgrid-grid-body, .jsgrid-grid-header,\n  .jsgrid-header-row > .jsgrid-header-cell,\n  .jsgrid-insert-row > .jsgrid-cell,\n  .jsgrid-row > .jsgrid-cell,\n  .jsgrid-alt-row > .jsgrid-cell {\n    border-color: $gray-600;\n  }\n  .jsgrid-header-row > .jsgrid-header-cell,\n  .jsgrid-row > .jsgrid-cell {\n    background-color: $dark;\n  }\n  .jsgrid-alt-row > .jsgrid-cell {\n    background-color: lighten($dark, 2.5%);\n  }\n  .jsgrid-selected-row > .jsgrid-cell {\n    background-color: lighten($dark, 5%);\n  }\n}\n"]}