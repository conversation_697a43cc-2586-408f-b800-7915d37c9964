* {
    direction: rtl !important;
}

.layout-fixed .main-sidebar {
    right: 0;
}

.brand-image {
    float: right;
}

.content-wrapper,
.main-footer,
.main-header {
    margin-left: 0 !important;
    margin-right: 250px;
}

.mr-auto-navbav {
    margin-right: auto!important;
}

.navbar-expand .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem;
}

[class*=icheck-]>input:first-child:checked+input[type=hidden]+label::after,
[class*=icheck-]>input:first-child:checked+label::after {
    right: 15px;
    left: auto;
}

.nav-sidebar .nav-link>.right,
.nav-sidebar .nav-link>p>.right {
    left: 1rem;
    right: auto;
}

.nav-sidebar .nav-link>.right:nth-child(2),
.nav-sidebar .nav-link>p>.right:nth-child(2) {
    left: 2.2rem;
    right: auto;
}

.small-box .icon>i {
    left: 15px;
    right: auto;
}

@media (min-width: 992px) {
    .sidebar-mini.sidebar-collapse .content-wrapper,
    .sidebar-mini.sidebar-collapse .main-footer,
    .sidebar-mini.sidebar-collapse .main-header {
        margin-right: 4.6rem!important;
    }
    .sidebar-mini.sidebar-collapse .content-wrapper,
    .sidebar-mini.sidebar-collapse .main-footer,
    .sidebar-mini.sidebar-collapse .main-header {
        margin-right: 4.6rem!important;
        margin-left: 0!important;
    }
}

@media (max-width: 767.98px) {
    .main-sidebar,
    .main-sidebar::before {
        box-shadow: none !important;
        margin-right: -250px;
    }
    .content-wrapper,
    .content-wrapper::before,
    .main-footer,
    .main-footer::before,
    .main-header,
    .main-header::before {
        margin-right: 0;
    }
    .sidebar-open .main-sidebar,
    .sidebar-open .main-sidebar::before {
        margin-right: 0;
    }
}

.ml-auto {
    margin-left: 0px !important;
}

.card-title {
    float: right !important;
}

.border-left {
    border-right: none !important;
}

.border-right {
    border-left: none !important;
}