$(document).ready(function () {

    /***** helper methods *****/

    // capitalize a word string
    const capitalize = (s) => {
        if (typeof s !== 'string') return '';
        return s.charAt(0).toUpperCase() + s.slice(1);
    }; // end of capitalize


    /***** end of helper methods *****/

    let fillComment = (userImage, data) => {
        return `
            <div class="row comment-block">
                <div class="user-block">
                    <img class="img-circle img-bordered-sm" src="${userImage}" alt="user image">
                    <span class="username">${data.commenter}</span>
                    <span class="description">${data.createdAt}</span>
                </div>
            </div>

            <!-- /.user-block -->
            <p>${data.desc}</p>
        `;

    }

    $('.add-comment').click(function (e) {
        e.preventDefault();

        let url = $(this).data('url');
        let userId = $("input[name=user_id]").val();
        let ticketId = $("input[name=ticket_id]").val();
        let desc = $("input[name=desc]").val();
        let _token = $("input[name=_token]").val();
        let userImage = $(this).data('user-image');

        $.ajax({
            _token: "{{ csrf_token() }}",
            url: url,
            type: "POST",
            data: {
                user_id: userId,
                ticket_id: ticketId,
                desc: desc,
                _token: _token
            },
            success: function (response) {
                data = response.data;
                message = response.success;

                let comment = fillComment(userImage, data);
                console.log(comment);

                new Noty({
                    type: 'success',
                    layout: 'topRight',
                    text: `${message}`,
                    timeout: 3000,
                    kill: true
                }).show();
                $('#comments-container').append(comment);
                $('.comment-input').val('');
            },
            statusCode: {
                422: function () {
                    new Noty({
                        type: 'error',
                        layout: 'topRight',
                        text: 'Please enter a valid comment!',
                        timeout: 3000,
                        kill: true
                    }).show();
                }
            }
        });
    });

    // view modal and send data of edit status modal
    let shipmentId;
    $('body').on('click', '#edit-status-btn', function (e) {
        e.preventDefault();
        let statusSelect = $('#status-select');
        statusSelect.empty();

        shipmentId = $(this).data('id');
        // console.log(shipmentId);
        let status = $.trim($(this).closest('td').prev().text());
        let baseUrl = $(this).data('base-url');

        $.ajax({
            _token: "{{ csrf_token() }}",
            url: `${baseUrl}/dashboard/couriers/statuses`,
            type: "GET",
            success: function (response) {
                statuses = response.data;
                $.each(statuses, function () {
                    statusSelect.append($("<option />").val(this.id).text(capitalize(this.name)));
                });

                // select selected option
                $('#status-select option').filter(function () {
                    return $.trim($(this).text()) == status;
                }).attr('selected', 'selected');

                $('#edit-shipment').modal('show');
            },
        });


    });

    // submit new status value
    $('body').on('click', '#submit-edit-status-btn', function (e) {
        e.preventDefault()


        let url = $(this).data('url');
        let statusId = $('#status-select').val();
        let _token = $("input[name=_token]").val();
        let comment = $("input[name=comment]").val();
        let selectedText = $.trim($("#status-select option:selected").html());

        // send modal data
        $.ajax({
            url: url,
            type: "POST",
            data: {
                id: shipmentId,
                status_id: statusId,
                comment: comment,
                _token: _token
            },
            success: function (response) {
                let data = response.data;
                let message = response.success;

                // get td contains shipment id
                let $updatedShipmentTd = $("td").filter(function () {
                    return $(this).text() == shipmentId;
                });


                // update text of statu
                $updatedShipmentTd.nextAll().eq(1).find('span').text(selectedText);

                // remove row if status = Delivered
                if (selectedText == 'Delivered') {
                    $updatedShipmentTd.parent().remove();
                }

                $('#edit-shipment').modal('hide');

                new Noty({
                    type: 'success',
                    layout: 'topRight',
                    text: `${message}`,
                    timeout: 3000,
                    kill: true
                }).show();
                $('#comments-container').append(comment);
                $('.comment-input').val('');
            },
            statusCode: {
                422: function () {
                    new Noty({
                        type: 'error',
                        layout: 'topRight',
                        text: 'Please enter a valid comment!',
                        timeout: 3000,
                        kill: true
                    }).show();
                }
            }
        });

    });


});
