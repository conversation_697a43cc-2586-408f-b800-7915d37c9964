﻿var CKBUILDER_CONFIG = {
    skin: "moono-lisa",
    preset: "standard",
    ignore: [
        ".DS_Store",
        ".bender",
        ".editorconfig",
        ".gitattributes",
        ".gitignore",
        ".idea",
        ".jscsrc",
        ".jshintignore",
        ".jshintrc",
        ".mailmap",
        ".npm",
        ".travis.yml",
        "bender-err.log",
        "bender-out.log",
        "bender.ci.js",
        "bender.js",
        "dev",
        "gruntfile.js",
        "less",
        "node_modules",
        "package.json",
        "tests"
    ],
    plugins: {
        a11yhelp: 1,
        about: 1,
        basicstyles: 1,
        blockquote: 1,
        clipboard: 1,
        contextmenu: 1,
        elementspath: 1,
        enterkey: 1,
        entities: 1,
        filebrowser: 1,
        floatingspace: 1,
        format: 1,
        horizontalrule: 1,
        htmlwriter: 1,
        image: 1,
        indentlist: 1,
        link: 1,
        list: 1,
        magicline: 1,
        maximize: 1,
        pastefromgdocs: 1,
        pastefromword: 1,
        pastetext: 1,
        pastetools: 1,
        removeformat: 1,
        resize: 1,
        scayt: 1,
        showborders: 1,
        sourcearea: 1,
        specialchar: 1,
        stylescombo: 1,
        tab: 1,
        table: 1,
        tableselection: 1,
        tabletools: 1,
        toolbar: 1,
        undo: 1,
        uploadimage: 1,
        wsc: 1,
        wysiwygarea: 1
    },
    languages: {
        ar: 1,
        en: 1
    }
};
