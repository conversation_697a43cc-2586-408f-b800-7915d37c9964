/*!
 SearchPanes 1.2.1
 2019-2020 SpryMedia Ltd - datatables.net/license
*/
(function(){var g,q,t,l=function(a,b,c,d,e,h){var f=this;void 0===h&&(h=null);if(!q||!q.versionCheck||!q.versionCheck("1.10.0"))throw Error("SearchPane requires DataTables 1.10 or newer");if(!q.select)throw Error("SearchPane requires Select");a=new q.Api(a);this.classes=g.extend(!0,{},l.classes);this.c=g.extend(!0,{},l.defaults,b);this.customPaneSettings=h;this.s={cascadeRegen:!1,clearing:!1,colOpts:[],deselect:!1,displayed:!1,dt:a,dtPane:void 0,filteringActive:!1,index:c,indexes:[],lastCascade:!1,
lastSelect:!1,listSet:!1,name:void 0,redraw:!1,rowData:{arrayFilter:[],arrayOriginal:[],arrayTotals:[],bins:{},binsOriginal:{},binsTotal:{},filterMap:new Map,totalOptions:0},scrollTop:0,searchFunction:void 0,selectPresent:!1,serverSelect:[],serverSelecting:!1,showFiltered:!1,tableLength:null,updating:!1};b=a.columns().eq(0).toArray().length;this.colExists=this.s.index<b;this.c.layout=d;b=parseInt(d.split("-")[1],10);this.dom={buttonGroup:g("<div/>").addClass(this.classes.buttonGroup),clear:g('<button type="button">&#215;</button>').addClass(this.classes.dull).addClass(this.classes.paneButton).addClass(this.classes.clearButton),
container:g("<div/>").addClass(this.classes.container).addClass(this.classes.layout+(10>b?d:d.split("-")[0]+"-9")),countButton:g('<button type="button"></button>').addClass(this.classes.paneButton).addClass(this.classes.countButton),dtP:g("<table><thead><tr><th>"+(this.colExists?g(a.column(this.colExists?this.s.index:0).header()).text():this.customPaneSettings.header||"Custom Pane")+"</th><th/></tr></thead></table>"),lower:g("<div/>").addClass(this.classes.subRow2).addClass(this.classes.narrowButton),
nameButton:g('<button type="button"></button>').addClass(this.classes.paneButton).addClass(this.classes.nameButton),panesContainer:e,searchBox:g("<input/>").addClass(this.classes.paneInputButton).addClass(this.classes.search),searchButton:g('<button type = "button" class="'+this.classes.searchIcon+'"></button>').addClass(this.classes.paneButton),searchCont:g("<div/>").addClass(this.classes.searchCont),searchLabelCont:g("<div/>").addClass(this.classes.searchLabelCont),topRow:g("<div/>").addClass(this.classes.topRow),
upper:g("<div/>").addClass(this.classes.subRow1).addClass(this.classes.narrowSearch)};this.s.displayed=!1;a=this.s.dt;this.selections=[];this.s.colOpts=this.colExists?this._getOptions():this._getBonusOptions();var o=this.s.colOpts,d=g('<button type="button">X</button>').addClass(this.classes.paneButton);g(d).text(a.i18n("searchPanes.clearPane","X"));this.dom.container.addClass(o.className);this.dom.container.addClass(null!==this.customPaneSettings&&void 0!==this.customPaneSettings.className?this.customPaneSettings.className:
"");this.s.name=void 0!==this.s.colOpts.name?this.s.colOpts.name:null!==this.customPaneSettings&&void 0!==this.customPaneSettings.name?this.customPaneSettings.name:this.colExists?g(a.column(this.s.index).header()).text():this.customPaneSettings.header||"Custom Pane";g(e).append(this.dom.container);var n=a.table(0).node();this.s.searchFunction=function(a,b,d){if(0===f.selections.length||a.nTable!==n)return!0;a=null;f.colExists&&(a=b[f.s.index],"filter"!==o.orthogonal.filter&&(a=f.s.rowData.filterMap.get(d),
a instanceof g.fn.dataTable.Api&&(a=a.toArray())));return f._search(a,d)};g.fn.dataTable.ext.search.push(this.s.searchFunction);if(this.c.clear)g(d).on("click",function(){f.dom.container.find(f.classes.search).each(function(){g(this).val("");g(this).trigger("input")});f.clearPane()});a.on("draw.dtsp",function(){f._adjustTopRow()});a.on("buttons-action",function(){f._adjustTopRow()});g(window).on("resize.dtsp",q.util.throttle(function(){f._adjustTopRow()}));a.on("column-reorder.dtsp",function(a,b,
d){f.s.index=d.mapping[f.s.index]});return this};l.prototype.clearData=function(){this.s.rowData={arrayFilter:[],arrayOriginal:[],arrayTotals:[],bins:{},binsOriginal:{},binsTotal:{},filterMap:new Map,totalOptions:0}};l.prototype.clearPane=function(){this.s.dtPane.rows({selected:!0}).deselect();this.updateTable();return this};l.prototype.destroy=function(){g(this.s.dtPane).off(".dtsp");g(this.s.dt).off(".dtsp");g(this.dom.nameButton).off(".dtsp");g(this.dom.countButton).off(".dtsp");g(this.dom.clear).off(".dtsp");
g(this.dom.searchButton).off(".dtsp");g(this.dom.container).remove();for(var a=g.fn.dataTable.ext.search.indexOf(this.s.searchFunction);-1!==a;)g.fn.dataTable.ext.search.splice(a,1),a=g.fn.dataTable.ext.search.indexOf(this.s.searchFunction);void 0!==this.s.dtPane&&this.s.dtPane.destroy();this.s.listSet=!1};l.prototype.getPaneCount=function(){return void 0!==this.s.dtPane?this.s.dtPane.rows({selected:!0}).data().toArray().length:0};l.prototype.rebuildPane=function(a,b,c,d){void 0===a&&(a=!1);void 0===
b&&(b=null);void 0===c&&(c=null);void 0===d&&(d=!1);this.clearData();var e=[];this.s.serverSelect=[];var h=null;void 0!==this.s.dtPane&&(d&&(this.s.dt.page.info().serverSide?this.s.serverSelect=this.s.dtPane.rows({selected:!0}).data().toArray():e=this.s.dtPane.rows({selected:!0}).data().toArray()),this.s.dtPane.clear().destroy(),h=g(this.dom.container).prev(),this.destroy(),this.s.dtPane=void 0,g.fn.dataTable.ext.search.push(this.s.searchFunction));this.dom.container.removeClass(this.classes.hidden);
this.s.displayed=!1;this._buildPane(!this.s.dt.page.info().serverSide?e:this.s.serverSelect,a,b,c,h);return this};l.prototype.removePane=function(){this.s.displayed=!1;g(this.dom.container).hide()};l.prototype.setCascadeRegen=function(a){this.s.cascadeRegen=a};l.prototype.setClear=function(a){this.s.clearing=a};l.prototype.updatePane=function(a){void 0===a&&(a=!1);this.s.updating=!0;this._updateCommon(a);this.s.updating=!1};l.prototype.updateTable=function(){this.selections=this.s.dtPane.rows({selected:!0}).data().toArray();
this._searchExtras();(this.c.cascadePanes||this.c.viewTotal)&&this.updatePane()};l.prototype._setListeners=function(){var a=this,b=this.s.rowData,c;this.s.dtPane.on("select.dtsp",function(){clearTimeout(c);a.s.dt.page.info().serverSide&&!a.s.updating?a.s.serverSelecting||(a.s.serverSelect=a.s.dtPane.rows({selected:!0}).data().toArray(),a.s.scrollTop=g(a.s.dtPane.table().node()).parent()[0].scrollTop,a.s.selectPresent=!0,a.s.dt.draw(!1)):(g(a.dom.clear).removeClass(a.classes.dull),a.s.selectPresent=
!0,a.s.updating||a._makeSelection(),a.s.selectPresent=!1)});this.s.dtPane.on("deselect.dtsp",function(){c=setTimeout(function(){a.s.dt.page.info().serverSide&&!a.s.updating?a.s.serverSelecting||(a.s.serverSelect=a.s.dtPane.rows({selected:!0}).data().toArray(),a.s.deselect=!0,a.s.dt.draw(!1)):(a.s.deselect=!0,0===a.s.dtPane.rows({selected:!0}).data().toArray().length&&g(a.dom.clear).addClass(a.classes.dull),a._makeSelection(),a.s.deselect=!1,a.s.dt.state.save())},50)});this.s.dt.on("stateSaveParams.dtsp",
function(d,c,h){if(g.isEmptyObject(h))a.s.dtPane.state.clear();else{var d=[],f,o,n,j;void 0!==a.s.dtPane&&(d=a.s.dtPane.rows({selected:!0}).data().map(function(a){return a.filter.toString()}).toArray(),f=g(a.dom.searchBox).val(),o=a.s.dtPane.order(),n=b.binsOriginal,j=b.arrayOriginal);void 0===h.searchPanes&&(h.searchPanes={});void 0===h.searchPanes.panes&&(h.searchPanes.panes=[]);h.searchPanes.panes.push({arrayFilter:j,bins:n,id:a.s.index,order:o,searchTerm:f,selected:d})}});this.s.dtPane.on("user-select.dtsp",
function(a,b,c,f,o){o.stopPropagation()});this.s.dtPane.on("draw.dtsp",function(){a._adjustTopRow()});g(this.dom.nameButton).on("click.dtsp",function(){var b=a.s.dtPane.order()[0][1];a.s.dtPane.order([0,"asc"===b?"desc":"asc"]).draw();a.s.dt.state.save()});g(this.dom.countButton).on("click.dtsp",function(){var b=a.s.dtPane.order()[0][1];a.s.dtPane.order([1,"asc"===b?"desc":"asc"]).draw();a.s.dt.state.save()});g(this.dom.clear).on("click.dtsp",function(){a.dom.container.find("."+a.classes.search).each(function(){g(this).val("");
g(this).trigger("input")});a.clearPane()});g(this.dom.searchButton).on("click.dtsp",function(){g(a.dom.searchBox).focus()});g(this.dom.searchBox).on("input.dtsp",function(){a.s.dtPane.search(g(a.dom.searchBox).val()).draw();a.s.dt.state.save()});this.s.dt.state.save();return!0};l.prototype._addOption=function(a,b,c,d,e,h){if(Array.isArray(a)||a instanceof q.Api)if(a instanceof q.Api&&(a=a.toArray(),b=b.toArray()),a.length===b.length)for(var f=0;f<a.length;f++)h[a[f]]?h[a[f]]++:(h[a[f]]=1,e.push({display:b[f],
filter:a[f],sort:c[f],type:d[f]})),this.s.rowData.totalOptions++;else throw Error("display and filter not the same length");else"string"===typeof this.s.colOpts.orthogonal?(h[a]?h[a]++:(h[a]=1,e.push({display:b,filter:a,sort:c,type:d})),this.s.rowData.totalOptions++):e.push({display:b,filter:a,sort:c,type:d})};l.prototype._addRow=function(a,b,c,d,e,h,f){for(var o,g=0,j=this.s.indexes;g<j.length;g++){var i=j[g];i.filter===b&&(o=i.index)}void 0===o&&(o=this.s.indexes.length,this.s.indexes.push({filter:b,
index:o}));return this.s.dtPane.row.add({className:f,display:""!==a?a:!1!==this.s.colOpts.emptyMessage?this.s.colOpts.emptyMessage:this.c.emptyMessage,filter:b,index:o,shown:c,sort:""!==e?e:!1!==this.s.colOpts.emptyMessage?this.s.colOpts.emptyMessage:this.c.emptyMessage,total:d,type:h})};l.prototype._adjustTopRow=function(){var a=this.dom.container.find("."+this.classes.subRowsContainer),b=this.dom.container.find(".dtsp-subRow1"),c=this.dom.container.find(".dtsp-subRow2"),d=this.dom.container.find("."+
this.classes.topRow);(252>g(a[0]).width()||252>g(d[0]).width())&&0!==g(a[0]).width()?(g(a[0]).addClass(this.classes.narrow),g(b[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowSearch),g(c[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowButton)):(g(a[0]).removeClass(this.classes.narrow),g(b[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowSearch),g(c[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowButton))};l.prototype._buildPane=
function(a,b,c,d,e){var h=this;void 0===a&&(a=[]);void 0===b&&(b=!1);void 0===c&&(c=null);void 0===d&&(d=null);void 0===e&&(e=null);this.selections=[];var f=this.s.dt,o=f.column(this.colExists?this.s.index:0),n=this.s.colOpts,j=this.s.rowData,i=f.i18n("searchPanes.count","{total}"),l=f.i18n("searchPanes.countFiltered","{shown} ({total})"),p=f.state.loaded();this.s.listSet&&(p=f.state());if(this.colExists){var m=-1;if(p&&p.searchPanes&&p.searchPanes.panes)for(var k=0;k<p.searchPanes.panes.length;k++)if(p.searchPanes.panes[k].id===
this.s.index){m=k;break}if((!1===n.show||void 0!==n.show&&!0!==n.show)&&-1===m)return this.dom.container.addClass(this.classes.hidden),this.s.displayed=!1;if(!0===n.show||-1!==m)this.s.displayed=!0;if(!this.s.dt.page.info().serverSide&&null===c){if(0===j.arrayFilter.length)if(this._populatePane(b),this.s.rowData.totalOptions=0,this._detailsPane(),p&&p.searchPanes&&p.searchPanes.panes)if(-1!==m)j.binsOriginal=p.searchPanes.panes[m].bins,j.arrayOriginal=p.searchPanes.panes[m].arrayFilter;else{this.dom.container.addClass(this.classes.hidden);
this.s.displayed=!1;return}else j.arrayOriginal=j.arrayTotals,j.binsOriginal=j.binsTotal;k=Object.keys(j.binsOriginal).length;c=this._uniqueRatio(k,f.rows()[0].length);if(!1===this.s.displayed&&((void 0===n.show&&null===n.threshold?c>this.c.threshold:c>n.threshold)||!0!==n.show&&1>=k)){this.dom.container.addClass(this.classes.hidden);this.s.displayed=!1;return}this.c.viewTotal&&0===j.arrayTotals.length?(this.s.rowData.totalOptions=0,this._detailsPane()):j.binsTotal=j.bins;this.dom.container.addClass(this.classes.show);
this.s.displayed=!0}else if(null!==c){if(void 0!==c.tableLength)this.s.tableLength=c.tableLength,this.s.rowData.totalOptions=this.s.tableLength;else if(null===this.s.tableLength||f.rows()[0].length>this.s.tableLength)this.s.tableLength=f.rows()[0].length,this.s.rowData.totalOptions=this.s.tableLength;b=f.column(this.s.index).dataSrc();if(void 0!==c[b]){k=0;for(c=c[b];k<c.length;k++)b=c[k],this.s.rowData.arrayFilter.push({display:b.label,filter:b.value,sort:b.label,type:b.label}),this.s.rowData.bins[b.value]=
this.c.viewTotal||this.c.cascadePanes?b.count:b.total,this.s.rowData.binsTotal[b.value]=b.total}k=Object.keys(j.binsTotal).length;c=this._uniqueRatio(k,this.s.tableLength);if(!1===this.s.displayed&&((void 0===n.show&&null===n.threshold?c>this.c.threshold:c>n.threshold)||!0!==n.show&&1>=k)){this.dom.container.addClass(this.classes.hidden);this.s.displayed=!1;return}this.s.rowData.arrayOriginal=this.s.rowData.arrayFilter;this.s.rowData.binsOriginal=this.s.rowData.bins;this.s.displayed=!0}}else this.s.displayed=
!0;this._displayPane();if(!this.s.listSet)this.dom.dtP.on("stateLoadParams.dt",function(a,b,d){g.isEmptyObject(f.state.loaded())&&g.each(d,function(a){delete d[a]})});null!==e&&0<g(this.dom.panesContainer).has(e).length?g(this.dom.container).insertAfter(e):g(this.dom.panesContainer).prepend(this.dom.container);k=g.fn.dataTable.ext.errMode;g.fn.dataTable.ext.errMode="none";e=q.Scroller;this.s.dtPane=g(this.dom.dtP).DataTable(g.extend(!0,{columnDefs:[{className:"dtsp-nameColumn",data:"display",render:function(a,
b,d){if(b==="sort")return d.sort;if(b==="type")return d.type;var c;(h.s.filteringActive||h.s.showFiltered)&&h.c.viewTotal?c=l.replace(/{total}/,d.total):c=i.replace(/{total}/,d.total);for(c=c.replace(/{shown}/,d.shown);c.indexOf("{total}")!==-1;)c=c.replace(/{total}/,d.total);for(;c.indexOf("{shown}")!==-1;)c=c.replace(/{shown}/,d.shown);b='<span class="'+h.classes.pill+'">'+c+"</span>";if(h.c.hideCount||n.hideCount)b="";return'<div class="'+h.classes.nameCont+'"><span title="'+(typeof a==="string"&&
a.match(/<[^>]*>/)!==null?a.replace(/<[^>]*>/g,""):a)+'" class="'+h.classes.name+'">'+a+"</span>"+b+"</div>"},targets:0,type:void 0!==f.settings()[0].aoColumns[this.s.index]?f.settings()[0].aoColumns[this.s.index]._sManualType:null},{className:"dtsp-countColumn "+this.classes.badgePill,data:"shown",orderData:[1,2],targets:1,visible:!1},{data:"total",targets:2,visible:!1}],deferRender:!0,dom:"t",info:!1,language:this.s.dt.settings()[0].oLanguage,paging:e?!0:!1,scrollX:!1,scrollY:"200px",scroller:e?
!0:!1,select:!0,stateSave:f.settings()[0].oFeatures.bStateSave?!0:!1},this.c.dtOpts,void 0!==n?n.dtOpts:{},void 0!==this.s.colOpts.options||!this.colExists?{createdRow:function(a,b){g(a).addClass(b.className)}}:void 0,null!==this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts?this.customPaneSettings.dtOpts:{}));g(this.dom.dtP).addClass(this.classes.table);g(this.dom.searchBox).attr("placeholder",void 0!==n.header?n.header:this.colExists?f.settings()[0].aoColumns[this.s.index].sTitle:
this.customPaneSettings.header||"Custom Pane");g.fn.dataTable.select.init(this.s.dtPane);g.fn.dataTable.ext.errMode=k;if(this.colExists){var o=(o=o.search())?o.substr(1,o.length-2).split("|"):[],r=0;j.arrayFilter.forEach(function(a){""===a.filter&&r++});k=0;for(e=j.arrayFilter.length;k<e;k++){o=!1;b=0;for(m=this.s.serverSelect;b<m.length;b++)c=m[b],c.filter===j.arrayFilter[k].filter&&(o=!0);if(this.s.dt.page.info().serverSide&&(!this.c.cascadePanes||this.c.cascadePanes&&0!==j.bins[j.arrayFilter[k].filter]||
this.c.cascadePanes&&null!==d||o)){o=this._addRow(j.arrayFilter[k].display,j.arrayFilter[k].filter,d?j.binsTotal[j.arrayFilter[k].filter]:j.bins[j.arrayFilter[k].filter],this.c.viewTotal||d?String(j.binsTotal[j.arrayFilter[k].filter]):j.bins[j.arrayFilter[k].filter],j.arrayFilter[k].sort,j.arrayFilter[k].type);b=0;for(m=this.s.serverSelect;b<m.length;b++)c=m[b],c.filter===j.arrayFilter[k].filter&&(this.s.serverSelecting=!0,o.select(),this.s.serverSelecting=!1)}else!this.s.dt.page.info().serverSide&&
j.arrayFilter[k]&&(void 0!==j.bins[j.arrayFilter[k].filter]||!this.c.cascadePanes)?this._addRow(j.arrayFilter[k].display,j.arrayFilter[k].filter,j.bins[j.arrayFilter[k].filter],j.binsTotal[j.arrayFilter[k].filter],j.arrayFilter[k].sort,j.arrayFilter[k].type):this.s.dt.page.info().serverSide||this._addRow("",r,r,"","","")}}q.select.init(this.s.dtPane);(void 0!==n.options||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options)&&this._getComparisonRows();this.s.dtPane.draw();this._adjustTopRow();
this.s.listSet||(this._setListeners(),this.s.listSet=!0);for(d=0;d<a.length;d++)if(j=a[d],void 0!==j){k=0;for(e=this.s.dtPane.rows().indexes().toArray();k<e.length;k++)o=e[k],void 0!==this.s.dtPane.row(o).data()&&j.filter===this.s.dtPane.row(o).data().filter&&(this.s.dt.page.info().serverSide?(this.s.serverSelecting=!0,this.s.dtPane.row(o).select(),this.s.serverSelecting=!1):this.s.dtPane.row(o).select())}this.s.dt.page.info().serverSide&&this.s.dtPane.search(g(this.dom.searchBox).val()).draw();if(p&&
p.searchPanes&&p.searchPanes.panes){this.c.cascadePanes||this._reloadSelect(p);a=0;for(p=p.searchPanes.panes;a<p.length;a++)d=p[a],d.id===this.s.index&&(g(this.dom.searchBox).val(d.searchTerm),g(this.dom.searchBox).trigger("input"),this.s.dtPane.order(d.order).draw())}this.s.dt.state.save();return!0};l.prototype._detailsPane=function(){var a=this.s.dt;this.s.rowData.arrayTotals=[];this.s.rowData.binsTotal={};var b=this.s.dt.settings()[0],a=a.rows().indexes();if(!this.s.dt.page.info().serverSide)for(var c=
0;c<a.length;c++)this._populatePaneArray(a[c],this.s.rowData.arrayTotals,b,this.s.rowData.binsTotal)};l.prototype._displayPane=function(){var a=this.dom.container,b=this.s.colOpts,c=parseInt(this.c.layout.split("-")[1],10);g(this.dom.topRow).empty();g(this.dom.dtP).empty();g(this.dom.topRow).addClass(this.classes.topRow);3<c&&g(this.dom.container).addClass(this.classes.smallGap);g(this.dom.topRow).addClass(this.classes.subRowsContainer);g(this.dom.upper).appendTo(this.dom.topRow);g(this.dom.lower).appendTo(this.dom.topRow);
g(this.dom.searchCont).appendTo(this.dom.upper);g(this.dom.buttonGroup).appendTo(this.dom.lower);(!1===this.c.dtOpts.searching||void 0!==b.dtOpts&&!1===b.dtOpts.searching||!this.c.controls||!b.controls||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts&&void 0!==this.customPaneSettings.dtOpts.searching&&!this.customPaneSettings.dtOpts.searching)&&g(this.dom.searchBox).attr("disabled","disabled").removeClass(this.classes.paneInputButton).addClass(this.classes.disabledButton);
g(this.dom.searchBox).appendTo(this.dom.searchCont);this._searchContSetup();this.c.clear&&(this.c.controls&&b.controls)&&g(this.dom.clear).appendTo(this.dom.buttonGroup);this.c.orderable&&(b.orderable&&this.c.controls&&b.controls)&&g(this.dom.nameButton).appendTo(this.dom.buttonGroup);!this.c.hideCount&&(!b.hideCount&&this.c.orderable&&b.orderable&&this.c.controls&&b.controls)&&g(this.dom.countButton).appendTo(this.dom.buttonGroup);g(this.dom.topRow).prependTo(this.dom.container);g(a).append(this.dom.dtP);
g(a).show()};l.prototype._getBonusOptions=function(){return g.extend(!0,{},l.defaults,{orthogonal:{threshold:null},threshold:null},void 0!==this.c?this.c:{})};l.prototype._getComparisonRows=function(){var a=this.s.colOpts,a=void 0!==a.options?a.options:null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options?this.customPaneSettings.options:void 0;if(void 0!==a){var b=this.s.dt.rows({search:"applied"}).data().toArray(),c=this.s.dt.rows({search:"applied"}),d=this.s.dt.rows().data().toArray(),
e=this.s.dt.rows(),h=[];this.s.dtPane.clear();for(var f=0;f<a.length;f++){var g=a[f],n=""!==g.label?g.label:this.c.emptyMessage,j=g.className,i=n,l="function"===typeof g.value?g.value:[],m=0,q=n,k=0;if("function"===typeof g.value){for(var r=0;r<b.length;r++)g.value.call(this.s.dt,b[r],c[0][r])&&m++;for(r=0;r<d.length;r++)g.value.call(this.s.dt,d[r],e[0][r])&&k++;"function"!==typeof l&&l.push(g.filter)}(!this.c.cascadePanes||this.c.cascadePanes&&0!==m)&&h.push(this._addRow(i,l,m,k,q,n,j))}return h}};
l.prototype._getOptions=function(){return g.extend(!0,{},l.defaults,{emptyMessage:!1,orthogonal:{threshold:null},threshold:null},this.s.dt.settings()[0].aoColumns[this.s.index].searchPanes)};l.prototype._makeSelection=function(){this.updateTable();this.s.updating=!0;this.s.dt.draw();this.s.updating=!1};l.prototype._populatePane=function(a){void 0===a&&(a=!1);var b=this.s.dt;this.s.rowData.arrayFilter=[];this.s.rowData.bins={};var c=this.s.dt.settings()[0];if(!this.s.dt.page.info().serverSide)for(var d=
0,a=((this.c.cascadePanes||this.c.viewTotal)&&!this.s.clearing&&!a?b.rows({search:"applied"}).indexes():b.rows().indexes()).toArray();d<a.length;d++)this._populatePaneArray(a[d],this.s.rowData.arrayFilter,c)};l.prototype._populatePaneArray=function(a,b,c,d){void 0===d&&(d=this.s.rowData.bins);var e=this.s.colOpts;if("string"===typeof e.orthogonal)c=c.oApi._fnGetCellData(c,a,this.s.index,e.orthogonal),this.s.rowData.filterMap.set(a,c),this._addOption(c,c,c,c,b,d);else{var h=c.oApi._fnGetCellData(c,
a,this.s.index,e.orthogonal.search);"string"===typeof h&&(h=h.replace(/<[^>]*>/g,""));this.s.rowData.filterMap.set(a,h);d[h]?d[h]++:(d[h]=1,this._addOption(h,c.oApi._fnGetCellData(c,a,this.s.index,e.orthogonal.display),c.oApi._fnGetCellData(c,a,this.s.index,e.orthogonal.sort),c.oApi._fnGetCellData(c,a,this.s.index,e.orthogonal.type),b,d));this.s.rowData.totalOptions++}};l.prototype._reloadSelect=function(a){if(void 0!==a){for(var b,c=0;c<a.searchPanes.panes.length;c++)if(a.searchPanes.panes[c].id===
this.s.index){b=c;break}if(void 0!==b)for(var c=this.s.dtPane,d=c.rows({order:"index"}).data().map(function(a){return null!==a.filter?a.filter.toString():null}).toArray(),e=0,a=a.searchPanes.panes[b].selected;e<a.length;e++){b=a[e];var h=-1;null!==b&&(h=d.indexOf(b.toString()));-1<h&&(c.row(h).select(),this.s.dt.state.save())}}};l.prototype._search=function(a,b){for(var c=this.s.colOpts,d=this.s.dt,e=0,h=this.selections;e<h.length;e++){var f=h[e];if(Array.isArray(a)){if(-1!==a.indexOf(f.filter))return!0}else if("function"===
typeof f.filter)if(f.filter.call(d,d.row(b).data(),b)){if("or"===c.combiner)return!0}else{if("and"===c.combiner)return!1}else if(a===f.filter||!("string"===typeof a&&0===a.length)&&a==f.filter||null===f.filter&&"string"===typeof a&&""===a)return!0}return"and"===c.combiner?!0:!1};l.prototype._searchContSetup=function(){this.c.controls&&this.s.colOpts.controls&&g(this.dom.searchButton).appendTo(this.dom.searchLabelCont);!1===this.c.dtOpts.searching||(!1===this.s.colOpts.dtOpts.searching||null!==this.customPaneSettings&&
void 0!==this.customPaneSettings.dtOpts&&void 0!==this.customPaneSettings.dtOpts.searching&&!this.customPaneSettings.dtOpts.searching)||g(this.dom.searchLabelCont).appendTo(this.dom.searchCont)};l.prototype._searchExtras=function(){var a=this.s.updating;this.s.updating=!0;var b=this.s.dtPane.rows({selected:!0}).data().pluck("filter").toArray(),c=b.indexOf(!1!==this.s.colOpts.emptyMessage?this.s.colOpts.emptyMessage:this.c.emptyMessage),d=g(this.s.dtPane.table().container());-1<c&&(b[c]="");0<b.length?
d.addClass(this.classes.selected):0===b.length&&d.removeClass(this.classes.selected);this.s.updating=a};l.prototype._uniqueRatio=function(a,b){return 0<b&&(0<this.s.rowData.totalOptions&&!this.s.dt.page.info().serverSide||this.s.dt.page.info().serverSide&&0<this.s.tableLength)?a/this.s.rowData.totalOptions:1};l.prototype._updateCommon=function(a){void 0===a&&(a=!1);if(!this.s.dt.page.info().serverSide&&void 0!==this.s.dtPane&&(!this.s.filteringActive||this.c.cascadePanes||!0===a)&&(!0!==this.c.cascadePanes||
!0!==this.s.selectPresent)&&(!this.s.lastSelect||!this.s.lastCascade)){var b=this.s.colOpts,c=this.s.dtPane.rows({selected:!0}).data().toArray(),a=g(this.s.dtPane.table().node()).parent()[0].scrollTop,d=this.s.rowData;this.s.dtPane.clear();if(this.colExists){0===d.arrayFilter.length?this._populatePane():this.c.cascadePanes&&this.s.dt.rows().data().toArray().length===this.s.dt.rows({search:"applied"}).data().toArray().length?(d.arrayFilter=d.arrayOriginal,d.bins=d.binsOriginal):(this.c.viewTotal||
this.c.cascadePanes)&&this._populatePane();this.c.viewTotal?this._detailsPane():d.binsTotal=d.bins;this.c.viewTotal&&!this.c.cascadePanes&&(d.arrayFilter=d.arrayTotals);for(var e=function(a){if(a&&(d.bins[a.filter]!==void 0&&d.bins[a.filter]!==0&&h.c.cascadePanes||!h.c.cascadePanes||h.s.clearing)){var b=h._addRow(a.display,a.filter,!h.c.viewTotal?d.bins[a.filter]:d.bins[a.filter]!==void 0?d.bins[a.filter]:0,h.c.viewTotal?String(d.binsTotal[a.filter]):d.bins[a.filter],a.sort,a.type),f=c.findIndex(function(b){return b.filter===
a.filter});if(f!==-1){b.select();c.splice(f,1)}}},h=this,f=0,o=d.arrayFilter;f<o.length;f++)e(o[f])}if(void 0!==b.searchPanes&&void 0!==b.searchPanes.options||void 0!==b.options||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options){e=function(a){var b=c.findIndex(function(b){if(b.display===a.data().display)return!0});-1!==b&&(a.select(),c.splice(b,1))};f=0;for(o=this._getComparisonRows();f<o.length;f++)b=o[f],e(b)}for(e=0;e<c.length;e++)b=c[e],b=this._addRow(b.display,b.filter,
0,this.c.viewTotal?b.total:0,b.display,b.display),this.s.updating=!0,b.select(),this.s.updating=!1;this.s.dtPane.draw();this.s.dtPane.table().node().parentNode.scrollTop=a}};l.version="1.1.0";l.classes={buttonGroup:"dtsp-buttonGroup",buttonSub:"dtsp-buttonSub",clear:"dtsp-clear",clearAll:"dtsp-clearAll",clearButton:"clearButton",container:"dtsp-searchPane",countButton:"dtsp-countButton",disabledButton:"dtsp-disabledButton",dull:"dtsp-dull",hidden:"dtsp-hidden",hide:"dtsp-hide",layout:"dtsp-",name:"dtsp-name",
nameButton:"dtsp-nameButton",nameCont:"dtsp-nameCont",narrow:"dtsp-narrow",paneButton:"dtsp-paneButton",paneInputButton:"dtsp-paneInputButton",pill:"dtsp-pill",search:"dtsp-search",searchCont:"dtsp-searchCont",searchIcon:"dtsp-searchIcon",searchLabelCont:"dtsp-searchButtonCont",selected:"dtsp-selected",smallGap:"dtsp-smallGap",subRow1:"dtsp-subRow1",subRow2:"dtsp-subRow2",subRowsContainer:"dtsp-subRowsContainer",title:"dtsp-title",topRow:"dtsp-topRow"};l.defaults={cascadePanes:!1,clear:!0,combiner:"or",
controls:!0,container:function(a){return a.table().container()},dtOpts:{},emptyMessage:"<i>No Data</i>",hideCount:!1,layout:"columns-3",name:void 0,orderable:!0,orthogonal:{display:"display",filter:"filter",hideCount:!1,search:"filter",show:void 0,sort:"sort",threshold:0.6,type:"type"},preSelect:[],threshold:0.6,viewTotal:!1};t=l;var i,s,u,m=function(a,b,c){var d=this;void 0===c&&(c=!1);this.regenerating=!1;if(!s||!s.versionCheck||!s.versionCheck("1.10.0"))throw Error("SearchPane requires DataTables 1.10 or newer");
if(!s.select)throw Error("SearchPane requires Select");var e=new s.Api(a);this.classes=i.extend(!0,{},m.classes);this.c=i.extend(!0,{},m.defaults,b);this.dom={clearAll:i('<button type="button">Clear All</button>').addClass(this.classes.clearAll),container:i("<div/>").addClass(this.classes.panes).text(e.i18n("searchPanes.loadMessage","Loading Search Panes...")),emptyMessage:i("<div/>").addClass(this.classes.emptyMessage),options:i("<div/>").addClass(this.classes.container),panes:i("<div/>").addClass(this.classes.container),
title:i("<div/>").addClass(this.classes.title),titleRow:i("<div/>").addClass(this.classes.titleRow),wrapper:i("<div/>")};this.s={colOpts:[],dt:e,filterPane:-1,panes:[],selectionList:[],serverData:{},stateRead:!1,updating:!1};if(void 0===e.settings()[0]._searchPanes){e.on("xhr",function(a,b,c){c.searchPanes&&c.searchPanes.options&&(d.s.serverData=c.searchPanes.options,d.s.serverData.tableLength=c.recordsTotal,d._serverTotals())});e.settings()[0]._searchPanes=this;this.dom.clearAll.text(e.i18n("searchPanes.clearMessage",
"Clear All"));this._getState();if(this.s.dt.settings()[0]._bInitComplete||c)this._paneDeclare(e,a,b);else e.one("preInit.dt",function(){d._paneDeclare(e,a,b)});return this}};m.prototype.clearSelections=function(){this.dom.container.find(this.classes.search).each(function(){i(this).val("");i(this).trigger("input")});for(var a=[],b=0,c=this.s.panes;b<c.length;b++){var d=c[b];void 0!==d.s.dtPane&&a.push(d.clearPane())}this.s.dt.draw();return a};m.prototype.getNode=function(){return this.dom.container};
m.prototype.rebuild=function(a,b){void 0===a&&(a=!1);void 0===b&&(b=!1);i(this.dom.emptyMessage).remove();var c=[];!1===a&&i(this.dom.panes).empty();for(var d=0,e=this.s.panes;d<e.length;d++){var h=e[d];!1!==a&&h.s.index!==a||(h.clearData(),c.push(h.rebuildPane(void 0!==this.s.selectionList[this.s.selectionList.length-1]?h.s.index===this.s.selectionList[this.s.selectionList.length-1].index:!1,this.s.dt.page.info().serverSide?this.s.serverData:void 0,null,b)),i(this.dom.panes).append(h.dom.container))}this.s.dt.page.info().serverSide||
this.s.dt.draw();this.c.cascadePanes||this.c.viewTotal?this.redrawPanes(!0):this._updateSelection();this._updateFilterCount();this._attachPaneContainer();this.s.dt.draw();return 1===c.length?c[0]:c};m.prototype.redrawPanes=function(a){void 0===a&&(a=!1);var b=this.s.dt;if(!this.s.updating&&!this.s.dt.page.info().serverSide){var c=!0,d=this.s.filterPane;if(b.rows({search:"applied"}).data().toArray().length===b.rows().data().toArray().length)c=!1;else if(this.c.viewTotal)for(var e=0,h=this.s.panes;e<
h.length;e++){var f=h[e];if(void 0!==f.s.dtPane){var g=f.s.dtPane.rows({selected:!0}).data().toArray().length;if(0===g)for(var n=0,j=this.s.selectionList;n<j.length;n++){var i=j[n];i.index===f.s.index&&0!==i.rows.length&&(g=i.rows.length)}0<g&&-1===d?d=f.s.index:0<g&&(d=null)}}h=void 0;e=[];if(this.regenerating){h=-1;1===e.length&&(h=e[0].index);a=0;for(e=this.s.panes;a<e.length;a++)if(f=e[a],void 0!==f.s.dtPane){b=!0;f.s.filteringActive=!0;if(-1!==d&&null!==d&&d===f.s.index||!1===c||f.s.index===
h)b=!1,f.s.filteringActive=!1;f.updatePane(!b?b:c)}this._updateFilterCount()}else{g=0;for(n=this.s.panes;g<n.length;g++)if(f=n[g],f.s.selectPresent){this.s.selectionList.push({index:f.s.index,rows:f.s.dtPane.rows({selected:!0}).data().toArray(),protect:!1});b.state.save();break}else f.s.deselect&&(h=f.s.index,j=f.s.dtPane.rows({selected:!0}).data().toArray(),0<j.length&&this.s.selectionList.push({index:f.s.index,rows:j,protect:!0}));if(0<this.s.selectionList.length){b=this.s.selectionList[this.s.selectionList.length-
1].index;g=0;for(n=this.s.panes;g<n.length;g++)f=n[g],f.s.lastSelect=f.s.index===b}for(f=0;f<this.s.selectionList.length;f++)if(this.s.selectionList[f].index!==h||!0===this.s.selectionList[f].protect){b=!1;for(g=f+1;g<this.s.selectionList.length;g++)this.s.selectionList[g].index===this.s.selectionList[f].index&&(b=!0);b||(e.push(this.s.selectionList[f]),this.s.selectionList[f].protect=!1)}h=-1;1===e.length&&(h=e[0].index);g=0;for(n=this.s.panes;g<n.length;g++)if(f=n[g],void 0!==f.s.dtPane){b=!0;f.s.filteringActive=
!0;if(-1!==d&&null!==d&&d===f.s.index||!1===c||f.s.index===h)b=!1,f.s.filteringActive=!1;f.updatePane(!b?!1:c)}this._updateFilterCount();if(0<e.length&&(e.length<this.s.selectionList.length||a)){this._cascadeRegen(e);b=e[e.length-1].index;d=0;for(a=this.s.panes;d<a.length;d++)f=a[d],f.s.lastSelect=f.s.index===b}else if(0<e.length){f=0;for(a=this.s.panes;f<a.length;f++)if(e=a[f],void 0!==e.s.dtPane){b=!0;e.s.filteringActive=!0;if(-1!==d&&null!==d&&d===e.s.index||!1===c)b=!1,e.s.filteringActive=!1;
e.updatePane(!b?b:c)}}}c||(this.s.selectionList=[])}};m.prototype._attach=function(){var a=this;i(this.dom.container).removeClass(this.classes.hide);i(this.dom.titleRow).removeClass(this.classes.hide);i(this.dom.titleRow).remove();i(this.dom.title).appendTo(this.dom.titleRow);this.c.clear&&(i(this.dom.clearAll).appendTo(this.dom.titleRow),i(this.dom.clearAll).on("click.dtsps",function(){a.clearSelections()}));i(this.dom.titleRow).appendTo(this.dom.container);for(var b=0,c=this.s.panes;b<c.length;b++)i(c[b].dom.container).appendTo(this.dom.panes);
i(this.dom.panes).appendTo(this.dom.container);0===i("div."+this.classes.container).length&&i(this.dom.container).prependTo(this.s.dt);return this.dom.container};m.prototype._attachExtras=function(){i(this.dom.container).removeClass(this.classes.hide);i(this.dom.titleRow).removeClass(this.classes.hide);i(this.dom.titleRow).remove();i(this.dom.title).appendTo(this.dom.titleRow);this.c.clear&&i(this.dom.clearAll).appendTo(this.dom.titleRow);i(this.dom.titleRow).appendTo(this.dom.container);return this.dom.container};
m.prototype._attachMessage=function(){var a;try{a=this.s.dt.i18n("searchPanes.emptyPanes","No SearchPanes")}catch(b){a=null}if(null===a)i(this.dom.container).addClass(this.classes.hide),i(this.dom.titleRow).removeClass(this.classes.hide);else return i(this.dom.container).removeClass(this.classes.hide),i(this.dom.titleRow).addClass(this.classes.hide),i(this.dom.emptyMessage).text(a),this.dom.emptyMessage.appendTo(this.dom.container),this.dom.container};m.prototype._attachPaneContainer=function(){for(var a=
0,b=this.s.panes;a<b.length;a++)if(!0===b[a].s.displayed)return this._attach();return this._attachMessage()};m.prototype._cascadeRegen=function(a){this.regenerating=!0;var b=-1;1===a.length&&(b=a[0].index);for(var c=0,d=this.s.panes;c<d.length;c++){var e=d[c];e.setCascadeRegen(!0);e.setClear(!0);(void 0!==e.s.dtPane&&e.s.index===b||void 0!==e.s.dtPane)&&e.clearPane();e.setClear(!1)}this._makeCascadeSelections(a);this.s.selectionList=a;a=0;for(b=this.s.panes;a<b.length;a++)e=b[a],e.setCascadeRegen(!1);
this.regenerating=!1};m.prototype._checkMessage=function(){for(var a=0,b=this.s.panes;a<b.length;a++)if(!0===b[a].s.displayed)return;return this._attachMessage()};m.prototype._getState=function(){var a=this.s.dt.state.loaded();a&&(a.searchPanes&&void 0!==a.searchPanes.selectionList)&&(this.s.selectionList=a.searchPanes.selectionList)};m.prototype._makeCascadeSelections=function(a){for(var b=0;b<a.length;b++)for(var c=function(c){if(c.s.index===a[b].index&&void 0!==c.s.dtPane){b===a.length-1&&(c.s.lastCascade=
!0);0<c.s.dtPane.rows({selected:!0}).data().toArray().length&&void 0!==c.s.dtPane&&(c.setClear(!0),c.clearPane(),c.setClear(!1));for(var e=function(a){c.s.dtPane.rows().every(function(b){c.s.dtPane.row(b).data()!==void 0&&(a!==void 0&&c.s.dtPane.row(b).data().filter===a.filter)&&c.s.dtPane.row(b).select()})},h=0,g=a[b].rows;h<g.length;h++)e(g[h]);d._updateFilterCount();c.s.lastCascade=!1}},d=this,e=0,h=this.s.panes;e<h.length;e++)c(h[e]);this.s.dt.state.save()};m.prototype._paneDeclare=function(a,
b,c){var d=this;a.columns(0<this.c.columns.length?this.c.columns:void 0).eq(0).each(function(a){d.s.panes.push(new t(b,c,a,d.c.layout,d.dom.panes))});for(var e=a.columns().eq(0).toArray().length,h=this.c.panes.length,f=0;f<h;f++)this.s.panes.push(new t(b,c,e+f,this.c.layout,this.dom.panes,this.c.panes[f]));if(0<this.c.order.length){e=this.c.order.map(function(a){return d._findPane(a)});this.dom.panes.empty();this.s.panes=e;e=0;for(h=this.s.panes;e<h.length;e++)this.dom.panes.append(h[e].dom.container)}this.s.dt.settings()[0]._bInitComplete?
this._startup(a):this.s.dt.settings()[0].aoInitComplete.push({fn:function(){d._startup(a)}})};m.prototype._findPane=function(a){for(var b=0,c=this.s.panes;b<c.length;b++){var d=c[b];if(a===d.s.name)return d}};m.prototype._serverTotals=function(){for(var a=!1,b=!1,c=this.s.dt,d=0,e=this.s.panes;d<e.length;d++){var h=e[d];if(h.s.selectPresent){this.s.selectionList.push({index:h.s.index,rows:h.s.dtPane.rows({selected:!0}).data().toArray(),protect:!1});c.state.save();h.s.selectPresent=!1;a=!0;break}else h.s.deselect&&
(b=h.s.dtPane.rows({selected:!0}).data().toArray(),0<b.length&&this.s.selectionList.push({index:h.s.index,rows:b,protect:!0}),b=a=!0)}if(a){h=[];for(c=0;c<this.s.selectionList.length;c++){d=!1;for(e=c+1;e<this.s.selectionList.length;e++)this.s.selectionList[e].index===this.s.selectionList[c].index&&(d=!0);!d&&0<this.s.panes[this.s.selectionList[c].index].s.dtPane.rows({selected:!0}).data().toArray().length&&h.push(this.s.selectionList[c])}this.s.selectionList=h}else this.s.selectionList=[];c=-1;if(b&&
1===this.s.selectionList.length){b=0;for(d=this.s.panes;b<d.length;b++)h=d[b],h.s.lastSelect=!1,h.s.deselect=!1,void 0!==h.s.dtPane&&0<h.s.dtPane.rows({selected:!0}).data().toArray().length&&(c=h.s.index)}else if(0<this.s.selectionList.length){b=this.s.selectionList[this.s.selectionList.length-1].index;d=0;for(e=this.s.panes;d<e.length;d++)h=e[d],h.s.lastSelect=h.s.index===b,h.s.deselect=!1}else if(0===this.s.selectionList.length){b=0;for(d=this.s.panes;b<d.length;b++)h=d[b],h.s.lastSelect=!1,h.s.deselect=
!1}i(this.dom.panes).empty();b=0;for(d=this.s.panes;b<d.length;b++)h=d[b],h.s.lastSelect?h._setListeners():h.rebuildPane(void 0,this.s.dt.page.info().serverSide?this.s.serverData:void 0,h.s.index===c?!0:null,!0),i(this.dom.panes).append(h.dom.container),void 0!==h.s.dtPane&&(i(h.s.dtPane.table().node()).parent()[0].scrollTop=h.s.scrollTop,i.fn.dataTable.select.init(h.s.dtPane));this.s.dt.page.info().serverSide||this.s.dt.draw()};m.prototype._startup=function(a){var b=this;i(this.dom.container).text("");
this._attachExtras();i(this.dom.container).append(this.dom.panes);i(this.dom.panes).empty();var c=this.s.dt.state.loaded();if(this.c.viewTotal&&!this.c.cascadePanes&&null!==c&&void 0!==c&&void 0!==c.searchPanes&&void 0!==c.searchPanes.panes){for(var d=!1,e=0,h=c.searchPanes.panes;e<h.length;e++){var f=h[e];if(0<f.selected.length){d=!0;break}}if(d){d=0;for(e=this.s.panes;d<e.length;d++)f=e[d],f.s.showFiltered=!0}}d=0;for(e=this.s.panes;d<e.length;d++)f=e[d],f.rebuildPane(void 0,0<Object.keys(this.s.serverData).length?
this.s.serverData:void 0),i(this.dom.panes).append(f.dom.container);this.s.dt.page.info().serverSide||this.s.dt.draw();!this.s.stateRead&&(null!==c&&void 0!==c)&&(this.s.dt.page(c.start/this.s.dt.page.len()),this.s.dt.draw("page"));this.s.stateRead=!0;if(this.c.viewTotal&&!this.c.cascadePanes){c=0;for(d=this.s.panes;c<d.length;c++)f=d[c],f.updatePane()}this._updateFilterCount();this._checkMessage();a.on("preDraw.dtsps",function(){b._updateFilterCount();(b.c.cascadePanes||b.c.viewTotal)&&!b.s.dt.page.info().serverSide?
b.redrawPanes():b._updateSelection();b.s.filterPane=-1});this.s.dt.on("stateSaveParams.dtsp",function(a,c,d){if(d.searchPanes===void 0)d.searchPanes={};d.searchPanes.selectionList=b.s.selectionList});this.s.dt.on("xhr",function(){var a=false;if(!b.s.dt.page.info().serverSide)b.s.dt.one("preDraw",function(){if(!a){a=true;i(b.dom.panes).empty();for(var c=0,d=b.s.panes;c<d.length;c++){var f=d[c];f.clearData();f.rebuildPane(b.s.selectionList[b.s.selectionList.length-1]!==void 0?f.s.index===b.s.selectionList[b.s.selectionList.length-
1].index:false,void 0,void 0,true);i(b.dom.panes).append(f.dom.container)}b.s.dt.page.info().serverSide||b.s.dt.draw();b.c.cascadePanes||b.c.viewTotal?b.redrawPanes(b.c.cascadePanes):b._updateSelection();b._checkMessage()}})});c=0;for(d=this.s.panes;c<d.length;c++)if(f=d[c],void 0!==f&&void 0!==f.s.dtPane&&(void 0!==f.s.colOpts.preSelect||void 0!==f.customPaneSettings.preSelect)){e=f.s.dtPane.rows().data().toArray().length;for(h=0;h<e;h++)(-1!==f.s.colOpts.preSelect.indexOf(f.s.dtPane.cell(h,0).data())||
null!==f.customPaneSettings&&void 0!==f.customPaneSettings.preSelect&&-1!==f.customPaneSettings.preSelect.indexOf(f.s.dtPane.cell(h,0).data()))&&f.s.dtPane.row(h).select();f.updateTable()}if(void 0!==this.s.selectionList&&0<this.s.selectionList.length){c=this.s.selectionList[this.s.selectionList.length-1].index;d=0;for(e=this.s.panes;d<e.length;d++)f=e[d],f.s.lastSelect=f.s.index===c}0<this.s.selectionList.length&&this.c.cascadePanes&&this._cascadeRegen(this.s.selectionList);this._updateFilterCount();
a.on("destroy.dtsps",function(){for(var c=0,d=b.s.panes;c<d.length;c++)d[c].destroy();a.off(".dtsps");i(b.dom.clearAll).off(".dtsps");i(b.dom.container).remove();b.clearSelections()});if(this.c.clear)i(this.dom.clearAll).on("click.dtsps",function(){b.clearSelections()});if(this.s.dt.page.info().serverSide)a.on("preXhr.dt",function(a,c,d){if(d.searchPanes===void 0)d.searchPanes={};a=0;for(c=b.s.panes;a<c.length;a++){var f=c[a],e=b.s.dt.column(f.s.index).dataSrc();d.searchPanes[e]===void 0&&(d.searchPanes[e]=
{});if(f.s.dtPane!==void 0)for(var f=f.s.dtPane.rows({selected:true}).data().toArray(),h=0;h<f.length;h++)d.searchPanes[e][h]=f[h].filter}b.c.viewTotal&&b._prepViewTotal()});else a.on("preXhr.dt",function(){for(var a=0,c=b.s.panes;a<c.length;a++)c[a].clearData()});a.settings()[0]._searchPanes=this};m.prototype._prepViewTotal=function(){for(var a=this.s.filterPane,b=!1,c=0,d=this.s.panes;c<d.length;c++){var e=d[c];if(void 0!==e.s.dtPane){var h=e.s.dtPane.rows({selected:!0}).data().toArray().length;
0<h&&-1===a?(a=e.s.index,b=!0):0<h&&(a=null)}}c=0;for(d=this.s.panes;c<d.length;c++)if(e=d[c],void 0!==e.s.dtPane&&(e.s.filteringActive=!0,-1!==a&&null!==a&&a===e.s.index||!1===b))e.s.filteringActive=!1};m.prototype._updateFilterCount=function(){for(var a=0,b=0,c=this.s.panes;b<c.length;b++){var d=c[b];void 0!==d.s.dtPane&&(a+=d.getPaneCount())}b=this.s.dt.i18n("searchPanes.title","Filters Active - %d",a);i(this.dom.title).text(b);void 0!==this.c.filterChanged&&"function"===typeof this.c.filterChanged&&
this.c.filterChanged.call(this.s.dt,a)};m.prototype._updateSelection=function(){this.s.selectionList=[];for(var a=0,b=this.s.panes;a<b.length;a++){var c=b[a];void 0!==c.s.dtPane&&this.s.selectionList.push({index:c.s.index,rows:c.s.dtPane.rows({selected:!0}).data().toArray(),protect:!1})}this.s.dt.state.save()};m.version="1.2.1";m.classes={clear:"dtsp-clear",clearAll:"dtsp-clearAll",container:"dtsp-searchPanes",emptyMessage:"dtsp-emptyMessage",hide:"dtsp-hidden",panes:"dtsp-panesContainer",search:"dtsp-search",
title:"dtsp-title",titleRow:"dtsp-titleRow"};m.defaults={cascadePanes:!1,clear:!0,container:function(a){return a.table().container()},columns:[],filterChanged:void 0,layout:"columns-3",order:[],panes:[],viewTotal:!1};u=m;var v=function(a,b,c){function d(a,b){void 0===b&&(b=!1);var c=new e.Api(a),d=c.init().searchPanes||e.defaults.searchPanes;return(new u(c,d,b)).getNode()}g=a;q=a.fn.dataTable;i=a;var e=s=a.fn.dataTable;a.fn.dataTable.SearchPanes=u;a.fn.DataTable.SearchPanes=u;a.fn.dataTable.SearchPane=
t;a.fn.DataTable.SearchPane=t;b=a.fn.dataTable.Api.register;b("searchPanes()",function(){return this});b("searchPanes.clearSelections()",function(){return this.iterator("table",function(a){a._searchPanes&&a._searchPanes.clearSelections()})});b("searchPanes.rebuildPane()",function(a,b){return this.iterator("table",function(c){c._searchPanes&&c._searchPanes.rebuild(a,b)})});b("searchPanes.container()",function(){var a=this.context[0];return a._searchPanes?a._searchPanes.getNode():null});a.fn.dataTable.ext.buttons.searchPanesClear=
{text:"Clear Panes",action:function(a,b){b.searchPanes.clearSelections()}};a.fn.dataTable.ext.buttons.searchPanes={action:function(a,b,c,d){a.stopPropagation();this.popover(d._panes.getNode(),{align:"dt-container"});d._panes.rebuild(void 0,!0)},config:{},init:function(b,c,d){var e=new a.fn.dataTable.SearchPanes(b,a.extend({filterChanged:function(a){b.button(c).text(b.i18n("searchPanes.collapse",{"0":"SearchPanes",_:"SearchPanes (%d)"},a))}},d.config)),g=b.i18n("searchPanes.collapse","SearchPanes",
0);b.button(c).text(g);d._panes=e},text:"Search Panes"};a(c).on("preInit.dt.dtsp",function(a,b){if("dt"===a.namespace&&(b.oInit.searchPanes||e.defaults.searchPanes))b._searchPanes||d(b,!0)});e.ext.feature.push({cFeature:"P",fnInit:d});e.ext.features&&e.ext.features.register("searchPanes",d)};"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(a){return v(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);if(!b||!b.fn.dataTable)b=
require("datatables.net")(a,b).$;return v(b,a,a.document)}:v(window.jQuery,window,document)})();
