(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-searchpanes"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);if(!b||!b.fn.dataTable)b=require("datatables.net-bs4")(a,b).$;console.log(b.fn.dataTable);b.fn.dataTable.SearchPanes||(console.log("not present"),require("datatables.net-searchpanes")(a,b));return c(b,a,a.document)}:c(jQuery,window,document)})(function(c){var a=c.fn.dataTable;
c.extend(!0,a.SearchPane.classes,{buttonGroup:"btn-group col justify-content-end",disabledButton:"disabled",dull:"",narrow:"col",pane:{container:"table"},paneButton:"btn btn-light",pill:"pill badge badge-pill badge-secondary",search:"col-sm form-control search",searchCont:"input-group col-sm",searchLabelCont:"input-group-append",subRow1:"dtsp-subRow1",subRow2:"dtsp-subRow2",table:"table table-sm table-borderless",topRow:"dtsp-topRow row"});c.extend(!0,a.SearchPanes.classes,{clearAll:"dtsp-clearAll col-auto btn btn-light",
container:"dtsp-searchPanes",panes:"dtsp-panes dtsp-container",title:"dtsp-title col",titleRow:"dtsp-titleRow row"});return a.searchPanes});
