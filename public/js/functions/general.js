/**
 * General (jquery)
 */
var appUrl = jQuery('meta[name="app-url"]').attr('content');
var appLang = jQuery('meta[name="app-lang"]').attr('content');

$(document).ready(function () {
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': jQuery('meta[name="_token"]').attr('content')
    }
  });
});

/* 
* Add Client
*/
function add_client() {
  $('#searchclient').attr('disabled', true);
  let name = $('#client_name').val();
  let phone = $('#client_phone').val();
  let email = $('#client_email').val();
  let address = $('#client_address').val();
  let shippingCountry = $('#client_country_id').val();
  if (name && phone && email && address && shippingCountry) {
    $.ajax(
      {
        url: appUrl + "users/create_ajax",
        type: "GET",
        data: {
          name: name,
          phone: phone,
          email: email,
          address: address,
          country_id: shippingCountry,
        },
        success: function (response) {
          let data = response.data;
          if (data) {
            if (data.item) {
              $('#searchclient').val(data.item.value);
              $('#phone').val(data.item.phone);
              $('#email').val(data.item.email);
              $('#address').val(data.item.address);
              $('#country_id').val(data.item.country_id).trigger('change');
              $('#client_id').val(data.item.id);
              $('#addClientForm').reset();
              $('#createUserModal').modal('hide');
            }
          }
          $('#searchclient').removeAttr('disabled');
        },
        error: function () {
          $('#searchclient').removeAttr('disabled');
          $('#searchclient').val('');
          $('#client_id').val('');
        }
      }
    );
  } else {
    alert(appLang == 'en' ? 'Please fill in all required informations.' : 'من فضلك قم بتعبئة البيانات المطلوبة.');
  }
}
