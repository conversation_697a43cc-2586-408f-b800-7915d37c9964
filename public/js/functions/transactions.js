/**
 * Transactions (jquery)
 */
var appUrl = jQuery('meta[name="app-url"]').attr('content');
var appLang = jQuery('meta[name="app-lang"]').attr('content');
var level = 1;
$(document).ready(function () {
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': jQuery('meta[name="_token"]').attr('content')
    }
  });
  //Load Parent Categories on page load
  loadCategories();
});

// Load categories initially
function loadCategories() {
  $.get(appUrl + 'settings/product_categories/categories-with-products', function (categories) {
    let categoryHTML = `<select id="category_select" class="form-select" onchange="change_category();"><option value="">${appLang == 'en' ? 'Select Category' : 'حدد القسم'}</option>`;
    categories.forEach(function (category) {
      categoryHTML += `<option value="${category.id}">${appLang == 'en' ? category.title_en : category.title_ar}</option>`;
    });
    categoryHTML += '</select>';
    $('#category_container').html(categoryHTML);
    $('#category_select').select2({ dropdownParent: $("#AddProductModal") });
  }).fail(function (xhr, status, error) {
    alert('Error fetching categories: ' + error);
  });
}

// Load subcategories and products for a given parent and level
function loadSubcategoriesAndProducts(parentId, level) {
  $.get(appUrl + 'settings/product_categories/categories-with-subcategories-and-products/' + parentId, function (category) {
    clearProductTable();
    clearSubCategoryDropdown(level);
    if (category) {
      if (category.subs.length > 0) {
        let subcategoryHTML = `<select id="subcategory_select_${level}" onchange="change_sub_category(${level});" class="form-select"><option value="">${appLang == 'en' ? 'Select Subcategory' : 'حدد القسم الفرعي'}</option>`;

        category.subs.forEach(function (subcategory) {
          subcategoryHTML += `<option value="${subcategory.id}">${appLang == 'en' ? subcategory.title_en : subcategory.title_ar}</option>`;
        });
        subcategoryHTML += '</select>';
        $('#subcategory_container').append(subcategoryHTML);
        $(`#subcategory_select_${level}`).select2({ dropdownParent: $("#AddProductModal") });
      }

      if (category.products && category.products.length > 0) {
        category.products.forEach(function (product) {
          $('#products_table tbody').append(`
                <tr>
                  <td>${product.sku}</td>
                  <td>${appLang == 'en' ? product.title_en : product.title_ar}</td>
                  <td class="text-center">${product.price ? product.price : '---'}</td>
                  <td class="text-center"><button type="button" class="btn btn-success btn-icon add-product-btn" data-product='${JSON.stringify(product)}'><i class="fa fa-plus"></i></button></td>
                </tr>
              `);
        });
      } else {
        $('#products_table tbody').append(`
          <tr><td colspan="4" class="text-center">${appLang == 'en' ? 'No products available.' : 'لم يتم العثور علي منتجات.'}</td></tr>
        `);
      }
    }
  }).fail(function (xhr, status, error) {
    alert('Error fetching subcategories and products: ' + error);
  });
}

// When a category is selected, load the subcategories for it
function change_category() {
  const selectedCategoryId = $('#category_select').val();
  clearProductTable();
  clearSubCategoryDropdown(1);  // Clear the subcategory dropdown at level 1 and beyond

  if (selectedCategoryId) {
    loadSubcategoriesAndProducts(selectedCategoryId, 1); // Load subcategories for the selected category at level 1
  }
}

// When a subcategory is selected, load subcategories for it at the next level
function change_sub_category(level) {
  const selectedSubcategoryId = $(`#subcategory_select_${level}`).val();

  // Clear product table and subcategories for the next level when a subcategory is selected
  if (selectedSubcategoryId) {
    clearProductTable();
    loadSubcategoriesAndProducts(selectedSubcategoryId, level + 1); // Load subcategories for the next level
  } else {
    clearProductTable();
    clearSubCategoryDropdown(level + 1); // Clear subcategories below the current level
  }
}

// Clear a specific subcategory dropdown at a given level
function clearSubCategoryDropdown(level) {
  // Remove subcategory dropdowns from the given level and above
  for (let i = level; i <= 20; i++) { // Assume 20 as the max depth (adjust as necessary)
    const subcategorySelect = $(`#subcategory_select_${i}`);
    // Check if the element exists to avoid errors
    if (subcategorySelect.length) {
      // Destroy the Select2 instance
      subcategorySelect.select2('destroy');

      // Remove the element from the DOM
      subcategorySelect.remove();
    }
  }
}

// Clear the product table
function clearProductTable() {
  $('#products_table tbody').empty();
}

// Event listener for Add Product button
$(document).on('click', '.add-product-btn', function () {
  const product = $(this).data('product');  // Get product details from the button's data attribute

  // Clear the "No products added" row if it exists
  clearNoProductsAddedRow();

  // Generate a new index for form array (e.g., product[0][id], product[1][id], etc.)
  const productIndex = $('#main_table tbody tr').length;

  // Append the selected product to the main table and include form array fields
  $('#main_table tbody').append(`
    <tr data-sku="${product.sku}">
      <td>
        <input type="hidden" name="product[${productIndex}][sku]" value="${product.sku}" />  
        ${product.sku}
      </td>
      <td>
        <input type="hidden" name="product[${productIndex}][title]" value="${appLang == 'en' ? product.title_en : product.title_ar}" />  
        ${appLang == 'en' ? product.title_en : product.title_ar}
      </td>
      <td class="text-center">
        <input type="hidden" name="product[${productIndex}][price]" value="${product.price}" />
        ${product.price ? product.price : '---'}
      </td>
      <td class="text-center">
        <input type="number" class="form-control qty-input" value="1" min="1" 
               name="product[${productIndex}][qty]" data-sku="${product.sku}" />
      </td>
      <td>
        <input type="text" class="form-control design-url-input" 
               name="product[${productIndex}][url]" data-sku="${product.sku}" 
               placeholder="${appLang == 'en' ? 'Enter URL Or Design Code' : 'أدخل رابط او كود التصميم'}" />
      </td>
      <td class="text-center">
        <input type="hidden" name="product[${productIndex}][id]" value="${product.id}" />
        <button type="button" class="btn btn-danger btn-icon remove-product-btn"><i class="fa fa-times"></i></button>
      </td>
    </tr>
  `);
});


// Event listener to remove product from the main table
$(document).on('click', '.remove-product-btn', function () {
  const row = $(this).closest('tr');  // Get the row that contains the product to be removed
  const indexToRemove = row.index();  // Get the row index (this will correspond to the form array index)

  // Remove the row from the main table
  row.remove();

  // Re-index remaining rows in the table to maintain correct form array indices
  $('#main_table tbody tr').each(function (index) {
    // Reassign the "name" attribute of the input fields to maintain correct index
    $(this).find('.qty-input').attr('name', `product[${index}][qty]`);
    $(this).find('.design-url-input').attr('name', `product[${index}][url]`);
    $(this).find('input[type="hidden"]').attr('name', `product[${index}][id]`);
  });

  // If no rows are left in the table, show the "No products added" message
  if ($('#main_table tbody tr').length === 0) {
    $('#main_table tbody').append(`
      <tr>
        <td colspan="6" class="text-center">@lang('no_products_added')</td>
      </tr>
    `);
  }
});

// Function to check if the "No products added" row exists and clear it
function clearNoProductsAddedRow() {
  // Check if tbody contains a row with td having colspan="5"
  const noProductsRow = $('#main_table tbody').find('tr td[colspan="6"]');
  if (noProductsRow.length > 0) {
    // If found, empty the tbody to clear the "No products added" message
    $('#main_table tbody').empty();
  }
}

$(function () {
  $('#addClientForm').submit(function (event) {
    $('#searchclient').attr('disabled', true);
    event.preventDefault(); // Prevent form submission
    var formData = $(this).serialize(); // Serialize form data
    $.ajax({
      url: appUrl + "users/create_ajax",  // Replace with your actual form URL
      method: 'GET',
      data: formData,
      success: function (response) {
        // Handle successful form submission
        alert(appLang == 'en' ? 'Form submitted successfully!' : 'تم ارسال البيانات بنجاح!');
        let data = response.data;
        if (data) {
          if (data.item) {
            $('#searchclient').val(data.item.name);
            $('#phone').val(data.item.phone);
            $('#address').val(data.item.address);
            $('#country_id').val(data.item.country_id).trigger('change');
            $('#client_id').val(data.item.id);
            // Reset form fields after success
            $('#addClientForm')[0].reset(); // Reset the form
            $('.error').text(''); // Clear any error messages
            $('.is-invalid').removeClass('is-invalid'); // Remove any input field error styling
            $('#createUserModal').modal('hide');
          }
        }
        $('#searchclient').removeAttr('disabled');
      },
      error: function (xhr) {
        // Clear previous error messages
        $('.error').text(''); // You can adjust this selector based on your setup
        $('.is-invalid').removeClass('is-invalid'); // Remove any input field error styling
        // Parse the error response
        var errors = xhr.responseJSON.errors;

        // Loop through the errors and set them for respective input fields
        $.each(errors, function (field, messages) {
          var errorMessage = messages.join('<br>'); // Combine messages if more than one

          // Display error message next to the field
          $('#client_' + field + '_error').html(errorMessage);

          // Optionally, add a class to highlight the input field with errors
          $('#client_' + field).addClass('is-invalid');
        });
      }
    });
  });

  $('#searchclient').autocomplete({
    source: appUrl + "users/search",
    minlength: 1,
    autoFocus: true,
    appendTo: $('#searchclient').parents().eq(0),
    select: function (e, ui) {
      $('#client_id').val('');
      if (ui.item.id == '') {
        ui.item.value = '';
        $('#createUserModal').modal('show');
      } else {
        $('#searchclient').val(ui.item.value);
        $('#phone').val(ui.item.phone);
        $('#address').val(ui.item.address);
        $('#country_id').val(ui.item.country_id).trigger('change');
        $('#client_id').val(ui.item.id);
      }
    }
  });
});


