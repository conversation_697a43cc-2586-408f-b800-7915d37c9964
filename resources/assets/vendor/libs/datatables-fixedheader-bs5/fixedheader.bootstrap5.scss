@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';
@import '~datatables.net-fixedheader-bs5/css/fixedHeader.bootstrap5';

// Fixed header Style
.dt-fixedheader.fixedHeader-floating.table.dataTable {
  width: auto !important;
}
.dt-fixedheader.fixedHeader-locked.table.dataTable {
  display: none;
}

// Last style
@if $enable-light-style {
  .light-style {
    .dtfh-floatingparenthead {
      border-bottom: 2px solid light.$body-bg;
    }
    .dt-fixedheader.fixedHeader-floating.table.dataTable thead > tr > th,
    .dt-fixedheader.fixedHeader-locked.table.dataTable thead > tr > th {
      background-color: light.$card-bg;
      border-bottom-width: 1px;
      border-color: light.$body-bg;
    }
  }
}

// Dark style
@if $enable-dark-style {
  .dark-style {
    .dtfh-floatingparenthead {
      border-bottom: 2px solid dark.$body-bg;
    }
    .dt-fixedheader.fixedHeader-floating.table.dataTable thead > tr > th,
    .dt-fixedheader.fixedHeader-locked.table.dataTable thead > tr > th {
      background-color: dark.$card-bg;
      border-bottom-width: 1px;
      border-color: dark.$body-bg;
    }
  }
}
