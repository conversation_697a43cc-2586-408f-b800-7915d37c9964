@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '~datatables.net-responsive-bs5/css/responsive.bootstrap5';
@import 'mixins';

// Responsive table area '+' icon position
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  line-height: 0.9em;
  font-weight: bold;
}

// To scroll within datatable area
@media screen and (max-width: 1399.98px) {
  table.dataTable.table-responsive {
    display: block;
  }
}

// Modal table style
.modal.dtr-bs-modal {
  .modal-body {
    padding: 0;
  }
  .table {
    tr:last-child > td {
      border-bottom: 0;
    }
    .btn {
      box-shadow: none !important;
    }
    .emp_name {
      font-weight: light.$font-weight-semibold;
    }
  }
}
