@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

$flatpickr-content-padding-x: 0.875rem !default;
$flatpickr-content-padding-y: 0.75rem !default;
$flatpickr-cell-size: 2.125rem !default;
$flatpickr-animation-duration: 400ms !default;
$flatpickr-time-picker-height: 40px !default;
$flatpickr-week-height: 1.75rem !default;
$flatpickr-month-height: 3.15rem !default;
$flatpickr-width: ($flatpickr-cell-size * 7)+ ($flatpickr-content-padding-x * 2) !default;

@mixin keyframes($name) {
  @-webkit-keyframes #{$name} {
    @content;
  }

  @-moz-keyframes #{$name} {
    @content;
  }

  @keyframes #{$name} {
    @content;
  }
}

.flatpickr-calendar {
  position: absolute;
  visibility: hidden;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0;
  max-height: 0;
  border: 0;
  text-align: center;
  opacity: 0;
  animation: none;
  outline: 0;
  touch-action: manipulation;

  &.open,
  &.inline {
    visibility: visible;
    overflow: visible;
    max-height: 640px;
    opacity: 1;
  }

  &.open {
    display: inline-block;
  }

  &.animate.open {
    animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);
  }

  &:not(.inline):not(.open) {
    display: none !important;
  }

  &.inline {
    position: relative;
    top: 2px;
    display: block;
  }

  &.static {
    position: absolute;
    top: calc(100% + 2px);
  }

  &.static.open {
    z-index: 999;
    display: block;
  }

  &.hasWeeks {
    width: auto;
  }

  @include app-ltr {
    &.hasWeeks .flatpickr-days {
      border-bottom-left-radius: 0 !important;
    }
  }

  @include app-rtl {
    &.hasWeeks .flatpickr-days {
      border-bottom-right-radius: 0 !important;
    }
  }

  &.hasTime .flatpickr-time {
    height: $flatpickr-time-picker-height;
  }

  &.noCalendar.hasTime .flatpickr-time {
    height: auto;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

.flatpickr-wrapper {
  position: relative;
  display: inline-block;
}

.flatpickr-month {
  position: relative;
  overflow: hidden;
  height: $flatpickr-month-height;
  text-align: center;
  line-height: 1;
  user-select: none;
}

.flatpickr-prev-month,
.flatpickr-next-month {
  position: absolute;
  top: 0.75rem;
  z-index: 3;
  padding: 0 0.41rem;
  height: 1.75rem;
  width: 1.75rem;
  text-decoration: none;
  cursor: pointer;
  border-radius: 50rem;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    vertical-align: middle;
  }
}

.flatpickr-prev-month i,
.flatpickr-next-month i {
  position: relative;
}

.flatpickr-prev-month {
  &.flatpickr-prev-month {
    right: 3.5rem;
  }

  @include app-rtl {
    left: 3.5rem;
    right: auto;
    transform: scaleX(-1);
  }
}

.flatpickr-next-month {
  &.flatpickr-prev-month {
    right: 0;
    left: 0;
  }

  &.flatpickr-next-month {
    right: 1rem;
  }

  @include app-rtl {
    right: auto;
    left: 1rem;
    transform: scaleX(-1);
  }
}

.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
  opacity: 1;
}

.flatpickr-prev-month svg,
.flatpickr-next-month svg {
  width: 0.6rem;
}

.flatpickr-prev-month svg path,
.flatpickr-next-month svg path {
  transition: fill 0.1s;
  fill: inherit;
}

.numInputWrapper {
  position: relative;
  height: auto;

  input,
  span {
    display: inline-block;
  }

  input {
    width: 100%;
  }

  span {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 14px;
    height: 50%;
    line-height: 1;
    opacity: 0;
    cursor: pointer;

    @include app-rtl {
      right: auto;
      left: 0;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    &:active {
      background: rgba(0, 0, 0, 0.2);
    }

    &:after {
      content: '';
      display: block;
      width: 0;
      height: 0;
    }

    &.arrowUp {
      top: 0;
    }

    &.arrowUp:after {
      border-right: 4px solid transparent;
      border-bottom: 4px solid rgba(72, 72, 72, 0.6);
      border-left: 4px solid transparent;
    }

    &.arrowDown {
      top: 50%;
    }

    &.arrowDown:after {
      border-top: 4px solid rgba(72, 72, 72, 0.6);
      border-right: 4px solid transparent;
      border-left: 4px solid transparent;
    }

    svg {
      width: inherit;
      height: auto;
    }

    svg path {
      fill: rgba(255, 255, 255, 0.5);
    }
  }

  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }

  &:hover span {
    opacity: 1;
  }
}

.flatpickr-current-month {
  position: absolute;
  left: 1rem;
  top: 1.15rem;
  display: inline-block;
  text-align: left;
  font-weight: 600;
  display: flex;
  align-items: center;
  line-height: 1;
  transform: translate3d(0px, 0px, 0px);
  @include app-rtl {
    right: 1rem;
    left: auto;
  }

  .flatpickr-monthDropdown-months,
  input.cur-year {
    padding: 0 0 0 0.5ch;
    outline: none;
    vertical-align: middle !important;
    font-weight: 600;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    color: inherit;
    display: inline-block;
    box-sizing: border-box;
    background: transparent;
    border: 0;
    border-radius: 0;
  }

  .numInputWrapper {
    display: inline-block;
    width: 6ch;
  }

  .flatpickr-monthDropdown-months {
    appearance: menulist;
    cursor: pointer;
    // margin: -1px 0 0 0;
    position: relative;
    padding: 0;
  }

  input.cur-year {
    margin: 0;
    height: $flatpickr-month-height - 2.15rem;
    cursor: default;

    @include app-rtl {
      padding-right: 0.5ch;
      padding-left: 0;
    }

    &:focus {
      outline: 0;
    }

    &[disabled],
    &[disabled]:hover {
      background: transparent;
      pointer-events: none;
    }

    &[disabled] {
      opacity: 0.5;
    }
  }
}

.flatpickr-weekdaycontainer {
  display: flex;
  width: 100%;
  padding: 1rem 0.875rem 0;
}

.flatpickr-weekdays {
  display: flex;
  overflow: hidden;
  align-items: center;
  width: 100%;
  height: $flatpickr-week-height;
  text-align: center;
}

span.flatpickr-weekday {
  display: block;
  flex: 1;
  margin: 0;
  text-align: center;
  line-height: 1;
  cursor: default;
}

.dayContainer,
.flatpickr-weeks {
  padding: 1px 0 0 0;
}

.flatpickr-days {
  position: relative;
  display: flex;
  overflow: hidden;
  width: auto !important;

  &:focus {
    outline: 0;
  }

  .flatpickr-calendar.hasTime & {
    border-bottom: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
}

.dayContainer {
  display: inline-block;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  box-sizing: border-box;
  padding: 0;
  min-width: $flatpickr-cell-size * 7;
  max-width: $flatpickr-cell-size * 7;
  width: $flatpickr-cell-size * 7;
  outline: 0;
  opacity: 1;
  transform: translate3d(0px, 0px, 0px);
}

.flatpickr-day {
  position: relative;
  display: inline-block;
  flex-basis: 14.2857143%;
  justify-content: center;
  box-sizing: border-box;
  margin: 0;
  max-width: $flatpickr-cell-size;
  width: 14.2857143%;
  height: $flatpickr-cell-size;
  border: 1px solid transparent;
  background: none;
  text-align: center;
  font-weight: 400;
  line-height: calc(#{$flatpickr-cell-size} - 2px);
  cursor: pointer;

  &.inRange,
  &.prevMonthDay.inRange,
  &.nextMonthDay.inRange,
  &.today.inRange,
  &.prevMonthDay.today.inRange,
  &.nextMonthDay.today.inRange,
  &:hover,
  &.prevMonthDay:hover,
  &.nextMonthDay:hover,
  &:focus,
  &.prevMonthDay:focus,
  &.nextMonthDay:focus {
    outline: 0;
    cursor: pointer;
  }

  &.inRange:not(.startRange):not(.endRange) {
    border-radius: 0 !important;
    border: transparent;
  }

  &.disabled,
  &.disabled:hover {
    border-color: transparent;
    background: transparent;
    cursor: default;
    pointer-events: none;
  }

  &.prevMonthDay,
  &.nextMonthDay {
    border-color: transparent;
    background: transparent;
    cursor: default;
  }

  &.notAllowed,
  &.notAllowed.prevMonthDay,
  &.notAllowed.nextMonthDay {
    border-color: transparent;
    background: transparent;
    cursor: default;
  }

  &.week.selected {
    border-radius: 0;
  }

  @include app-ltr {
    &.selected.startRange,
    &.startRange.startRange,
    &.endRange.startRange {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &.selected.endRange,
    &.startRange.endRange,
    &.endRange.endRange {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  @include app-rtl {
    &.selected.startRange,
    &.startRange.startRange,
    &.endRange.startRange {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    &.selected.endRange,
    &.startRange.endRange,
    &.endRange.endRange {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}

.rangeMode .flatpickr-day {
  margin-top: 1px;
}

.flatpickr-weekwrapper {
  display: inline-block;
  float: left;

  .flatpickr-weeks {
    padding: $flatpickr-content-padding-y 0;
    background-clip: padding-box !important;

    @include app-ltr {
      .flatpickr-weeks {
        border-bottom-right-radius: 0 !important;
      }
    }

    @include app-rtl {
      .flatpickr-weeks {
        border-bottom-left-radius: 0 !important;
      }
    }
  }

  .flatpickr-calendar.hasTime .flatpickr-weeks {
    border-bottom: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .flatpickr-weekday {
    float: none;
    width: 100%;
    line-height: $flatpickr-week-height;
    position: relative;
    top: 0.5652rem;
  }

  span.flatpickr-day {
    display: block;
    max-width: none;
    width: $flatpickr-cell-size;
    background: none !important;
  }
}

.flatpickr-calendar.hasTime .flatpickr-weeks {
  border-bottom: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.flatpickr-innerContainer {
  display: block;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
}

.flatpickr-rContainer {
  display: inline-block;
  box-sizing: border-box;
  padding: 0;
}

.flatpickr-time {
  display: block;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  max-height: $flatpickr-time-picker-height;
  height: 0;
  outline: 0;
  background-clip: padding-box !important;
  text-align: center;
  line-height: $flatpickr-time-picker-height;

  &:after {
    content: '';
    display: table;
    clear: both;
  }

  .numInputWrapper {
    float: left;
    flex: 1;
    width: 40%;
    height: $flatpickr-time-picker-height;
  }

  &.hasSeconds .numInputWrapper {
    width: 26%;
  }

  &.time24hr .numInputWrapper {
    width: 49%;
  }

  input {
    position: relative;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    height: inherit;
    border: 0;
    border-radius: 0;
    background: transparent;
    box-shadow: none;
    text-align: center;
    line-height: inherit;
    cursor: pointer;

    &.flatpickr-minute,
    &.flatpickr-second {
      font-weight: normal;
    }

    &:focus {
      outline: 0;
      border: 0;
    }
  }

  .flatpickr-time-separator,
  .flatpickr-am-pm {
    display: inline-block;
    float: left;
    align-self: center;
    width: 2%;
    height: inherit;
    line-height: inherit;
    user-select: none;
  }

  .flatpickr-am-pm {
    width: 18%;
    outline: 0;
    text-align: center;
    font-weight: normal;
    cursor: pointer;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }

  .flatpickr-calendar.noCalendar & {
    box-shadow: none !important;
  }

  .flatpickr-calendar:not(.noCalendar) & {
    border-top: 0;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
}

.flatpickr-input[readonly] {
  cursor: pointer;
}

// Animations
//

@include keyframes(fpFadeInDown) {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
// Light layout
@if $enable-light-style {
  .light-style {
    .flatpickr-calendar {
      background: light.$dropdown-bg;
    }
    .flatpickr-prev-month,
    .flatpickr-next-month {
      background-color: light.rgba-to-hex(rgba(light.$black, 0.08), light.$card-bg);
      svg {
        fill: light.$body-color;
        stroke: light.$body-color;
      }
    }
    // Dimensions
    .flatpickr-calendar,
    .flatpickr-days {
      width: calc(#{$flatpickr-width} + #{light.$dropdown-border-width * 2}) !important;
    }

    .flatpickr-calendar.hasWeeks {
      width: calc(#{$flatpickr-width + $flatpickr-cell-size} + #{light.$dropdown-border-width * 3}) !important;
    }

    .flatpickr-calendar.open {
      z-index: light.$zindex-popover;
    }
    //!  Flatpickr provide default input as readonly, applying default input style to readonly
    .flatpickr-input[readonly],
    .flatpickr-input ~ .form-control[readonly] {
      background: light.$input-bg;
    }

    .flatpickr-days {
      background: light.$dropdown-bg;
      padding: 0.75rem $flatpickr-content-padding-x;
      border: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      border-top: 0;
      background-clip: padding-box;

      @include light.border-bottom-radius(light.$border-radius);
    }

    @include app-ltr-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-left: 0;
        padding-left: calc(#{$flatpickr-content-padding-x} + #{light.$dropdown-border-width});
        box-shadow: light.$dropdown-border-width 0 0 opacify(light.$dropdown-border-color, 0.05) inset;
      }
    }

    @include app-rtl-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-right: 0;
        padding-right: calc(#{$flatpickr-content-padding-x} + #{light.$dropdown-border-width});
        box-shadow: -(light.$dropdown-border-width) 0 0 opacify(light.$dropdown-border-color, 0.05) inset;
      }
    }

    .flatpickr-calendar {
      line-height: light.$line-height-base;
      box-shadow: light.$box-shadow;

      @include light.border-radius(light.$border-radius);

      &.hasTime:not(.noCalendar):not(.hasTime) .flatpickr-time {
        display: none !important;
      }

      &.hasTime .flatpickr-time {
        box-shadow: 0 1px 0 light.$border-color inset;
      }
    }
    .flatpickr-month {
      @include light.border-top-radius(light.$border-radius);
      border-bottom: light.$border-width solid light.$border-color;
      // ! FIX: OS Windows and Linux Browsers DD Option color
      option.flatpickr-monthDropdown-month {
        color: light.$body-color;
        background: light.$input-bg;
      }
    }

    span.flatpickr-weekday {
      font-weight: light.$font-weight-semibold;
      font-size: light.$small-font-size;
      background: light.$dropdown-bg;
      color: light.$headings-color;
    }

    .flatpickr-day {
      color: light.$body-color;
      @include light.border-radius(50rem);

      &:hover,
      &:focus,
      &.prevMonthDay:hover,
      &.nextMonthDay:hover,
      &.today:hover,
      &.prevMonthDay:focus,
      &.nextMonthDay:focus,
      &.today:focus {
        background: light.rgba-to-hex(rgba(light.$black, 0.08), light.$card-bg);
        &:not(.today) {
          border-color: transparent;
        }
      }

      &.prevMonthDay,
      &.nextMonthDay,
      &.flatpickr-disabled {
        color: light.$text-muted;

        &.today {
          border: none;
        }
      }

      &.disabled {
        color: light.$text-light !important;
      }

      &.selected.startRange.endRange {
        border-radius: light.$border-radius !important;
      }
      &.selected {
        box-shadow: light.$box-shadow-sm;
      }
    }

    .flatpickr-weeks {
      border-bottom: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      border-left: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      background: light.$dropdown-bg;

      @include light.border-bottom-radius(light.$border-radius);
      border-bottom-right-radius: 0;
    }

    @include app-rtl-style {
      .flatpickr-weeks {
        border-right: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
        border-left: 0;

        @include light.border-bottom-radius(light.$border-radius);
        border-bottom-left-radius: 0;
      }
    }

    .flatpickr-time {
      border: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      background: light.$dropdown-bg;

      @include light.border-radius(light.$border-radius);

      input {
        color: light.$body-color;
      }

      .numInputWrapper span {
        &.arrowUp:after {
          border-bottom-color: light.$text-muted;
        }

        &.arrowDown:after {
          border-top-color: light.$text-muted;
        }
      }

      .flatpickr-am-pm {
        color: light.$body-color;
      }

      .flatpickr-time-separator {
        color: light.$body-color;
      }
    }
  }
}

// Dark layout
@if $enable-dark-style {
  .dark-style {
    .flatpickr-calendar {
      background: dark.$dropdown-bg;
    }
    .flatpickr-prev-month,
    .flatpickr-next-month {
      background-color: dark.rgba-to-hex(rgba(dark.$base, 0.08), dark.$card-bg);
      svg {
        fill: dark.$body-color;
        stroke: dark.$body-color;
      }
    }
    .flatpickr-calendar,
    .flatpickr-days {
      width: calc(#{$flatpickr-width} + #{dark.$dropdown-border-width * 2}) !important;
    }

    .flatpickr-calendar.hasWeeks,
    .flatpickr-calendar.hasWeeks .flatpickr-days {
      width: calc(#{$flatpickr-width + $flatpickr-cell-size} + #{dark.$dropdown-border-width * 3}) !important;
    }

    .flatpickr-calendar.open {
      z-index: light.$zindex-popover;
    }

    //!  Flatpickr provide default input as readonly, applying default input style to readonly
    .flatpickr-input:not(.is-invalid):not(.is-valid) ~ .form-control:disabled,
    .flatpickr-input:not(.is-invalid):not(.is-valid)[readonly],
    .flatpickr-input:not(.is-invalid):not(.is-valid) ~ .form-control[readonly] {
      background-color: dark.$input-bg;
    }

    .flatpickr-days {
      border: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      border-top: 0;
      padding: 0.75rem $flatpickr-content-padding-x;
      background: dark.$dropdown-bg;
      background-clip: padding-box;

      @include dark.border-bottom-radius(dark.$border-radius);
    }

    @include app-ltr-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-left: 0;
        padding-left: calc(#{$flatpickr-content-padding-x} + #{dark.$dropdown-border-width});
        box-shadow: dark.$dropdown-border-width 0 0 opacify(dark.$dropdown-border-color, 0.05) inset;
      }
    }

    @include app-rtl-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-right: 0;
        padding-right: calc(#{$flatpickr-content-padding-x} + #{dark.$dropdown-border-width});
        box-shadow: -(dark.$dropdown-border-width) 0 0 opacify(dark.$dropdown-border-color, 0.05) inset;
      }
    }
    .flatpickr-calendar {
      line-height: dark.$line-height-base;
      box-shadow: dark.$box-shadow;

      @include dark.border-radius(dark.$border-radius);

      &.hasTime:not(.noCalendar):not(.hasTime) .flatpickr-time {
        display: none !important;
      }

      &.hasTime .flatpickr-time {
        box-shadow: 0 1px 0 dark.$border-color inset;
      }
    }

    .flatpickr-month {
      @include dark.border-top-radius(dark.$border-radius);
      border-bottom: dark.$border-width solid dark.$border-color;
      // ! FIX: OS Windows and Linux Browsers DD Option color
      option.flatpickr-monthDropdown-month {
        color: dark.$body-color;
        background: dark.$input-bg;
      }
    }

    span.flatpickr-weekday {
      font-weight: dark.$font-weight-semibold;
      font-size: dark.$small-font-size;
      background: dark.$dropdown-bg;
      color: dark.$headings-color;
    }

    .flatpickr-day {
      color: dark.$body-color;
      @include dark.border-radius(50rem);

      &:hover,
      &:focus,
      &.nextMonthDay:hover,
      &.prevMonthDay:hover,
      &.today:hover,
      &.nextMonthDay:focus,
      &.prevMonthDay:focus,
      &.today:focus {
        border-color: transparent;
        background: dark.rgba-to-hex(rgba(dark.$base, 0.08), dark.$card-bg);
      }

      &.nextMonthDay,
      &.prevMonthDay,
      &.flatpickr-disabled {
        color: dark.$text-muted;

        &.today {
          border: 0;
        }
      }

      &.selected.startRange.endRange {
        border-radius: dark.$border-radius !important;
      }

      &.disabled {
        color: darken(dark.$text-light, 30) !important;
      }
      &.selected {
        box-shadow: dark.$box-shadow-sm;
      }
    }

    .flatpickr-weeks {
      border-bottom: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      border-left: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      background: dark.$dropdown-bg;

      @include dark.border-bottom-radius(dark.$border-radius);
      border-bottom-right-radius: 0;
    }

    @include app-rtl-style {
      .flatpickr-weeks {
        border-right: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
        border-left: 0;
      }
    }

    .flatpickr-time {
      border: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      background: dark.$dropdown-bg;

      @include dark.border-radius(dark.$border-radius);

      input {
        color: dark.$body-color;
      }

      .numInputWrapper span {
        &.arrowUp:after {
          border-bottom-color: dark.$text-muted;
        }

        &.arrowDown:after {
          border-top-color: dark.$text-muted;
        }
      }

      .flatpickr-am-pm {
        color: dark.$body-color;
      }

      .flatpickr-time-separator {
        color: dark.$body-color;
      }
    }
  }
}
