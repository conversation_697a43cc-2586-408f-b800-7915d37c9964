@mixin fullcalendar-theme($background, $color) {
  .fc {
    // FC event
    @include bg-label-variant('.fc-event-primary:not(.fc-list-event)', $background);
    // FC list event
    .fc-event-primary.fc-list-event {
      .fc-list-event-dot {
        border-color: $background !important;
      }
    }

    .fc-button-primary:not(.fc-prev-button):not(.fc-next-button) {
      background-color: rgba($background, 0.16) !important;
      border: 0;
      color: $background;
      &.fc-button-active,
      &:hover {
        background-color: rgba($background, 0.24) !important;
        color: $background;
      }
    }
  }
}
