@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '~nouislider/dist/nouislider';
@import '../../scss/_custom-variables/libs';
@import 'mixins';

$noUiSlider-handle-width: 0.875rem !default;
$noUiSlider-handle-height: 0.875rem !default;
$noUiSlider-bar-height: 0.375rem !default;
$noUiSlider-vertical-height: 13.125rem !default;
$noUiSlider-tick-size: 0.5rem !default;
$noUiSlider-tick-label-font-size: 0.6875rem !default;

.noUi-target {
  direction: ltr !important;
  position: relative;
  border-width: 0;
  box-shadow: none;
}

.noUi-target,
.noUi-target * {
  touch-action: none;
  user-select: none;
  box-sizing: border-box;
}

.noUi-connects {
  height: $noUiSlider-bar-height;
}

.noUi-base,
.noUi-connects {
  z-index: 1;
  position: relative;
  height: 100%;
  width: 100%;
}

.noUi-horizontal .noUi-origin {
  height: 0;

  @include app-ltr {
    left: auto;
    right: 0;
  }
}

.noUi-vertical .noUi-origin {
  width: 0;
}

.noUi-handle {
  backface-visibility: hidden;
  outline: none !important;
  position: absolute;
  transform-origin: center;
  transition: transform 0.2s;

  &:before,
  &:after {
    display: none;
  }

  &.noUi-active {
    transform: scale(1.4, 1.4);
  }
}

.noUi-horizontal .noUi-handle.noUi-active .noUi-tooltip {
  transform: translate(-50%, 10%) scale(0.8, 0.8);
}

.noUi-vertical .noUi-handle.noUi-active .noUi-tooltip {
  transform: translate(10%, -50%) scale(0.8, 0.8);
}

.noUi-touch-area {
  height: 100%;
  width: 100%;
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  transition: top 0.3s, right 0.3s, bottom 0.3s, left 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

// Slider size and handle placement

.noUi-horizontal {
  height: $noUiSlider-bar-height;
}

.noUi-horizontal .noUi-handle {
  left: -($noUiSlider-handle-width * 0.5);
  width: $noUiSlider-handle-width;
  height: $noUiSlider-handle-height;
  top: -5px !important;

  @include app-ltr {
    right: -($noUiSlider-handle-width * 0.5);
    left: auto;
  }
}

.noUi-vertical {
  width: $noUiSlider-bar-height;
}

.noUi-vertical .noUi-handle {
  left: -(($noUiSlider-handle-height * 0.5) + 0.161);
  bottom: -($noUiSlider-handle-height);
  width: $noUiSlider-handle-height;
  height: $noUiSlider-handle-width;
}

// Styling
.noUi-target {
  border-radius: 10rem;
}

// Handles and cursors
.noUi-draggable {
  cursor: ew-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}

.noUi-handle {
  border-radius: 10rem;
  cursor: default;
}

// Disabled state
[disabled] .noUi-connect {
  opacity: 0.3;
}

[disabled] .noUi-handle {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

[disabled].noUi-target,
[disabled].noUi-handle,
[disabled] .noUi-handle {
  cursor: not-allowed;
}

// Base
.noUi-pips,
.noUi-pips * {
  box-sizing: border-box;
}

.noUi-pips {
  color: #999;
  position: absolute;
}

// Values
.noUi-value {
  position: absolute;
  white-space: nowrap;
  text-align: center;
  font-size: $noUiSlider-tick-label-font-size;
}

// Markings
.noUi-marker {
  position: absolute;
}

// Horizontal layout
.noUi-pips-horizontal {
  left: 0;
  top: 100%;
  padding: (($noUiSlider-handle-height - $noUiSlider-bar-height) * 0.5 + 0.375rem) 0 0 0;
  height: 5rem;
  width: 100%;
}

.noUi-value-horizontal {
  padding-top: 0.125rem;
  transform: translate(-50%, 50%);

  @include app-rtl {
    transform: translate(50%, 50%);
  }
}

.noUi-marker-horizontal.noUi-marker {
  height: $noUiSlider-tick-size;
  width: 1px;
}

@include app-rtl(false) {
  .noUi-horizontal {
    .noUi-origin {
      left: 0;
    }
  }
}

// Vertical layout
.noUi-pips-vertical {
  top: 0;
  left: 100%;
  padding: 0 0 0 (($noUiSlider-handle-height - $noUiSlider-bar-height) * 0.5 + 0.375rem);
  height: 100%;

  @include app-rtl {
    right: 100%;
    left: auto;
  }
}

.noUi-value-vertical {
  padding-left: $noUiSlider-tick-size + 0.375rem;
  transform: translate(0, 50%);

  @include app-rtl {
    right: 100%;
    padding-right: $noUiSlider-tick-size + 0.375rem;
    padding-left: 0;
  }
}

@include app-rtl(false) {
  .noUi-marker-vertical {
    right: 100%;
  }
}

.noUi-marker-vertical.noUi-marker {
  width: $noUiSlider-tick-size;
  height: 1px;
}

// Tooltips
.noUi-tooltip {
  position: absolute;
  display: block;
  padding: 0.282rem 0.25rem;
  border-radius: 0.25rem;
  text-align: center;
  line-height: 1;
  transition: transform 0.2s;
  font-size: 0.8125rem;
  border: 0;
}

.noUi-horizontal .noUi-tooltip {
  bottom: 157%;
  left: 50%;
  transform: translate(-50%, 0);
}

.noUi-vertical .noUi-tooltip {
  top: 50%;
  right: 200%;
  transform: translate(0, -50%);

  @include app-rtl {
    right: auto;
    left: 125%;
  }
}

@include app-rtl(false) {
  .noUi-vertical .noUi-handle.noUi-active .noUi-tooltip {
    transform: translate(-10%, -50%) scale(0.8, 0.8);
  }
}

// Light style
@if $enable-light-style {
  .light-style {
    $noUiSlider-handle-color: light.$body-color !default;
    $noUiSlider-default-bg: light.rgba-to-hex(light.$gray-600, light.$rgba-to-hex-bg);
    $noUiSlider-line-color: rgba(light.$black, 0.08);
    $noUiSlider-disabled-line-color: rgba(light.$black, 0.24);
    $noUiSlider-tick-label-color: light.$text-muted;

    .noUi-target {
      background: $noUiSlider-line-color;
    }

    .noUi-connect {
      background: $noUiSlider-default-bg !important;
    }

    .noUi-handle {
      border: 2px solid light.$card-bg;
      box-shadow: light.$box-shadow-sm;
      background: $noUiSlider-handle-color !important;
    }

    .noUi-target[disabled] {
      background: $noUiSlider-disabled-line-color;
    }

    .noUi-value {
      color: $noUiSlider-tick-label-color;
    }

    .noUi-marker {
      background: light.$border-color;
    }

    .noUi-tooltip {
      color: light.$headings-color;
      background: light.rgba-to-hex(rgba(light.$black, 0.16), light.$rgba-to-hex-bg);
      font-weight: light.$font-weight-semibold;
    }

    @each $color, $value in light.$theme-colors {
      @if $color !=primary {
        .noUi-#{$color} {
          @include nouislider-variant($value);
        }
      }
    }
  }
}

@if $enable-dark-style {
  .dark-style {
    $noUiSlider-handle-color: dark.rgba-to-hex(dark.$gray-800, dark.$rgba-to-hex-bg);
    $noUiSlider-default-bg: dark.rgba-to-hex(rgb(dark.$black, 0.1), dark.$rgba-to-hex-bg);
    $noUiSlider-line-color: rgba(dark.$base, 0.08);
    $noUiSlider-disabled-line-color: rgba(dark.$base, 0.24);
    $noUiSlider-tick-label-color: dark.$text-muted;

    .noUi-target {
      background: $noUiSlider-line-color;
    }

    .noUi-connect {
      background: $noUiSlider-default-bg;
    }

    .noUi-handle {
      border: 2px solid dark.$card-bg;
      box-shadow: dark.$box-shadow-sm;
      background: $noUiSlider-handle-color !important;
    }

    .noUi-target[disabled] {
      background: $noUiSlider-disabled-line-color;
    }

    .noUi-value {
      color: $noUiSlider-tick-label-color;
    }

    .noUi-marker {
      background: dark.$border-color;
    }

    .noUi-tooltip {
      color: dark.$headings-color;
      background: rgba(dark.$base, 0.16);
      font-weight: dark.$font-weight-semibold;
    }

    @each $color, $value in dark.$theme-colors {
      @if $color !=primary {
        .noUi-#{$color} {
          @include nouislider-variant($value);
        }
      }
    }
  }
}
