@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

// Variables
@import '~plyr/src/sass/settings/breakpoints';
@import '~plyr/src/sass/settings/colors';
@import '~plyr/src/sass/settings/cosmetics';
@import '~plyr/src/sass/settings/type';
@import '~plyr/src/sass/settings/badges';
@import '~plyr/src/sass/settings/captions';
@import '~plyr/src/sass/settings/controls';
@import '~plyr/src/sass/settings/helpers';
@import '~plyr/src/sass/settings/menus';
@import '~plyr/src/sass/settings/progress';
@import '~plyr/src/sass/settings/sliders';
@import '~plyr/src/sass/settings/tooltips';
@import '~plyr/src/sass/lib/animation';
@import '~plyr/src/sass/lib/functions';
@import '~plyr/src/sass/lib/mixins';

// Components
@import '~plyr/src/sass/base';
@import '~plyr/src/sass/components/badges';
@import '~plyr/src/sass/components/captions';
@import '~plyr/src/sass/components/control';
@import '~plyr/src/sass/components/controls';
@import '~plyr/src/sass/components/menus';
@import '~plyr/src/sass/components/sliders';
@import '~plyr/src/sass/components/poster';
@import '~plyr/src/sass/components/times';
@import '~plyr/src/sass/components/tooltips';
@import '~plyr/src/sass/components/progress';
@import '~plyr/src/sass/components/volume';
@import '~plyr/src/sass/types/audio';
@import '~plyr/src/sass/types/video';
@import '~plyr/src/sass/states/fullscreen';
@import '~plyr/src/sass/plugins/ads';
@import '~plyr/src/sass/plugins/preview-thumbnails';
@import '~plyr/src/sass/utils/animation';
@import '~plyr/src/sass/utils/hidden';

.plyr__progress__container,
.plyr__volume input[type='range'] {
  flex: 0 1 auto;
}
.plyr--audio .plyr__controls {
  padding: 0;
}

.plyr__menu__container {
  @include app-rtl {
    direction: rtl;
    text-align: right;

    .plyr__control--forward {
      &::after {
        left: 5px;
        right: auto;
        border-right-color: rgba($plyr-menu-color, 0.8);
        border-left-color: transparent;
      }

      &.plyr__tab-focus::after,
      &:hover::after {
        border-right-color: currentColor;
      }
    }

    .plyr__menu__value {
      padding-left: 1rem;
      padding-right: calc(calc(var(--plyr-control-spacing, 10px) * 0.7) * 1.5);
    }

    .plyr__control[role='menuitemradio'] {
      .plyr__menu__value {
        margin-right: auto;
        padding-left: 0;
      }
      &::before {
        margin-left: $plyr-control-spacing;
        margin-right: 0;
      }

      &::after {
        right: 15px;
        left: auto;
      }
    }
  }
}

@if $enable-light-style {
  .light-style {
    .plyr__tooltip {
      line-height: light.$line-height-sm;
      font-size: light.$font-size-sm;
    }
  }
}

@if $enable-dark-style {
  .dark-style {
    .plyr__tooltip {
      line-height: dark.$line-height-sm;
      font-size: dark.$font-size-sm;
    }

    .plyr--audio .plyr__controls {
      color: dark.$body-color;
      background-color: dark.$card-bg;
    }

    .plyr--full-ui.plyr--audio input[type='range'] {
      &::-webkit-slider-runnable-track {
        background-color: dark.$gray-100;
      }

      &::-moz-range-track {
        background-color: dark.$gray-100;
      }

      &::-ms-track {
        background-color: dark.$gray-100;
      }
    }

    .plyr--audio .plyr__progress__buffer {
      color: dark.$gray-200;
    }
  }
}
