@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

$tag-line-height: 1.5rem !default;
$tag-spacer: light.px-to-rem(floor(light.rem-to-px((light.$input-height-inner - $tag-line-height) * 0.5))) !default;

.tags-inline {
  .tagify__dropdown__wrapper {
    padding: 0 $tag-spacer $tag-spacer $tag-spacer;
  }
  .tagify__dropdown__item {
    display: inline-block;
    border-radius: 3px;
    padding: 0.3em 0.5em;
    margin: $tag-spacer $tag-spacer 0 0;
    font-size: 0.85em;
    transition: 0s;
  }
}

//RTL
@include app-rtl(false) {
  .tags-inline {
    .tagify__dropdown__item {
      margin: $tag-spacer 0 0 $tag-spacer;
    }
  }
}

// Light styles
@if $enable-light-style {
  .light-style {
    .tags-inline {
      .tagify__dropdown__item {
        border: 1px solid light.$border-color;
        background: light.rgba-to-hex(light.$gray-100);

        color: light.$body-color;

        &--active {
          color: light.$white !important;
        }

        &:hover {
          color: light.$white !important;
        }
      }
    }
  }
}

// Dark styles
@if $enable-dark-style {
  .dark-style {
    .tags-inline {
      .tagify__dropdown__item {
        border: 1px solid dark.$border-color;
        background: dark.$body-bg;
        color: dark.$body-color;

        &--active {
          color: dark.$white !important;
        }

        &:hover {
          color: dark.$white !important;
        }
      }
    }
  }
}
