// Dropdowns
// *****************************************************************

// On hover outline
[data-trigger='hover'] {
  outline: 0;
}

.dropdown-menu {
  box-shadow: $dropdown-box-shadow;
  // Animations
  animation: dropdownAnimation 0.1s;
  li {
    &:first-child {
      .dropdown-item {
        margin-top: 0;
      }
    }
    &:last-child {
      .dropdown-item {
        margin-bottom: 0;
      }
    }
  }

  // Mega dropdown inside the dropdown menu
  .mega-dropdown > & {
    left: 0 !important;
    right: 0 !important;
  }

  // Badge within dropdown menu
  .badge[class^='float-'],
  .badge[class*=' float-'] {
    position: relative;
    top: 0.071em;
  }

  // Dark style
  @if $dark-style {
    .list-group-item {
      border-color: rgba-to-hex($dropdown-divider-bg, $dropdown-bg);
    }
  }

  // For RTL
  @include rtl-style {
    text-align: right;
  }
}
// Dropdown item styles
.dropdown-item {
  line-height: $dropdown-link-line-height;
  width: calc(100% - ($dropdown-spacer * 4));
  margin: $dropdown-spacer ($dropdown-spacer * 2);
  border-radius: $dropdown-border-radius;
}

// Hidden dropdown toggle arrow
.dropdown-toggle.hide-arrow,
.dropdown-toggle-hide-arrow > .dropdown-toggle {
  &::before,
  &::after {
    display: none;
  }
}

// Dropdown caret icon

@if $enable-caret {
  // Dropdown arrow
  .dropdown-toggle::after {
    @include caret-down;
  }
  // Dropend arrow
  .dropend .dropdown-toggle::after {
    @include caret-right;
  }
  // Dropstart arrow
  .dropstart .dropdown-toggle::before {
    @include caret-left;
  }
  // Dropup arrow
  .dropup .dropdown-toggle::after {
    @include caret-up;
  }

  .dropstart .dropdown-toggle::before,
  .dropend .dropdown-toggle::after {
    vertical-align: $caret-vertical-align;
  }

  @include rtl-only {
    .dropdown-toggle:not(.dropdown-toggle-split)::after {
      margin-left: 0;
      margin-right: $caret-spacing;
    }
  }
}

@include rtl-only {
  // Dropdown menu alignment
  @each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

      .dropdown-menu#{$infix}-start {
        --bs-position: start;

        &[data-bs-popper] {
          left: auto;
          right: 0;
        }
      }

      .dropdown-menu#{$infix}-end {
        --bs-position: end;

        &[data-bs-popper] {
          left: 0;
          right: auto;
        }
      }
    }
  }
}
