// Offcanvas
// *******************************************************************************

.offcanvas {
  box-shadow: $offcanvas-box-shadow;
  .offcanvas-header {
    .btn-close {
      background-color: rgba-to-hex(rgba($gray-500, 0.16), $card-bg);
      padding: 0.44rem;
      margin-right: 0;
      background-image: str-replace(str-replace($btn-close-bg, '#{$btn-close-color}', $headings-color), '#', '%23');
    }
  }
}

// RTL
// *******************************************************************************
@include rtl-only {
  .offcanvas-start {
    right: 0;
    transform: translateX(100%);
  }

  .offcanvas-end {
    right: auto;
    left: 0;
    transform: translateX(-100%);
  }
}
