// Pagination
// *******************************************************************************

// Pagination Mixins
@each $color, $value in $theme-colors {
  @if $color != primary and $color != light {
    @include template-pagination-variant('.pagination-#{$color}', $value);
  }
}

// animation of active pagination

.page-item {
  &.active,
  &:active {
    .page-link {
      transform: scale(0.98);
      box-shadow: $box-shadow-sm;
    }
  }
}
// Pagination next, prev, first & last css padding
.page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding-top: $pagination-padding-y - 0.1rem;
      padding-bottom: $pagination-padding-y - 0.1rem;
    }
  }
}

// Pagination basic style
.page-link,
.page-link > a {
  @include border-radius($border-radius);

  line-height: $pagination-line-height;
  text-align: center;
  min-width: calc(
    #{'#{($font-size-base * $pagination-line-height) + ($pagination-padding-y * 2)} + #{$pagination-border-width * 2}'}
  );

  &:focus {
    color: $pagination-hover-color;
  }
}

.page-link.btn-primary {
  box-shadow: none !important;
}

// Pagination shapes + border less
.pagination {
  &.pagination-square .page-item a {
    @include border-radius(0);
  }
  &.pagination-round .page-item a {
    @include border-radius(50%);
  }
}

// Sizing
// *******************************************************************************

// Pagination Large
.pagination-lg .page-link,
.pagination-lg > li > a:not(.page-link) {
  min-width: calc(
    #{'#{($font-size-lg * $pagination-line-height) + ($pagination-padding-y-lg * 2)} + #{$pagination-border-width * 2}'}
  );
}
.pagination-lg > .page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding-top: $pagination-padding-y-lg - 0.0845rem;
      padding-bottom: $pagination-padding-y-lg - 0.1675rem;
    }
  }
}

// Pagination Small
.pagination-sm .page-link,
.pagination-sm > li > a:not(.page-link) {
  min-width: calc(
    #{'#{($font-size-sm * $pagination-line-height) + ($pagination-padding-y-sm * 2)} + #{$pagination-border-width * 2}'}
  );
}
.pagination-sm > .page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding-top: $pagination-padding-y-sm - 0.075rem;
      padding-bottom: $pagination-padding-y-sm - 0.048rem;
    }
  }
}

// RTL pagination
// *******************************************************************************

@include rtl-only {
  .pagination {
    padding-right: 0;
  }

  // Add spacing between pagination items
  .page-item + .page-item .page-link,
  .pagination li + li > a:not(.page-link) {
    margin-left: 0;
    margin-right: $pagination-margin-start;
  }

  .page-item {
    &.first,
    &.last,
    &.next,
    &.prev,
    &.previous {
      .page-link {
        svg {
          transform: rotate(180deg);
        }
      }
    }
  }
}
