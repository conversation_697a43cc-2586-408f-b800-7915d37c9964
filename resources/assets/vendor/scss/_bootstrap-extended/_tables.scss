// Tables
// ********************************************************************************/

@each $color, $value in $table-variants {
  @if $color != primary {
    @include template-table-variant($color, $value);
  }
}

// Style for table inside card
.table {
  &:not(.table-borderless) {
    margin-bottom: 0;
  }
  > :not(:first-child) {
    border-top: 0;
  }
  .table-light {
    th {
      border-bottom: 0;
    }
  }
}

@supports (-moz-appearance: none) {
  .table {
    .dropdown-menu.show {
      display: inline-table;
    }
  }
}
// Table heading style
.table th {
  text-transform: uppercase;
  font-size: $small-font-size;
  letter-spacing: 1px;
  padding-top: 0.88rem;
  padding-bottom: 0.88rem;
}
.table:not(.table-dark) {
  thead:not(.table-dark) {
    th {
      color: $headings-color;
    }
  }
}

// class for to remove table border bottom
.table-border-bottom-0 {
  tr:last-child {
    td,
    th {
      border-bottom-width: 0;
    }
  }
}

// Dark Table icon button color
.table.table-dark {
  a,
  .btn {
    i {
      color: $component-active-color;
    }
  }
}

// Flush spacing of left from first column ans right from last column
.table.table-flush-spacing {
  thead,
  tbody {
    tr > td:first-child {
      padding-left: 0;
    }
    tr > td:last-child {
      padding-right: 0;
    }
  }
}

// * Table inside card
// *******************************************************************************

// .card,
.nav-align-top,
.nav-align-right,
.nav-align-bottom,
.nav-align-left {
  .table:not(.table-dark),
  .table:not(.table-dark) thead:not(.table-dark) th,
  .table:not(.table-dark) tfoot:not(.table-dark) th,
  .table:not(.table-dark) td {
    border-color: $border-inner-color;
  }
}

// Dark styles

// Dark Table icon button color
@if $dark-style {
  .table.table-dark {
    a,
    .btn {
      i {
        color: rgba-to-hex($gray-700, $rgba-to-hex-bg);
      }
    }
  }
}
