// Variables
//
// Variables should follow the `$component-state-property-size` formula for
// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.
//
// (C) Custom variables for extended components of bootstrap only

// * Color system
// *******************************************************************************

// scss-docs-start gray-color-variables
$white: #fff !default;
$black: #4b465c !default;
$gray-25: rgba($black, 0.015) !default; // (C)
$gray-50: rgba($black, 0.03) !default; // (C)
$gray-100: rgba($black, 0.05) !default;
$gray-200: rgba($black, 0.2) !default;
$gray-300: rgba($black, 0.3) !default;
$gray-400: rgba($black, 0.4) !default;
$gray-500: rgba($black, 0.5) !default;
$gray-600: rgba($black, 0.6) !default;
$gray-700: rgba($black, 0.7) !default;
$gray-800: rgba($black, 0.8) !default;
$gray-900: rgba($black, 0.9) !default;
// scss-docs-end gray-color-variables

// scss-docs-start gray-colors-map
$grays: (
  '25': $gray-25,
  '50': $gray-50
) !default;
// scss-docs-end gray-colors-map

// scss-docs-start color-variables
$blue: #007bff !default;
$indigo: #6610f2 !default;
$purple: #333669 !default;
$pink: #e83e8c !default;
$red: #ea5455 !default;
$orange: #fd7e14 !default;
$yellow: #ff9f43 !default;
$green: #28c76f !default;
$teal: #20c997 !default;
$cyan: #00cfe8 !default;
// scss-docs-end color-variables

// scss-docs-start theme-color-variables
$primary: $purple !default;
$secondary: #a8aaae !default;
$success: $green !default;
$info: $cyan !default;
$warning: $yellow !default;
$danger: $red !default;
$light: #dfdfe3 !default;
$dark: #4b4b4b !default;
$gray: $gray-100 !default; // (C)
// scss-docs-end theme-color-variables

// scss-docs-start theme-colors-map
$theme-colors: (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'light': $light,
  'dark': $dark,
  'gray': $gray
) !default;
// scss-docs-end theme-colors-map

$color-scheme: 'light' !default; // (C)
// The contrast ratio to reach against white, to determine if color changes from "light" to "dark". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.
// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast
$min-contrast-ratio: 1.7 !default;

// * Options
// *******************************************************************************

$enable-negative-margins: true !default;
$enable-validation-icons: false !default;

// Prefix for :root CSS variables
$variable-prefix: bs- !default;

// * Spacing
// *******************************************************************************

$spacer: 1rem !default;

$sizes-px: (
  px-14: 14px,
  px-18: 18px,
  px-20: 20px,
  px-30: 30px,
  px-40: 40px,
  px-50: 50px,
  px-75: 75px,
  px-100: 100px,
  px-150: 150px,
  px-200: 200px,
  px-250: 250px,
  px-300: 300px,
  px-350: 350px,
  px-400: 400px,
  px-500: 500px,
  px-600: 600px,
  px-700: 700px,
  px-800: 800px,
  auto: auto
) !default; // (C)

// * Body
// *******************************************************************************

$body-bg: #f8f7fa !default;
$rgba-to-hex-bg: #fff !default; // (C)
$body-color: rgba-to-hex($gray-800, $rgba-to-hex-bg) !default;
$rgba-to-hex-bg-inverted: #000 !default; // (C)

// * Links
// *******************************************************************************

$link-color: $primary !default;
$link-decoration: none !default;
$link-shade-percentage: 10% !default;
$link-hover-color: shift-color($link-color, $link-shade-percentage) !default;
$link-hover-decoration: null !default;

// * Grid
// *******************************************************************************

// Grid containers

// scss-docs-start container-max-widths
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1440px // Custom xxl size
) !default;
// scss-docs-end container-max-widths

$grid-gutter-width: 1.5rem !default;
$container-padding-x: 1.5rem !default; // (C)
$container-padding-x-sm: 1rem !default; // (C)
$container-padding-y: 1.5rem !default; // (C)

// * Components
// *******************************************************************************

// scss-docs-start border-variables
$border-width: 1px !default;
$border-color: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default;
$border-inner-color: rgba($black, 0.075) !default; // (C)
// scss-docs-end border-variables

// scss-docs-start border-radius-variables
$border-radius: 0.375rem !default;
$border-radius-xl: 0.625rem !default; // (C)
$border-radius-lg: 0.5rem !default;
$border-radius-sm: 0.25rem !default;
$border-radius-xs: 0.125rem !default; // (C)
// scss-docs-end border-radius-variables

// scss-docs-start box-shadow-variables
$box-shadow: 0 0.25rem 1rem rgba(rgba-to-hex($gray-500, $rgba-to-hex-bg), 0.45) !default;
$box-shadow-sm: 0 0.125rem 0.25rem rgba(rgba-to-hex($gray-500, $rgba-to-hex-bg), 0.3) !default;
$box-shadow-lg: 0 0.625rem 1.25rem rgba(rgba-to-hex($gray-500, $rgba-to-hex-bg), 0.4) !default;
// scss-docs-end box-shadow-variables

$component-active-color: $white !default;
$component-active-bg: $primary !default;

$component-hover-color: $primary !default; // (C)
$component-hover-bg: rgba($primary, 0.08) !default; // (C)

$component-line-height: 1.54 !default; // (C)
$component-focus-shadow-width: 2px !default; // (C)

$floating-component-border-color: rgba($black, 0.05) !default; // (C)
$floating-component-shadow: 0 0.31rem 1.25rem 0 $gray-400 !default; // (C) used for modal and range

$hr-color: $border-color !default;
$hr-opacity: 1 !default;
$bordered-row-border-color: $hr-color !default; // (C)

// scss-docs-start caret-variables
$caret-width: 0.42em !default;
$caret-vertical-align: middle !default;
$caret-spacing: 0.5em !default;
// scss-docs-end caret-variables

// * Typography
// *******************************************************************************

// scss-docs-start font-variables
$font-family-sans-serif: 'Public Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen', 'Ubuntu', 'Cantarell',
  'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !default;
$font-family-serif: Georgia, 'Times New Roman', serif !default; // (C)
$font-family-monospace: 'SFMono-Regular', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !default;
// stylelint-enable value-keyword-case
$font-family-base: var(--#{$variable-prefix}font-sans-serif) !default;
$font-family-code: var(--#{$variable-prefix}font-monospace) !default;

// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins
// $font-size-base effects the font size of the body text
$font-size-root: 16px !default;
$font-size-base: 0.9375rem !default; // Assumes the browser default, typically `16px`
$font-size-xl: 1.25rem !default; // (C)
$font-size-lg: 1rem !default;
$font-size-sm: 0.75rem !default;
$font-size-xs: 0.69rem !default; // (C)

$font-weight-semibold: 600 !default; // (C)
$font-weight-normal: 400 !default;
$font-weight-bolder: 700 !default;
$font-weight-lighter: 300 !default;

$line-height-base: 1.47 !default;
$line-height-xl: 1.5 !default; // (C)
$line-height-lg: 1.375 !default;
$line-height-sm: 1 !default;
$line-height-xs: 0.75 !default; // (C)

$h1-font-size: 2.375rem !default;
$h2-font-size: 2rem !default;
$h3-font-size: 1.625rem !default;
$h4-font-size: 1.375rem !default;
$h5-font-size: 1.125rem !default;
$h6-font-size: $font-size-base !default;
// scss-docs-end font-variables

// scss-docs-start headings-variables
$headings-margin-bottom: $spacer !default;
$headings-font-weight: $font-weight-semibold !default;
$headings-line-height: 1.37 !default;
$headings-color: rgba-to-hex($gray-900, $rgba-to-hex-bg) !default;
// scss-docs-end headings-variables

// scss-docs-start display-headings
$display-font-sizes: (
  1: 4.75rem,
  2: 4.375rem,
  3: 3.875rem,
  4: 3.375rem,
  5: 3rem,
  6: 2.625rem
) !default;

$display-font-weight: 500 !default;

// scss-docs-end display-headings

// scss-docs-start type-variables

$lead-font-size: $spacer * 1.125 !default;

$tiny-font-size: 70% !default; // (C)
$small-font-size: 0.8125rem !default;
$big-font-size: 112% !default; // (C)
$large-font-size: 150% !default; // (C)
$xlarge-font-size: 170% !default; // (C)

$text-muted: rgba-to-hex($gray-500, $rgba-to-hex-bg) !default;
$text-muted-hover: rgba-to-hex($gray-600, $rgba-to-hex-bg) !default; // (C)

$text-light: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default; // (C)
$text-lighter: rgba-to-hex($gray-300, $rgba-to-hex-bg) !default; // (C)
$text-lightest: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default; // (C)
// scss-docs-end type-variables

// * Z-index master list
// *******************************************************************************

$zindex-menu-fixed: 1080 !default;
$zindex-modal: 1090 !default;
$zindex-modal-backdrop: $zindex-modal - 1 !default;
// $zindex-modal-top: 1090 !default; // (C)
$zindex-offcanvas: 1090 !default;
$zindex-offcanvas-backdrop: $zindex-offcanvas - 1 !default;
$zindex-layout-mobile: 1100 !default; // (C)
$zindex-popover: 1091 !default;
$zindex-toast: 1095 !default; // (C)
$zindex-tooltip: 1099 !default;
$zindex-notification: 999999 !default; // (C)

// * Tables
// *******************************************************************************

// scss-docs-start table-variables
$table-cell-padding-y: 0.55rem !default;
$table-cell-padding-x: 1.25rem !default;
$table-cell-padding-y-sm: 0.25rem !default;
$table-cell-padding-x-sm: $table-cell-padding-x !default;

$table-cell-vertical-align: middle !default;

$table-th-font-weight: $font-weight-semibold !default;

$table-striped-bg-factor: 0.02 !default;
$table-striped-bg: rgba-to-hex(rgba($black, $table-striped-bg-factor), $rgba-to-hex-bg) !default;

$table-active-bg-factor: 0.08 !default;
$table-active-bg: rgba($primary, $table-active-bg-factor) !default;

$table-hover-bg-factor: 0.04 !default;

$table-border-factor: 0.2 !default;

$table-bg-scale: -92% !default;

// * Buttons + Forms
// *******************************************************************************

$input-btn-padding-y: 0.6rem !default;
$input-btn-padding-x: 1.25rem !default;
$input-btn-font-size: $font-size-base !default;
$input-btn-line-height: 1.125 !default;

$input-btn-focus-width: 0.05rem !default;
$input-btn-focus-color-opacity: 0.1 !default;
$input-btn-focus-color: rgba($component-active-bg, $input-btn-focus-color-opacity) !default;
$input-btn-focus-blur: 0.25rem !default;
$input-btn-focus-box-shadow: 0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;

$input-btn-padding-y-xs: 0.175rem !default; // (C)
$input-btn-padding-x-xs: 0.75rem !default; // (C)
$input-btn-font-size-xs: $font-size-xs !default; // (C)
$input-btn-line-height-xs: $line-height-xs !default; // (C)

$input-btn-padding-y-sm: 0.41rem !default;
$input-btn-padding-x-sm: 0.875rem !default;
$input-btn-font-size-sm: 0.8125rem !default;
$input-btn-line-height-sm: $line-height-sm !default;

$input-btn-padding-y-lg: 0.84rem !default;
$input-btn-padding-x-lg: 1.625rem !default;
$input-btn-font-size-lg: 1.0625rem !default;
$input-btn-line-height-lg: $line-height-lg !default;

$input-btn-padding-y-xl: 0.875rem !default; // (C)
$input-btn-padding-x-xl: 1.75rem !default; // (C)
$input-btn-font-size-xl: $font-size-xl !default; // (C)
$input-btn-line-height-xl: $line-height-lg !default; // (C)

// * Buttons
// *******************************************************************************

$btn-padding-y-xs: $input-btn-padding-y-xs !default; // (C)
$btn-padding-x-xs: $input-btn-padding-x-xs !default; // (C)
$btn-font-size-xs: $input-btn-font-size-xs !default; // (C)

$btn-padding-y-xl: $input-btn-padding-y-xl !default; // (C)
$btn-padding-x-xl: $input-btn-padding-x-xl !default; // (C)
$btn-font-size-xl: $input-btn-font-size-xl !default; // (C)

$btn-line-height-xs: $input-btn-line-height-xs !default; // (C)
$btn-line-height-sm: $line-height-sm; //(C)
$btn-line-height-lg: $line-height-lg; //(C)
$btn-line-height-xl: $input-btn-line-height-xl !default; // (C)

$btn-font-weight: 500 !default;
$btn-box-shadow: 0px 2px 4px rgba(165, 163, 174, 0.4) !default;
$btn-focus-box-shadow: none !default;
$btn-active-box-shadow: none !default;

$btn-border-radius-xs: $border-radius-sm !default; // (C)
$btn-border-radius-xl: $border-radius-lg !default; // (C)

$btn-transition: all 0.2s ease-in-out !default;

$btn-label-bg-shade-amount: 84% !default; // (C)
$btn-label-bg-tint-amount: 85% !default; // (C)
$btn-label-hover-shade-amount: 26% !default; // (C)
$btn-label-hover-tint-amount: 76% !default; // (C)
$btn-label-disabled-bg-shade-amount: 84% !default; // (C)
$btn-label-disabled-bg-tint-amount: 85% !default; // (C)

$btn-hover-bg-shade-amount: 10% !default;
$btn-hover-bg-tint-amount: 25% !default;
$btn-hover-border-shade-amount: 10% !default;
$btn-hover-border-tint-amount: 10% !default;
$btn-active-bg-shade-amount: 10% !default;
$btn-active-bg-tint-amount: 20% !default;
$btn-active-border-shade-amount: 10% !default;
$btn-active-border-tint-amount: 10% !default;

// Outline buttons
$btn-outline-hover-bg-shade-amount: 10% !default; // (C)
$btn-outline-hover-bg-tint-amount: 92% !default; // (C)
$btn-outline-active-bg-shade-amount: 10% !default; // (C)
$btn-outline-active-bg-tint-amount: 84% !default; // (C)

// * Forms
// *******************************************************************************

// scss-docs-start form-text-variables
$form-text-font-size: $input-btn-font-size-sm !default;
// scss-docs-end form-text-variables

// scss-docs-start form-label-variables
$form-label-margin-bottom: 0.25rem !default;
$form-label-font-size: $input-btn-font-size-sm !default;
$form-label-color: $headings-color !default;
$form-label-letter-spacing: inherit !default; //(C)
$form-label-text-transform: inherit !default; //(C)
// scss-docs-end form-label-variables

// scss-docs-start form-input-variables
$input-padding-y: 0.422rem !default;
$input-padding-x: $input-btn-padding-x-sm !default;
$input-line-height: $line-height-xl !default;
$input-font-size: $input-btn-font-size !default;

$input-padding-y-sm: 0.266rem !default;
$input-padding-x-sm: 0.625rem !default;

$input-padding-y-lg: 0.594rem !default;
$input-padding-x-lg: $spacer !default;
$input-font-size-lg: 1.125rem !default;

$input-bg: $white !default;
$input-disabled-bg: rgba($black, 0.08) !default;

$input-color: $body-color !default;
$input-border-color: $border-color !default;

$input-focus-border-color: $component-active-bg !default;
$input-focus-box-shadow: $box-shadow-sm !default;

$input-placeholder-color: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default;

$input-height-inner: px-to-rem(
  floor(rem-to-px(($input-btn-font-size * $input-btn-line-height) + ($input-btn-padding-y * 2)))
) !default;
$input-height-inner-sm: px-to-rem(
  floor(rem-to-px(($input-btn-font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2)))
) !default; // (C)
$input-height-inner-lg: px-to-rem(
  floor(rem-to-px(($font-size-lg * $line-height-lg) + ($input-btn-padding-y-lg * 2)))
) !default; // (C)
// scss-docs-end form-input-variables

// scss-docs-start form-check-variables
$form-check-input-width: 1.2em !default;
$form-datatables-check-input-size: 17px !default; // (C) For datatables with checkbox- update according to $form-check-input-width
$form-check-min-height: $font-size-base * $line-height-base !default;
$form-check-input-border: 1px solid $input-border-color !default;

$form-check-label-cursor: pointer !default;
$form-check-label-disabled-color: $text-muted !default; // (C)R

$form-check-input-focus-box-shadow: $input-focus-box-shadow !default;

$form-check-input-checked-color: $component-active-color !default;
$form-check-input-checked-bg-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='11' height='13' viewBox='0 0 15 14' fill='none'%3E%3Cpath d='M3.41667 7L6.33333 9.91667L12.1667 4.08333' stroke='#{$form-check-input-checked-color}' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !default;

$form-check-radio-checked-bg-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='5' y='5' width='8' height='8' rx='4' fill='#{$form-check-input-checked-color}'/%3E%3C/svg%3E%0A") !default;

$form-check-input-indeterminate-color: $component-active-color !default;
$form-check-input-indeterminate-bg-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.5 9H12.5' stroke='#{$form-check-input-indeterminate-color}' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A") !default;

$form-check-input-disabled-bg: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default; // (C)
// scss-docs-end form-check-variables

// scss-docs-start form-switch-variables
$form-switch-color: $gray-300 !default;
$form-switch-width: 2em !default;
$form-switch-padding-start: $form-switch-width + 0.5em !default;
$form-switch-bg-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='9' cy='9' r='6' fill='#{$form-switch-color}'/%3E%3C/svg%3E%0A") !default;

$form-switch-focus-color: $input-focus-border-color !default;
$form-switch-focus-bg-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='9' cy='9' r='6' fill='#{$form-switch-focus-color}'/%3E%3C/svg%3E%0A") !default;

$form-switch-checked-color: $component-active-color !default;
$form-switch-checked-bg-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='9' cy='9' r='6' fill='#{$form-switch-checked-color}'/%3E%3C/svg%3E%0A") !default;

$form-switch-checked-bg-position-rtl: left center !default; //(C)
// scss-docs-end form-switch-variables

//input-group-variables
$input-group-addon-padding-x: $input-btn-padding-x-xs !default;
$input-group-addon-bg: $input-bg !default;
// scss-docs-end input-group-variables

// scss-docs-start form-select-variables
$form-select-padding-y: $input-padding-y !default;
$form-select-padding-x: $input-padding-x !default;
$form-select-indicator-padding: $form-select-padding-x * 2.8 !default;
$form-select-disabled-color: $text-muted !default;
$form-select-disabled-bg: $input-disabled-bg !default;
$form-select-bg-size: 22px 20px !default;
$form-select-indicator-color: $body-color !default;
$form-select-indicator: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 7.5L10 12.5L15 7.5' stroke='#{$form-select-indicator-color}' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M5 7.5L10 12.5L15 7.5' stroke='white' stroke-opacity='0.2' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !default;

$form-select-focus-box-shadow: $input-focus-box-shadow !default;

$form-select-padding-y-sm: $input-padding-y-sm !default;
$form-select-padding-x-sm: $input-padding-x-sm !default;

$form-select-padding-y-lg: $input-padding-y-lg !default;
$form-select-padding-x-lg: $input-padding-x-lg !default;
// scss-docs-end form-select-variables

// scss-docs-start form-range-variables
$form-range-track-height: 0.125rem !default;
$form-range-track-bg: $gray-200 !default;
$form-range-track-box-shadow: none !default;
$form-range-track-disabled-bg: $gray-50 !default; // (C)

$form-range-thumb-width: 0.75rem !default;
$form-range-thumb-height: $form-range-thumb-width !default;
$form-range-thumb-bg: $white !default;
$form-range-thumb-box-shadow: 0 0 0 1px rgba($black, 0.1), $floating-component-shadow !default;
$form-range-thumb-focus-box-shadow: 0 0 0 3px rgba($black, 0.1), $floating-component-shadow !default;
$form-range-thumb-active-bg: $form-range-thumb-bg !default;
$form-range-thumb-disabled-bg: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default;
// scss-docs-end form-range-variables

// scss-docs-start form-floating-variables
$form-floating-label-opacity: 0.75 !default;
$form-floating-transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out !default;
$form-floating-label-transform-rtl: scale(0.85) translateY(-0.5rem) translateX(-0.15rem) !default; // (C)
// scss-docs-end form-floating-variables

// Form validation

// scss-docs-start form-feedback-variables
$form-feedback-valid-color: $success !default;
$form-feedback-invalid-color: $danger !default;

$form-select-feedback-icon-padding: $form-select-indicator-padding + $input-height-inner !default; // (C)
$form-select-feedback-icon-padding-sm: $form-select-indicator-padding + $input-height-inner-sm !default; // (C)
$form-select-feedback-icon-padding-lg: $form-select-indicator-padding + $input-height-inner-lg !default; // (C)
// scss-docs-end form-feedback-variables

// * Navs
// *******************************************************************************

$nav-spacer: 0.125rem !default; // (C)

$nav-link-padding-y: 0.543rem !default;
$nav-link-padding-x: 1.25rem !default;
$nav-link-color: $headings-color !default;
$nav-link-disabled-color: $text-muted !default;
$nav-link-line-height: $line-height-lg !default; // (C)

$nav-link-padding-y-lg: 0.6rem !default; // (C)
$nav-link-padding-x-lg: 1.5rem !default; // (C)
$nav-link-line-height-lg: $line-height-lg !default; // (C)

$nav-link-padding-y-sm: 0.376rem !default; // (C)
$nav-link-padding-x-sm: 1rem !default; // (C)
$nav-link-line-height-sm: $line-height-sm !default; // (C)

$nav-tabs-border-color: transparent !default;
$nav-tabs-link-active-color: $component-active-bg !default;
$nav-tabs-link-active-bg: $white !default;

// * Navbar
// *******************************************************************************

$navbar-toggler-padding-y: 0.5rem !default;
$navbar-toggler-padding-x: 0.7rem !default;
$navbar-toggler-font-size: 0.625rem !default;

$navbar-dark-color: rgba($white, 0.8) !default;
$navbar-dark-hover-color: $white !default;
$navbar-dark-active-color: $white !default;
$navbar-dark-disabled-color: rgba($white, 0.4) !default;
$navbar-dark-toggler-icon-bg: url("data:image/svg+xml,%3Csvg width='14px' height='11px' viewBox='0 0 14 11' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M0,0 L14,0 L14,1.75 L0,1.75 L0,0 Z M0,4.375 L14,4.375 L14,6.125 L0,6.125 L0,4.375 Z M0,8.75 L14,8.75 L14,10.5 L0,10.5 L0,8.75 Z' id='path-1'%3E%3C/path%3E%3C/defs%3E%3Cg id='💎-UI-Elements' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='12)-Navbar' transform='translate(-1174.000000, -1290.000000)'%3E%3Cg id='Group' transform='translate(1174.000000, 1288.000000)'%3E%3Cg id='Icon-Color' transform='translate(0.000000, 2.000000)'%3E%3Cuse fill='#{$navbar-dark-color}' xlink:href='%23path-1'%3E%3C/use%3E%3Cuse fill-opacity='0.1' fill='#{$navbar-dark-color}' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !default;

$navbar-light-color: $gray-500 !default;
$navbar-light-hover-color: $body-color !default;
$navbar-light-active-color: $body-color !default;
$navbar-light-disabled-color: $gray-300 !default;
$navbar-light-toggler-icon-bg: url("data:image/svg+xml,%3Csvg width='14px' height='11px' viewBox='0 0 14 11' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M0,0 L14,0 L14,1.75 L0,1.75 L0,0 Z M0,4.375 L14,4.375 L14,6.125 L0,6.125 L0,4.375 Z M0,8.75 L14,8.75 L14,10.5 L0,10.5 L0,8.75 Z' id='path-1'%3E%3C/path%3E%3C/defs%3E%3Cg id='💎-UI-Elements' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='12)-Navbar' transform='translate(-1174.000000, -1290.000000)'%3E%3Cg id='Group' transform='translate(1174.000000, 1288.000000)'%3E%3Cg id='Icon-Color' transform='translate(0.000000, 2.000000)'%3E%3Cuse fill='#{$navbar-light-color}' xlink:href='%23path-1'%3E%3C/use%3E%3Cuse fill-opacity='0.1' fill='#{$navbar-light-color}' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !default;

$navbar-light-toggler-border-color: rgba($black, 0.06) !default;
$navbar-dropdown-hover-bg: rgba($black, 0.04) !default; // (C)
$navbar-dropdown-icon-bg: rgba($black, 0.08) !default; // (C)

// * Dropdowns
// *******************************************************************************

$dropdown-spacer: 0.25rem !default;
$dropdown-border-width: 0px !default;
$dropdown-border-color: $border-color !default;
$dropdown-box-shadow: $box-shadow !default;

$dropdown-inner-border-radius: 0px !default;

$dropdown-link-color: $headings-color !default;
$dropdown-link-hover-color: $component-hover-color !default;
$dropdown-link-hover-bg: $component-hover-bg !default;
$dropdown-link-line-height: $line-height-lg !default; // (C)

$dropdown-item-padding-y: 0.42rem !default;
$dropdown-item-padding-x: 1rem !default;

$dropdown-link-disabled-color: $text-lighter !default;
$dropdown-header-color: $text-muted !default;

// * Pagination
// *******************************************************************************

$pagination-padding-y: 0.594rem !default;
$pagination-padding-x: 0.35rem !default;
$pagination-padding-y-sm: 0.5rem !default;
$pagination-padding-x-sm: 0.15rem !default;
$pagination-padding-y-lg: 0.875rem !default;
$pagination-padding-x-lg: 0.4rem !default;

$pagination-border-width: 0px !default;

$pagination-line-height: 1 !default; // (c)

$pagination-color: $headings-color !default;
$pagination-bg: rgba($black, 0.08) !default;
$pagination-margin-start: 0.25rem !default;
$pagination-border-color: $pagination-bg !default;

$pagination-focus-color: $pagination-color !default;
$pagination-focus-bg: rgba($black, 0.16) !default;
$pagination-focus-box-shadow: none !default;

$pagination-hover-color: $pagination-focus-color !default;
$pagination-hover-bg: $pagination-focus-bg !default;
$pagination-hover-border-color: $pagination-hover-bg !default;

$pagination-disabled-color: $text-muted !default;
$pagination-disabled-bg: $pagination-bg !default;
$pagination-disabled-border-color: $pagination-disabled-bg !default;

// * Cards
// *******************************************************************************

$card-spacer-y: $spacer * 1.5 !default;
$card-spacer-x: $spacer * 1.5 !default;
$card-title-spacer-y: $spacer * 0.875 !default;
$card-spacer-x-sm: 1rem !default; // (C)
$card-border-width: 0 !default;
$card-border-color: $border-color !default;
$card-inner-border-color: $border-inner-color !default; // (C)
$card-cap-padding-y: $spacer * 1.5 !default;
$card-cap-bg: transparent !default;
$card-bg: $white !default;
$card-img-overlay-padding: 1.5rem !default;
$card-box-shadow: 0 0.25rem 1.125rem rgba($black, 0.1) !default;

// * Accordion
// *******************************************************************************

$accordion-padding-y: 0.82rem !default;
$accordion-padding-x: 1.125rem !default;
$accordion-bg: $card-bg !default;
$accordion-button-color: $headings-color !default;

$accordion-border-color: $card-bg !default;
$accordion-button-active-bg: $accordion-bg !default;
$accordion-button-active-color: $accordion-button-color !default;

$accordion-icon-width: 1.125rem !default;
$accordion-icon-color: $accordion-button-color !default;
$accordion-icon-active-color: $accordion-button-active-color !default;

$accordion-icon-transform: rotate(90deg) !default;

$accordion-button-icon: url("data:image/svg+xml,%3Csvg width='20' height='21' viewBox='0 0 20 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.5 5.5L12.5 10.5L7.5 15.5' stroke='#{$accordion-icon-color}' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7.5 5.5L12.5 10.5L7.5 15.5' stroke='white' stroke-opacity='0.1' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A") !default;

$accordion-button-active-icon: url("data:image/svg+xml,%3Csvg width='20' height='21' viewBox='0 0 20 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.5 5.5L12.5 10.5L7.5 15.5' stroke='#{$accordion-icon-color}' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7.5 5.5L12.5 10.5L7.5 15.5' stroke='white' stroke-opacity='0.1' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A") !default;

// * Tooltips
// *******************************************************************************

$tooltip-font-size: $font-size-base !default;
$tooltip-bg: $dark !default;
$tooltip-opacity: 1 !default;
$tooltip-padding-y: $spacer * 0.3125 !default;
$tooltip-padding-x: $spacer * 0.75 !default;

$tooltip-box-shadow: none !default; // (C)

// * Popovers
// *******************************************************************************
$popover-font-size: $font-size-base !default;
$popover-bg: $card-bg !default;
$popover-border-width: 0px !default;
$popover-box-shadow: $box-shadow !default;

$popover-header-bg: $card-bg !default;
$popover-header-color: $headings-color !default;
$popover-header-padding-y: $spacer !default;
$popover-header-padding-x: 1.125rem !default;

$popover-body-color: $body-color !default;
$popover-body-padding-y: 1.125rem !default;
$popover-body-padding-x: 1.125rem !default;

// * Toasts
// *******************************************************************************
$toast-padding-y: 0.406rem !default;
$toast-font-size: $font-size-base !default;
$toast-color: $body-color !default;
$toast-background-color: rgba($card-bg, 0.85);
$toast-border-width: 0px !default;
$toast-header-border-color: rgba($border-color, 0.3) !default;
$toast-spacing: 1rem !default;

$toast-header-color: $headings-color !default;
$toast-header-background-color: $card-bg !default;

// * Badges
// *******************************************************************************

$badge-font-size: 0.81em !default;
$badge-font-weight: $font-weight-semibold !default;
$badge-padding-y: 0.49em !default;
$badge-padding-x: 1em !default;
$badge-border-radius: 0.25rem !default;

$badge-pill-padding-x: 0.583em !default;
$badge-pill-border-radius: 10rem !default;

$badge-height: 1.5rem !default; // (C)
$badge-width: 1.5rem !default; // (C)
$badge-center-font-size: 0.812rem !default; // (C)

// * Modals
// *******************************************************************************

$modal-inner-padding: 1.5rem !default;

$modal-footer-margin-between: 1rem !default;

$modal-dialog-margin: $modal-inner-padding !default;

$modal-content-border-width: 0px !default;
$modal-content-border-radius: $border-radius !default;
$modal-content-box-shadow-xs: $floating-component-shadow !default;
$modal-content-box-shadow-sm-up: $floating-component-shadow !default;
$modal-backdrop-opacity: 0.5 !default;
$modal-header-border-width: 0px !default;
$modal-header-padding-y: 1.25rem !default;
$modal-header-padding-x: 0.25rem !default;
$modal-header-padding: $modal-header-padding-y $modal-inner-padding $modal-header-padding-x !default;
$modal-footer-padding: $modal-header-padding-x $modal-inner-padding $modal-header-padding-y !default; // (C)

$modal-lg: 50rem !default;
$modal-md: 35rem !default;
$modal-sm: 22.5rem !default;

$modal-fade-transform: translateY(-100px) scale(0.8) !default;
$modal-show-transform: translateY(0) scale(1) !default;

$modal-transition-duration: 0.15s !default; // (C)
$modal-transition: transform $modal-transition-duration ease-out !default;

$modal-simple-padding: 3rem !default; // (C)
$modal-simple-close-position: 1rem !default; // (C)

// * Alerts
// *******************************************************************************

$alert-padding-y: $spacer * 0.687 !default;
$alert-padding-x: $spacer * 0.875 !default;
$alert-border-radius: $border-radius !default;
$alert-bg-scale: -84% !default;
$alert-border-scale: -84% !default;
$alert-color-scale: 0% !default;
$alert-icon-bg: $white !default; // (C)

// * Progress bars
// *******************************************************************************

$progress-height: 0.75rem !default;
$progress-font-size: 0.75rem !default;
$progress-bg: rgba-to-hex(rgba($gray-500, 0.08), $rgba-to-hex-bg) !default;
$progress-border-radius: 3.125rem !default;
$progress-bar-color: $white !default;

// List group
// *******************************************************************************

// scss-docs-start list-group-variables
$list-group-color: $body-color !default;
$list-group-bg: transparent !default;
$list-group-border-color: $border-color !default;
$list-group-item-padding-y: 0.57rem !default;
$list-group-item-padding-x: 1.25rem !default;
$list-group-item-bg-scale: -84% !default;
$list-group-item-border-scale: -84% !default; // (C)
$list-group-item-color-scale: 0% !default;
$list-group-item-bg-hover-scale: 0% !default; //  (c)

$list-group-hover-bg: rgba-to-hex($component-hover-bg, $rgba-to-hex-bg) !default;

$list-group-disabled-color: $text-muted !default;
$list-group-disabled-bg: $list-group-bg !default;

$list-group-action-hover-color: $component-hover-color !default;

$list-group-action-active-color: $component-active-bg !default;
$list-group-action-active-bg: $list-group-hover-bg !default;
// scss-docs-end list-group-variables

// * Image thumbnails
// *******************************************************************************

$thumbnail-padding: 0 !default;
$thumbnail-bg: transparent !default;
$thumbnail-border-width: 0px !default;
$thumbnail-border-radius: 0px !default;

// * Figures
// *******************************************************************************

$figure-caption-color: $text-muted !default;

// * Breadcrumbs
// *******************************************************************************

$breadcrumb-padding-y: 0 !default;
$breadcrumb-padding-x: 0 !default;
$breadcrumb-item-padding-x: 0.875rem !default;
$breadcrumb-margin-bottom: 1rem !default;
$breadcrumb-bg: transparent !default;
$breadcrumb-divider-color: $black !default;
$breadcrumb-active-color: $breadcrumb-divider-color !default;
$breadcrumb-divider: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-tabler icon-tabler-chevron-right' width='16' height='24' viewBox='0 0 24 24' stroke-width='1.75' stroke='#{$breadcrumb-divider-color}' fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='none' d='M0 0h24v24H0z' fill='none'%3E%3C/path%3E%3Cpolyline points='9 6 15 12 9 18'%3E%3C/polyline%3E%3C/svg%3E") !default;
$breadcrumb-divider-flipped: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-tabler icon-tabler-chevron-left' width='16' height='24' viewBox='0 0 24 24' stroke-width='1.75' stroke='#{$breadcrumb-divider-color}' fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='none' d='M0 0h24v24H0z' fill='none'%3E%3C/path%3E%3Cpolyline points='15 6 9 12 15 18'%3E%3C/polyline%3E%3C/svg%3E") !default;
$breadcrumb-color: $component-active-bg !default; // (C)

$breadcrumb-divider-check: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-tabler icon-tabler-check' width='16' height='24' viewBox='0 0 24 24' stroke-width='1.75' stroke='#{$breadcrumb-divider-color}' fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='none' d='M0 0h24v24H0z' fill='none'%3E%3C/path%3E%3Cpath d='M5 12l5 5l10 -10'%3E%3C/path%3E%3C/svg%3E");

$breadcrumb-icon-check-svg: str-replace(
  str-replace($breadcrumb-divider-check, '#{$breadcrumb-divider-color}', $breadcrumb-divider-color),
  '#',
  '%23'
); // (C)

// * Carousel
// *******************************************************************************

$carousel-control-color: $white !default;

$carousel-indicator-width: 34px !default;
$carousel-indicator-height: 5px !default;
$carousel-indicator-hit-area-height: 0 !default;
$carousel-indicator-spacer: 5px !default;
$carousel-indicator-opacity: 0.4 !default;

$carousel-caption-spacer: 1.437rem !default;

$carousel-control-icon-width: 2.5rem !default;
$carousel-control-prev-icon-bg: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.25 4.5L6.75 9L11.25 13.5' stroke='#{$carousel-control-color}' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M11.25 4.5L6.75 9L11.25 13.5' stroke='white' stroke-opacity='0.2' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A") !default;

$carousel-control-next-icon-bg: url("data:image/svg+xml,%3Csvg width='19' height='18' viewBox='0 0 19 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.25 4.5L11.75 9L7.25 13.5' stroke='#{$carousel-control-color}' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7.25 4.5L11.75 9L7.25 13.5' stroke='white' stroke-opacity='0.2' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !default;

// Spinners
// *******************************************************************************

$spinner-width-lg: 3rem !default; // (C)
$spinner-height-lg: $spinner-width-lg !default; // (C)
$spinner-border-width-lg: 0.3em !default; // (C)

// * Close
// *******************************************************************************

$btn-close-width: 1.125rem !default;
$btn-close-color: $black !default;
$btn-close-bg: url("data:image/svg+xml,%3Csvg width='19' height='18' viewBox='0 0 19 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 4.5L5 13.5' stroke='#{$btn-close-color}' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M14 4.5L5 13.5' stroke='white' stroke-opacity='0.2' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M5 4.5L14 13.5' stroke='#{$btn-close-color}' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M5 4.5L14 13.5' stroke='white' stroke-opacity='0.2' stroke-width='1.75' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A") !default;
$btn-close-focus-shadow: none !default;
$btn-close-focus-opacity: 0.75 !default;

$close-font-weight: 300 !default; // (C)

// * Offcanvas
// *******************************************************************************

// scss-docs-start offcanvas-variables
$offcanvas-transition-duration: 0.25s !default;
// scss-docs-end offcanvas-variables

// Utilities
$overflows: auto, hidden, scroll, visible !default;

// Config
$rtl-support: false !default;
$dark-style: false !default;

// Useful Icons
$upload-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-tabler icon-tabler-upload' width='24' height='24' viewBox='0 0 24 24' stroke-width='2' stroke='currentColor' fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='none' d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2' /%3E%3Cpolyline points='7 9 12 4 17 9' /%3E%3Cline x1='12' y1='4' x2='12' y2='16' /%3E%3C/svg%3E");
