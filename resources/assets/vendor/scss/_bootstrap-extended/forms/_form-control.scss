// Form control
// *******************************************************************************

// form input placeholder animation
.form-control {
  &::placeholder,
  &:focus::placeholder {
    transition: all 0.2s ease;
  }
}
// ltr only
@include ltr-only {
  .form-control {
    &:focus::placeholder {
      transform: translateX(4px);
    }
  }
}
// rtl only
@include rtl-only {
  .form-control {
    &:focus::placeholder {
      transform: translateX(-4px);
    }
  }
}
