// Select
// *******************************************************************************

.form-select {
  background-clip: padding-box;
}

// form-select svg size
.form-select-sm {
  background-size: 19px 17px;
}
.form-select-lg {
  background-size: 24px 22px;
}

// Multiple select RTL Only
@include rtl-only {
  .form-select {
    background-position: left $form-select-padding-x center;
    padding-right: $form-select-padding-x;
    padding-left: $form-select-indicator-padding;

    &[multiple],
    &[size]:not([size='1']) {
      padding-left: $form-select-padding-x;
      background-image: none;
    }
  }
}
@if $dark-style {
  select.form-select option {
    background-color: $input-bg;
  }
}
