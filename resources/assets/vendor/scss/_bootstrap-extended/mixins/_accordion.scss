// Accordion
// *******************************************************************************

// Header text color accordion
@mixin template-accordion-header-variant($parent, $background) {
  .accordion {
    &#{$parent} {
      .accordion-item {
        &.active {
          .accordion-button {
            color: $background !important;
          }
        }
      }
    }
  }
}

// Bordered accordion
@mixin template-accordion-border-background-variant($parent, $background) {
  .accordion {
    &#{$parent} {
      .accordion-item {
        background-color: rgba($background, 0.1);
        border-color: $background !important;
        .accordion-button {
          background: transparent;
        }
        .accordion-button:not(.collapsed) {
          box-shadow: inset 0 ($accordion-border-width * -1) 0 $background;
        }
        &.active {
          .accordion-button {
            border-color: $background !important;
          }
        }
      }
    }
  }
}

// Solid accordion with active border
@mixin template-accordion-border-solid-variant($parent, $background) {
  .accordion {
    &#{$parent} {
      .accordion-item {
        border-color: $background;
        background-color: $background;

        .accordion-header {
          .accordion-button {
            background-color: $background;
            border-color: $background;
            color: $white;
          }
        }
        .accordion-button:not(.collapsed) {
          box-shadow: inset 0 ($accordion-border-width * -1) 0 $background;
        }
        &.active {
          background-color: rgba($background, 0.1);
          border: 1px solid $background !important;

          .accordion-header {
            .accordion-button {
              background-color: transparent;
              border-color: $background !important;
              color: $background;
            }
          }
        }
      }
    }
  }
}

// Solid accordion
@mixin template-accordion-solid-variant($parent, $background) {
  .accordion {
    &#{$parent} {
      .accordion-item {
        background-color: $background;
        border-color: $background !important;
        color: $white;

        .accordion-button {
          background-color: $background;
          color: $white;
          // border-color: shade-color($background, 10%) !important;
        }
        .accordion-button:not(.collapsed) {
          box-shadow: inset 0 ($accordion-border-width * -1) 0 shade-color($background, 20%);
        }
      }
    }
  }
}

// Outline accordion
@mixin template-accordion-outline-variant($parent, $background) {
  .accordion {
    &#{$parent} {
      .accordion-item {
        border-color: $background !important;
        .accordion-button:not(.collapsed) {
          box-shadow: inset 0 ($accordion-border-width * -1) 0 $background;
        }
      }
    }
  }
}
