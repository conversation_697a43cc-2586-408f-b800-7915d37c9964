// Theme mixin
// *******************************************************************************

@mixin template-common-theme($background, $color: null) {
  @include text-variant('.text-primary', $background);
  @include bg-variant('.bg-primary', $background);
  @include bg-label-variant('.bg-label-primary', $background);
  @include bg-glow-variant('.bg-primary', $background);
  @include template-pagination-theme($background, $color);
  @include template-progress-bar-theme($background, $color);
  @include template-modal-onboarding-theme($background, $color);
  @include template-list-group-theme($background, $color);
  @include template-list-group-timeline-variant('.list-group-timeline-primary', $background);
  @include template-alert-variant('.alert-primary', $background);
  @include template-tooltip-variant(
    '.tooltip-primary, .tooltip-primary > .tooltip, .ngb-tooltip-primary + ngb-tooltip-window',
    $background,
    $color
  );
  @include template-popover-variant(
    '.popover-primary, .popover-primary > .popover, .ngb-popover-primary + ngb-popover-window',
    $background,
    $color
  );

  // Need to add shift-color as BS5 updated with table variant colors like this

  @include template-table-variant('primary', shift-color($background, $table-bg-scale));
  @include template-button-variant('.btn-primary', $background, $color);
  @include template-button-label-variant('.btn-label-primary', $background, $color);
  @include template-button-outline-variant('.btn-outline-primary', $background, $color);
  @include template-dropdown-theme($background, $color);
  @include template-nav-theme($background, $color);
  @include template-form-control-theme($background);
  @include template-form-check-theme($background, $color);
  @include template-form-switch-theme($background);
  @include template-file-input-theme($background);

  @include template-switch-variant('', $background, $color); // For default switch
  @include template-switch-variant('.switch-primary', $background, $color);

  @include template-switch-outline-variant('.switch-outline', $background, $color); // For default outline switch
  @include template-switch-outline-variant('.switch-outline-primary', $background, $color);

  @include template-timeline-variant('.timeline-item-primary', $background);
  @include template-timeline-point-variant('.timeline-point-primary', $background);
  @include template-timeline-indicator-variant('.timeline-indicator-primary', $background);
  @include template-text-divider-variant('.divider-primary', $background);

  @include template-navbar-style('.navbar.bg-primary', $primary-color);
  @include template-menu-style('.menu.bg-primary', $primary-color);
  @include template-footer-style('.footer.bg-primary', $primary-color);
  @include template-float-label-theme($primary-color);
  @include template-svg-color($primary-color);
  @include template-treeview-clicked-bg($primary-color);

  html:not([dir='rtl']) .border-primary,
  html[dir='rtl'] .border-primary {
    border-color: $background !important;
  }
  a {
    color: $background;
    &:hover {
      color: tint-color($background, 10%);
    }
  }
  .fill-primary {
    fill: $background;
  }
}
