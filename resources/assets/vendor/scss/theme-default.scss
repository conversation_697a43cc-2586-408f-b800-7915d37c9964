@import './_components/include';
@import './_theme/common';
@import './_theme/libs';
@import './_theme/pages';
@import './_theme/_theme';

$primary-color: #ffd403;
$body-bg: #333669;

body {
  background: $body-bg;
}

.bg-body {
  background: $body-bg !important;
}

@include template-common-theme($primary-color);
@include template-libs-theme($primary-color);
@include template-pages-theme($primary-color);

// Navbar
// ---------------------------------------------------------------------------
@include template-navbar-style('.bg-navbar-theme', $card-bg, $color: $body-color, $active-color: $headings-color);

.layout-navbar {
  box-shadow: 0 0 10px $border-color;
  backdrop-filter: saturate(200%) blur(6px);
}
.menu-horizontal {
  backdrop-filter: saturate(200%) blur(6px);
}
.layout-horizontal .layout-navbar {
  box-shadow: 0 1px 0 $border-color;
}
.navbar-detached {
  box-shadow: $box-shadow-sm;
}
.layout-navbar-fixed .layout-page:before {
  backdrop-filter: saturate(200%) blur(10px);
  background: linear-gradient(180deg, rgba($body-bg, 70%) 44%, rgba($body-bg, 43%) 73%, rgba($body-bg, 0%));
  -webkit-mask: linear-gradient($body-bg, $body-bg 18%, transparent 100%);
  mask: linear-gradient($body-bg, $body-bg 18%, transparent 100%);
}

// Menu
// ---------------------------------------------------------------------------
@include template-menu-style(
  '.bg-menu-theme',
  $card-bg,
  $color: $body-color,
  $active-color: $headings-color,
  $border: transparent,
  $active-bg: $primary-color
);
@include media-breakpoint-up(xl) {
  .bg-menu-theme {
    box-shadow: $box-shadow-sm;
  }
}
.bg-menu-theme {
  .menu-inner {
    .menu-item {
      &.open,
      &.active {
        > .menu-link.menu-toggle {
          &,
          .layout-menu-hover.layout-menu-collapsed & {
            background: rgba-to-hex(rgba($black, 0.04), $rgba-to-hex-bg);
          }
        }
      }
      &:not(.active) .menu-link:hover {
        html:not(.layout-menu-collapsed) &,
        .layout-menu-hover.layout-menu-collapsed & {
          background: rgba-to-hex(rgba($black, 0.04), $rgba-to-hex-bg);
        }
      }
    }
  }
  .menu-inner .menu-sub .menu-item:not(.active) > .menu-link::before {
    color: $text-muted !important;
  }
}

// Footer
// ---------------------------------------------------------------------------
@include template-footer-style('.bg-footer-theme', $card-bg, $color: $body-color, $active-color: $headings-color);

.layout-footer-fixed .layout-wrapper:not(.layout-horizontal) .content-footer .footer-container,
.layout-footer-fixed .layout-wrapper.layout-horizontal .content-footer {
  box-shadow: $box-shadow;
}
