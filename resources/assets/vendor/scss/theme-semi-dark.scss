@import './_components/include';
@import './_theme/common';
@import './_theme/libs';
@import './_theme/pages';
@import './_theme/_theme';

$primary-color: #333669;
$body-bg: #35363a;

body {
  background: $body-bg;
}

.bg-body {
  background: $body-bg !important;
}

@include template-common-theme($primary-color);
@include template-libs-theme($primary-color);
@include template-pages-theme($primary-color);

// Navbar
// ---------------------------------------------------------------------------
@include template-navbar-style('.bg-navbar-theme', $card-bg, $color: $body-color, $active-color: $headings-color);

.layout-navbar {
  box-shadow: 0 1px 0 $border-color;
  backdrop-filter: saturate(200%) blur(6px);
}
.menu-horizontal {
  backdrop-filter: saturate(200%) blur(6px);
}
.navbar-detached {
  box-shadow: 0 0 0.375rem 0.25rem rgba(rgba-to-hex($gray-500, $rgba-to-hex-bg), 0.15);
}
.layout-navbar-fixed .layout-page:before {
  backdrop-filter: saturate(200%) blur(10px);
  background: linear-gradient(180deg, rgba($body-bg, 70%) 44%, rgba($body-bg, 43%) 73%, rgba($body-bg, 0%));
  -webkit-mask: linear-gradient($body-bg, $body-bg 18%, transparent 100%);
  mask: linear-gradient($body-bg, $body-bg 18%, transparent 100%);
}

// Menu
// ---------------------------------------------------------------------------
@include template-menu-style(
  '.bg-menu-theme',
  #242745,
  $color: rgba-to-hex($gray-300, $rgba-to-hex-bg),
  $active-color: rgba-to-hex($gray-200, $rgba-to-hex-bg),
  $active-bg: $primary-color
);
.bg-menu-theme {
  .menu-inner {
    .menu-item {
      &.open,
      &.active {
        > .menu-link.menu-toggle {
          &,
          .layout-menu-hover.layout-menu-collapsed & {
            background: #2f324e;
          }
        }
      }
      &:not(.active) .menu-link:hover {
        html:not(.layout-menu-collapsed) &,
        .layout-menu-hover.layout-menu-collapsed & {
          background: #2f324e;
        }
      }
    }
  }
  .menu-inner .menu-sub .menu-item:not(.active) > .menu-link::before {
    color: rgba-to-hex($gray-500, $rgba-to-hex-bg) !important;
  }
}

// Footer
// ---------------------------------------------------------------------------
@include template-footer-style('.bg-footer-theme', $card-bg, $color: $body-color, $active-color: $headings-color);

.layout-footer-fixed .layout-wrapper:not(.layout-horizontal) .content-footer .footer-container,
.layout-footer-fixed .layout-wrapper.layout-horizontal .content-footer {
  box-shadow: $box-shadow;
}
