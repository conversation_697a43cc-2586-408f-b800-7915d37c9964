<!-- Add Role Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-add-new-role">
        <div class="modal-content p-3 p-md-5">
            <button type="button" class="btn-close btn-pinned" data-bs-dismiss="modal" aria-label="Close"></button>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h3 class="role-title mb-2">Add New Role</h3>
                    <p class="text-muted">Set role permissions</p>
                </div>
                <!-- Edit role form -->
                <form id="editRoleForm" class="row g-3">
                    <div class="col-12 mb-4">
                        <label class="form-label" for="name">Role Name</label>
                        <input type="text" id="name" name="name" class="form-control" placeholder="Enter a role name" tabindex="-1" />
                    </div>
                    <div class="col-12">
                        <h5>Role Permissions</h5>
                        <!-- Permission table -->
                        @php

                        $actions = [['value' => 'read', 'text' => __('read')], ['value' => 'create', 'text' =>
                        __('create')], ['value' => 'update', 'text' => __('update')], ['value' => 'delete',
                        'text' => __('delete')]]; // permissions

                        @endphp
                        <div class="table-responsive">
                            <table class="table table-flush-spacing">
                                <tbody>
                                    <tr>
                                        <td class="text-nowrap fw-semibold">Administrator Access <i class="ti ti-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Allows a full access to the system"></i></td>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll" />
                                                <label class="form-check-label" for="selectAll">
                                                    Select All
                                                </label>
                                            </div>
                                        </td>
                                    </tr>
                                    @foreach ($levels as $index => $level)
                                    <tr>
                                        <td class="text-nowrap fw-semibold">{{ ucfirst($level->name) }}</td>
                                        <td>
                                            <div class="d-flex">
                                                @foreach ($actions as $key => $action)
                                                @php
                                                $firstChar = substr($action['value'], 0, 1);
                                                $checked = strpos($level->actions, $firstChar);
                                                @endphp
                                                <div class="form-check me-3 me-lg-5">
                                                    <input class="form-check-input" type="checkbox" name="permissions[]" id="{{ $action['value'] . ' ' . $level->name }}" value="{{ $action['value'] . ' ' . $level->name }}" />
                                                    <label class="form-check-label" for="{{ $action['value'] . ' ' . $level->name }}">
                                                        {{ $action['text'] }}
                                                    </label>
                                                </div>
                                                @endforeach

                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <!-- Permission table -->
                    </div>
                    <div class="col-12 text-center mt-4">
                        <button type="submit" id="submitRole" class="btn btn-primary me-sm-3 me-1">Submit</button>
                        <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                    </div>
                </form>
                <!--/ Add role form -->
            </div>
        </div>
    </div>
</div>
<!--/ Add Role Modal -->
