<!-- Share Project Modal -->
<div class="modal fade" id="shareProject" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-simple modal-enable-otp modal-dialog-centered">
    <div class="modal-content p-3 p-md-5">
      <div class="modal-body">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="text-center">
          <h3 class="mb-2">Share Project</h3>
          <p>Share project with a team member</p>
        </div>
      </div>
      <div class="mb-4 pb-2">
        <label for="select2Basic" class="form-label">Add Members</label>
        <select id="select2Basic" class="form-select form-select-lg share-project-select" data-allow-clear="true">
          <option data-name="<PERSON> Nichols" data-image="img/avatars/13.png" selected><PERSON></option>
          <option data-name="<PERSON>" data-image="img/avatars/9.png"><PERSON></option>
          <option data-name="<PERSON>" data-image="img/avatars/10.png">Sophie Gilbert</option>
          <option data-name="Marvin <PERSON>" data-image="img/avatars/7.png">Marvin Wheeler</option>
        </select>
      </div>
      <h4 class="mb-4 pb-2">8 Members</h4>
      <ul class="p-0 m-0">
        <li class="d-flex flex-wrap mb-3">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/1.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Lester Palmer</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Can Edit</span></button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="d-flex flex-wrap mb-3">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/2.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Mattie Blair</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Owner</span></button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="d-flex flex-wrap mb-3">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/3.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Marvin Wheeler</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Can Edit</span></button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="d-flex flex-wrap mb-3">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/4.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Nannie Ford</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Can Comment</span> </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="d-flex flex-wrap mb-3">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/5.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Julian Murphy</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Can View</span></button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="d-flex flex-wrap mb-3">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/6.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Sophie Gilbert</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Can View</span> </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="d-flex flex-wrap mb-3">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/7.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Chris Watkins</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Can Comment</span> </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="d-flex flex-wrap">
          <div class="avatar me-3">
            <img src="{{asset('assets/img/avatars/8.png')}}" alt="avatar" class="rounded-circle">
          </div>
          <div class="d-flex justify-content-between flex-grow-1">
            <div class="me-2">
              <p class="mb-0">Adelaide Nichols</p>
              <p class="mb-0 text-muted"><EMAIL></p>
            </div>
            <div class="dropdown">
              <button type="button" class="btn dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false"><span class="text-muted fw-normal me-2 d-none d-sm-inline-block">Can Edit</span> </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Owner</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Edit</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can Comment</a>
                </li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0);">Can View</a>
                </li>
              </ul>
            </div>
          </div>
        </li>
      </ul>
      <div class="d-flex align-items-start mt-4 align-items-sm-center">
        <i class="ti ti-users me-2"></i>
        <div class="d-flex justify-content-between flex-grow-1 align-items-center flex-wrap gap-2">
          <h6 class="mb-0">Public to {{ config('variables.templateName') }} - {{ config('variables.creatorName') }}</h6>
          <button class="btn btn-primary">Copy Project Link</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!--/ Share Project Modal -->
