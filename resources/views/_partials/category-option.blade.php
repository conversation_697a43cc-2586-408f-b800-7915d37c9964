<!-- resources/views/partials/category-option.blade.php -->

@php
    // Set a max level for recursion
    $maxLevel = 6;
@endphp

@isset($category)
    <!-- Render the current category option -->
    <option value="{{ $category->id }}" 
        {{ (old('product_category_id', $selectedCategoryId) == $category->id) ? 'selected' : '' }}>
        {{ str_repeat('--', $level - 1) }} {{ $category->{'title_' . __('lang')} }}
    </option>

    <!-- Check if the current category has subcategories and ensure it doesn't exceed max level -->
    @if ($category->subs->count() > 0 && $level < $maxLevel)
        @foreach ($category->subs as $sub)
            <!-- Recursively include the category-option partial for subcategories -->
            @include('_partials.category-option', ['category' => $sub, 'level' => $level + 1, 'selectedCategoryId' => old('product_category_id', $selectedCategoryId)])
        @endforeach
    @endif
@endisset
