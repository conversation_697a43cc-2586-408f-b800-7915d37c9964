<script>
    // handle noty delete button
    $('.delete').click(function (e) {
        //"use strict";
        e.preventDefault();
        var that = $(this);

        Swal.fire({
            title: "{{ __('confirm_delete') }}",
            text: "{{ __('no_revert') }}",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: "{{ __('delete_it') }}",
            cancelButtonText: "{{ __('cancel') }}",
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            padding: '2em',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-danger',
            }
        }).then(function (result) {
            if (result.value) {
                that.closest('form').submit();
            }
        })

    });
    
    // handle noty status button
    $('.status').click(function (e) {
        //"use strict";
        e.preventDefault();
        var that = $(this);

        Swal.fire({
            title: "{{ __('confirm_status') }}",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: "{{ __('change_it') }}",
            cancelButtonText: "{{ __('cancel') }}",
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            padding: '2em',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-danger',
            }
        }).then(function (result) {
            if (result.value) {
                that.closest('form').submit();
            }
        })

    });

</script>
