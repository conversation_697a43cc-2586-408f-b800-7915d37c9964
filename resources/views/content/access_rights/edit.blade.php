@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('edit') . ' ' . __('role'))


@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/modal-add-role.js') }}"></script>
@endsection


@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('edit') . ' ' . __('role') }}</h4>
                    <a href="{{ route('roles.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')

                    <form action="{{ route('roles.update', $role->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="col-12 mb-4">
                            <label class="form-label" for="name">{{ __('role_name') }}</label>
                            <input type="text" id="name" name="name" value="{{ $role->name }}"
                                class="form-control" placeholder="{{ __('role_name') }}" />
                        </div>
                        <div class="col-12">
                            <h5>{{ __('Permissions') }}</h5>
                            @php
                                $actions = [['value' => 'read', 'text' => __('read')], ['value' => 'create', 'text' => __('create')], ['value' => 'update', 'text' => __('update')], ['value' => 'delete', 'text' => __('delete')]]; // permissions
                            @endphp
                            <div class="table-responsive">
                                <table class="table table-flush-spacing">
                                    <tbody>
                                        <tr>
                                            <td class="text-nowrap fw-semibold">{{ __('Administrator Access') }} <i
                                                    class="ti ti-info-circle" data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="{{ __('Allows a full access to the system') }}"></i></td>
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="selectAll" />
                                                    <label class="form-check-label" for="selectAll">
                                                        {{ __('Select All') }}
                                                    </label>
                                                </div>
                                            </td>
                                        </tr>
                                        @foreach ($levels as $index => $level)
                                            <tr>
                                                <td class="text-nowrap fw-semibold">{{ ucfirst(__("$level->name")) }}</td>
                                                <td>
                                                    <div class="d-flex">
                                                        @foreach ($actions as $key => $action)
                                                            @php
                                                                $firstChar = substr($action['value'], 0, 1);
                                                                $checked = strpos($level->actions, $firstChar);
                                                            @endphp
                                                            @if ($checked === false)
                                                            @else
                                                                <div class="form-check me-3 me-lg-5">
                                                                    <input class="form-check-input" type="checkbox"
                                                                        name="permissions[]"
                                                                        id="{{ $action['value'] . ' ' . $level->name }}"
                                                                        value="{{ $action['value'] . ' ' . $level->name }}"
                                                                        {{ $role->hasPermissionTo($action['value'] . ' ' . $level->name) ? 'checked' : '' }} />
                                                                    <label class="form-check-label"
                                                                        for="{{ $action['value'] . ' ' . $level->name }}">
                                                                        {{ $action['text'] }}
                                                                    </label>
                                                                </div>
                                                            @endif
                                                        @endforeach
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
