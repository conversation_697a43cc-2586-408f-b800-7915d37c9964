@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('roles'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css">
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@push('scripts')
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.buttons.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/datatables/buttons.server-side.js"></script>
    <script src="{{ asset('dashboard_files') }}/dist/js/datatables.js"></script>

    {!! $dataTable->scripts() !!}
@endpush

@section('content')
    <h4 class="fw-semibold mb-4">{{ __('roles') }}</h4>
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        @can('create roles')
                            <a href="{{ route('roles.create') }}" class="btn btn-primary mb-2 text-nowrap add-new-role"><i
                                    class="fa fa-plus"></i>{{ __('add') }}</a>
                        @endcan
                    </div>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(['class' => 'dataTable datatables-users table table-hover', 'responsive' => 'true'], true) !!}
                </div>
            </div>
        </div>
    </div>

@endsection
