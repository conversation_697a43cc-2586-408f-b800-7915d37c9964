@extends('layouts/layoutMaster')

@section('title', __('edit') . ' ' . __($nameS))

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('edit') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.update', $item->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-6 mb-4">
                                <label class="form-label" for="country_id">{{ __('country') }}</label>
                                <select id="country_id" name="country_id" class="form-control">
                                    @foreach ($countries as $country)
                                        <option value="{{ $country->id }}" {{ $item->country_id == $country->id ? 'selected' : '' }}>{{ $country->title_en }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="title_en">{{ __('title_en') }}</label>
                                <input type="text" id="title_en" name="title_en" class="form-control" placeholder="{{ __('title_en') }}" value="{{ old('title_en', $item->title_en) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="title_ar">{{ __('title_ar') }}</label>
                                <input type="text" id="title_ar" name="title_ar" class="form-control" placeholder="{{ __('title_ar') }}" value="{{ old('title_ar', $item->title_ar) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="lat">{{ __('lat') }}</label>
                                <input type="text" id="lat" name="lat" class="form-control" placeholder="{{ __('lat') }}" value="{{ old('lat', $item->lat) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="lon">{{ __('lon') }}</label>
                                <input type="text" id="lon" name="lon" class="form-control" placeholder="{{ __('lon') }}" value="{{ old('lon', $item->lon) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="radius">{{ __('radius') }}</label>
                                <input type="text" id="radius" name="radius" class="form-control" placeholder="{{ __('radius') }}" value="{{ old('radius', $item->radius) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="active">{{ __('active') }}</label>
                                <select id="active" name="active" class="form-control">
                                    <option value="1" {{ $item->active == 1 ? 'selected' : '' }}>{{ __('active') }}</option>
                                    <option value="0" {{ $item->active == 0 ? 'selected' : '' }}>{{ __('inactive') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
