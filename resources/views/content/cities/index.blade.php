@extends('layouts/layoutMaster')

@section('title', __($name))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins/dtable/css/jquery.dataTables.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins/dtable/css/buttons.dataTables.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins/noty/noty.min.css') }}" />
@endpush

@section('content')
    <h4 class="fw-semibold mb-4">{{ __($name) }}</h4>
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        @can('create ' . $name)
                            <a href="{{ route($name . '.create') }}" class="btn btn-primary mb-2 text-nowrap add-new-{{ $nameS }}">
                                <i class="fa fa-plus"></i> {{ __('add') }}
                            </a>
                        @endcan
                    </div>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(['class' => 'dataTable datatables-{{ $nameS }} table table-hover', 'responsive' => 'true'], true) !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="{{ asset('dashboard_files/plugins/dtable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('dashboard_files/plugins/dtable/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ asset('dashboard_files/plugins/dtable/js/dataTables.buttons.min.js') }}"></script>
    <script src="{{ asset('dashboard_files/plugins/datatables/buttons.server-side.js') }}"></script>
    <script src="{{ asset('dashboard_files/dist/js/datatables.js') }}"></script>
    {!! $dataTable->scripts() !!}
@endpush
