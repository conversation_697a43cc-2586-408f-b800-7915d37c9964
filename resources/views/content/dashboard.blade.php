@extends('layouts/layoutMaster')

@section('title', __('dashboard'))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/apex-charts/apex-charts.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/swiper/swiper.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-checkboxes-jquery/datatables.checkboxes.css') }}" />
@endsection

@section('page-style')
    <!-- Page -->
    <link rel="stylesheet" href="{{ asset('assets/vendor/css/pages/cards-advance.css') }}">
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/swiper/swiper.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/apex-charts/apexcharts.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/dashboards-analytics.js') }}"></script>
@endsection

@section('content')


<div class="row g-4">
        <i class="menu-icon tf-icons ti ti-xl ti-users">@lang('total_users') : {{ number_format($s['total_users'] ?? 0) }}</i>
        <div class="col-12">
            <div class="row">
                <div class="col-sm-4 mb-4 text-center">
                    <div class="card p-4 bg-primary text-white">
                        <div>{{ __('total_views') }}</div>
                        <div>{{ number_format(num: $s['total_views'] ?? 0) }}</div>
                    </div>
                </div>
                <div class="col-sm-4 mb-4 text-center">
                    <div class="card p-4 bg-success text-white">
                        <div>{{ __('total_search') }}</div>
                        <div>{{ number_format($s['total_search'] ?? 0) }}</div>
                    </div>
                </div>
                <div class="col-sm-4 mb-4 text-center">
                    <div class="card p-4 bg-danger text-white">
                        <div>{{ __('total_reservations') }}</div>
                        <div>{{ number_format($s['total_reservations'] ?? 0) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr />
    <div class="row g-4">
        <div class="col-12">
            <div class="row">
                <!-- Roles -->
                @can('read roles')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('roles.index') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-key"></i>
                                <div>{{ __('roles') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
                <!-- Users -->
                @can('read users')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('users.index') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-users"></i>
                                <div>{{ __('users') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
                <!-- Attendance -->
                @can('read attendances')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('attendances.index') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-clock"></i>
                                <div>{{ __('attendances') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
                <!-- Service Category Items -->
                @can('read service_category_items')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('service_category_items.index') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-leaf"></i>
                                <div>{{ __('service_category_items') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
                <!-- Transactions -->
                @can('read transactions')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('transactions.index') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-exchange"></i>
                                <div>{{ __('transactions') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
                <!-- Invoices -->
                @can('read invoices')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('invoices.index') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-receipt"></i>
                                <div>{{ __('invoices') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
                <!-- Settings -->
                @can('read settings')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('settings.edit') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-settings"></i>
                                <div>{{ __('settings') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
                <!-- Financial Categories -->
                @can('read financial_categories')
                    <div class="col-sm-4 mb-4 text-center">
                        <div class="card p-4">
                            <a href="{{ route('financial_categories.index') }}">
                                <i class="menu-icon tf-icons ti ti-xl ti-building-bank"></i>
                                <div>{{ __('financial_categories') }}</div>
                            </a>
                        </div>
                    </div>
                @endcan
            </div>
        </div>
    </div>

@endsection
