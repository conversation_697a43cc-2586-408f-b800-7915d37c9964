<div class="dropdown">
    @if (!in_array($id, [1, 2, 3, 4, 5, 6]))
        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i
                class="ti ti-dots-vertical"></i></button>
        <div class="dropdown-menu">

            @can('update financial_categories')
                <a href="{{ route('financial_categories.edit', $id) }}" class="dropdown-item"
                    title="{{ __('edit') }}">{{ __('edit') }}</a>
            @endcan

            @can('delete financial_categories')
                <form action="{{ route('financial_categories.destroy', $id) }}" method="post" class="mb-0">
                    @csrf
                    @method('DELETE')
                    <button class="dropdown-item delete" title="{{ __('delete') }}">
                        {{ __('delete') }}
                    </button>
                </form>
            @endcan

        </div>
    @endif
</div>
@include('content._datatableAction')
