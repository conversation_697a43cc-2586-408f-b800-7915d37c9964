<div class="dropdown">
    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i
            class="ti ti-dots-vertical"></i></button>
    <div class="dropdown-menu">
        <!-- Button trigger modal -->
        <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#Payments{{ $id }}">
            @lang('payments')
        </button>
        @can('create invoices')
            @if (!$status)
                <!-- Button trigger modal -->
                <button type="button" class="dropdown-item" data-bs-toggle="modal"
                    data-bs-target="#PayRemain{{ $id }}">
                    <i class="fa fa-money"></i> @lang('pay_remaining')
                </button>
            @endif
        @endcan
        @can('delete invoices')
            <form action="{{ route('invoices.destroy', $id) }}" method="post" class="mb-0">
                @csrf
                @method('DELETE')
                <button class="dropdown-item delete" title="{{ __('delete') }}">
                    {{ __('delete') }}
                </button>
            </form>
        @endcan

    </div>
</div>
<!-- Modal -->
<div class="modal fade" id="Payments{{ $id }}" data-bs-backdrop="static" data-bs-keyboard="false"
    aria-labelledby="Payments{{ $id }}Label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="Payments{{ $id }}Label">@lang('payments')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="@lang('close')"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <table class="table">
                        <tr>
                            <th>@lang('amount')</th>
                            <th>@lang('created_at')</th>
                        </tr>
                        @foreach ($payments as $p)
                            <tr>
                                <td>{{ $p['amount'] }}</td>
                                <td><span dir="ltr">{{ date('Y-m-d g:i A', strtotime($p['created_at'])) }}</span>
                                </td>
                            </tr>
                        @endforeach
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('close')</button>
            </div>
        </div>
    </div>
</div>
@can('create invoices')
    @if (!$status)
        <!-- Modal -->
        <div class="modal fade" id="PayRemain{{ $id }}" data-bs-backdrop="static" data-bs-keyboard="false"
            aria-labelledby="PayRemain{{ $id }}Label" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="PayRemain{{ $id }}Label">@lang('pay_remaining')</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="@lang('close')"></button>
                    </div>

                    <form action="{{ route('invoices.pay', $id) }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="row">
                                <div class="col mb-4">
                                    <label class="form-label required" for="paid"><i class="required ml-sm">*</i>
                                        {{ __('paid') }}</label>
                                    <input type="text" id="paid" name="paid" class="form-control"
                                        placeholder="{{ __('paid') }}" value="{{ $remaining }}" required />
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                data-bs-dismiss="modal">@lang('cancel')</button>
                            <button type="submit" class="btn btn-primary">@lang('pay_remaining')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endcan
@include('content._datatableAction')
