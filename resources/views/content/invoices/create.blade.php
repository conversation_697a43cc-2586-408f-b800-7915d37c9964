@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))


@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/leaflet/leaflet.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/leaflet/leaflet.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/form-layouts.js') }}"></script>
    <script src="{{ asset('js/functions/' . $name . '.js') }}"></script>
    <script src="{{ asset('js/functions/general.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col mb-4">
                                <label class="form-label required"
                                    for="financial_category_id">{{ __('financial_category') }}
                                    <i class="required ml-sm">*</i></label>
                                <select id="financial_category_id" class="form-select" name="financial_category_id">
                                    <option value="" {{ !old('financial_category_id') ? 'selected' : '' }}>
                                        {{ __('select_category') }}</option>
                                    @foreach ($categories as $category)
                                        @if ($category->id !== 3)
                                            <option value="{{ $category->id }}"
                                                {{ old('financial_category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->{'title_' . __('lang')} }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label required" for="amount">{{ __('amount') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" id="amount" name="amount" class="form-control"
                                    placeholder="{{ __('amount') }}" value="{{ old('amount') }}" />
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label" for="paid">{{ __('paid') }}</label>
                                <input type="text" id="paid" name="paid" class="form-control"
                                    placeholder="{{ __('paid') }}" value="{{ old('paid') }}" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 mb-4">
                                <label class="form-label" for="comment">{{ __('comment') }}</label>
                                <textarea id="comment" name="comment" class="form-control" placeholder="{{ __('comment') }}">{{ old('comment') }}</textarea>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
