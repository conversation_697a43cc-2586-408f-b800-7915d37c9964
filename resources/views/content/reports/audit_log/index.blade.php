@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('audit_log'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@push('scripts')
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.buttons.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/datatables/buttons.server-side.js"></script>
    <script src="{{ asset('dashboard_files') }}/dist/js/datatables.js"></script>

    {!! $dataTable->scripts() !!}
@endpush


@section('page-script')
    <script>
        const datepickerList = document.querySelectorAll('.dob-picker');
        // Flat Picker Birth Date
        if (datepickerList) {
            datepickerList.forEach(function(datepicker) {
                datepicker.flatpickr({
                    monthSelectorType: 'static',
                    enableTime: true,
                    dateFormat: "Y-m-d H:i",
                });
            });
        }
    </script>
@endsection

@section('content')
    <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center text-center mb-2">
        <h4 class="fw-semibold mb-4">{{ __('audit_log') }}</h4>
    </div>
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <form action="{{ route($name . '.audit_log.index') }}" method="GET">
                        @include('_partials._errors')
                        <div class="row">
                            <div class="col mb-4">
                                <label for="type" class="form-label">{{ __('type') }}</label>
                                <select id="type" class="form-select" name="type">

                                    <option value="" {{ !request('type') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    @for ($i = 1; $i <= 2; $i++)
                                        <option value="{{ $i }}" {{ request('type') == $i ? 'selected' : '' }}>
                                            {{ __("log_type_{$i}") }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label for="user_id" class="form-label">{{ __('user') }}</label>
                                <select id="user_id" class="form-select" name="user_id">
                                    <option value="" {{ !request('user_id') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    @foreach ($users as $user)
                                        <option value="{{ $user->id }}"
                                            {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label class="form-label" for="from_date">{{ __('from_date') }}</label>
                                <input type="text" id="from_date" name="from_date" class="form-control dob-picker"
                                    placeholder="{{ __('from_date') }}" style="direction:ltr;"
                                    value="{{ request('from_date', $dayStart) }}" />
                            </div>
                            <div class="col mb-4">
                                <label class="form-label" for="to_date">{{ __('to_date') }}</label>
                                <input type="text" id="to_date" name="to_date" class="form-control dob-picker"
                                    placeholder="{{ __('to_date') }}" style="direction:ltr;"
                                    value="{{ request('to_date', $now) }}" />
                            </div>
                            <div class="col text-center mt-4">
                                <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('filter') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(
                        ['class' => 'dataTable datatables-{{ $nameS }} table table-hover', 'responsive' => 'true'],
                        true,
                    ) !!}
                </div>
            </div>
        </div>
    </div>

@endsection
