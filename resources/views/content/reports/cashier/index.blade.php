@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __($nameS . '_cashier'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@push('scripts')
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.buttons.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/datatables/buttons.server-side.js"></script>
    <script src="{{ asset('dashboard_files') }}/dist/js/datatables.js"></script>

    {!! $dataTable->scripts() !!}
@endpush

@section('page-script')
    <script>
        const datepickerList = document.querySelectorAll('.dob-picker');
        // Flat Picker Birth Date
        if (datepickerList) {
            datepickerList.forEach(function(datepicker) {
                datepicker.flatpickr({
                    monthSelectorType: 'static',
                    enableTime: true,
                    dateFormat: "Y-m-d H:i",
                });
            });
        }
    </script>
@endsection

@section('content')
    <h4 class="fw-semibold mb-4">{{ __($nameS . '_cashier') }}</h4>
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white mb-3">
                    <form action="{{ route($name . '.cashier.index') }}" method="GET">
                        @include('_partials._errors')
                        <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center row">
                            <div class="col mb-4">
                                <label for="financial_category_id"
                                    class="form-label text-white fw-bold">{{ __('financial_category') }}</label>
                                <select id="financial_category_id" class="form-select" name="financial_category_id">
                                    <option value="" {{ !request('financial_category_id') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    @foreach ($categories as $category)
                                        <option value="{{ $category->id }}"
                                            {{ request('financial_category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->{'title_' . __('lang')} }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label for="type" class="form-label text-white fw-bold">{{ __('type') }}</label>
                                <select id="type" class="form-select" name="type">
                                    <option value="" {{ !request('type') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    <option value="0" {{ request('type') == '0' ? 'selected' : '' }}>
                                        {{ __('cash_in') }}</option>
                                    <option value="1" {{ request('type') == '1' ? 'selected' : '' }}>
                                        {{ __('cash_out') }}</option>
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label for="status" class="form-label text-white fw-bold">{{ __('status') }}</label>
                                <select id="status" class="form-select" name="status">
                                    <option value="" {{ !request('status') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>
                                        {{ __('invoice_status_0') }}</option>
                                    <option value="1" {{ request('status') == 1 ? 'selected' : '' }}>
                                        {{ __('invoice_status_1') }}</option>
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label for="user_id" class="form-label text-white fw-bold">{{ __('user') }}</label>
                                <select id="user_id" class="form-select" name="user_id">
                                    <option value="" {{ !request('user_id') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    @foreach ($users as $user)
                                        <option value="{{ $user->id }}"
                                            {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label class="form-label text-white fw-bold" for="from_date">{{ __('from_date') }}</label>
                                <input type="text" id="from_date" name="from_date" class="form-control dob-picker"
                                    placeholder="{{ __('from_date') }}" style="direction:ltr;"
                                    value="{{ request('from_date', $dayStart) }}" />
                            </div>
                            <div class="col mb-4">
                                <label class="form-label text-white fw-bold" for="to_date">{{ __('to_date') }}</label>
                                <input type="text" id="to_date" name="to_date" class="form-control dob-picker"
                                    placeholder="{{ __('to_date') }}" style="direction:ltr;"
                                    value="{{ request('to_date', $now) }}" />
                            </div>
                            <div class="col text-center mt-4">
                                <button type="submit" class="btn btn-success me-sm-3 me-1">{{ __('filter') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(
                        ['class' => 'dataTable datatables-{{ $nameS }} table table-hover', 'responsive' => 'true'],
                        true,
                    ) !!}
                </div>
                <div class="card-footer bg-primary text-white ">
                    <div class="row text-center">
                        @foreach ($categories as $category)
                            @if (!request('financial_category_id') || request('financial_category_id') == $category->id)
                            <div class="col-3 border m-3">
                                <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center p-2">
                                    <span class="label">
                                        <span
                                            class="label bg-{{ $category->id !== 3 ? ($category->deduct ? 'danger' : 'success') : 'primary' }} p-1">{{ $category->id !== 3 ? ($category->deduct ? '-' : '+') : '' }}</span>
                                        {{ $category->{'title_' . __('lang')} }}
                                    </span>:
                                    <span class="p-1">
                                        [
                                        @if ($category->id !== 3 && $results->where('financial_category_id', $category->id)->sum('remaining') != 0)
                                            <span class="label">@lang('amount'):
                                                <b>{{ number_format($results->where('financial_category_id', $category->id)->sum('amount')) }}</b>
                                            </span>
                                        @endif
                                        <span class="label">
                                            {{ __($category->id !== 3 ? ($category->deduct ? 'sub' : 'paid') : 'amount') }}:
                                            <b>{{ number_format($results->where('financial_category_id', $category->id)->sum('paid')) }}</b>

                                        </span>
                                        @if ($category->id !== 3 && $results->where('financial_category_id', $category->id)->sum('remaining') != 0)
                                            <span class="label">@lang('remaining'):
                                                <b>{{ number_format($results->where('financial_category_id', $category->id)->sum('remaining')) }}</b>

                                            </span>
                                        @endif
                                        ]
                                    </span>
                                </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                    <hr />
                    <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center text-center">
                        @if (!empty($statistics['cash']['total']))
                            <span class="label p-2">@lang('cash_before'):
                                <b>{{ number_format($statistics['cash']['total']) }}</b></span>
                        @endif
                        @if (!empty($statistics['in']['total']))
                            <span class="label p-2"><span class="label bg-success p-1">+</span>
                                @lang('total_in'): <b>{{ number_format($statistics['in']['total']) }}</b></span>
                            @if (!empty($statistics['in']['remaining']))
                                <span class="label p-1">[
                                    <span class="label p-1">@lang('paid_in'):
                                        <b>{{ number_format($statistics['in']['paid']) }}</b></span>
                                    <span class="label p-1">@lang('remaining'):
                                        <b>{{ number_format($statistics['in']['remaining']) }}</b></span>
                                    ]
                                </span>
                            @endif
                        @endif
                        @if (!empty($statistics['out']['total']))
                            <span class="label p-2"><span class="label bg-danger p-1">-</span>
                                @lang('total_out'): <b>{{ number_format($statistics['out']['total']) }}</b></span>
                            @if (!empty($statistics['out']['remaining']))
                                <span class="label p-1">[
                                    <span class="label p-1">@lang('paid_out'):
                                        <b>{{ number_format($statistics['out']['paid']) }}</b></span>
                                    <span class="label p-1">@lang('remaining'):
                                        <b>{{ number_format($statistics['out']['remaining']) }}</b></span>
                                    ]
                                </span>
                            @endif
                        @endif
                        @if (
                            !empty($statistics['cash']['required']) &&
                                (!empty($statistics['in']['paid']) || !empty($statistics['out']['paid'])))
                            <span class="label p-3"><span
                                    class="label bg-{{ $statistics['cash']['required'] > 0 ? 'success' : 'danger' }} p-1">{{ $statistics['cash']['required'] > 0 ? '+' : '-' }}</span>
                                @lang('total_required'): <b>{{ number_format($statistics['cash']['required']) }}</b></span>
                        @endif
                    </div>
                    @if (!empty($total['atm_fees']) || !empty($total['fees']) || !empty($total['commission']))
                        <hr />
                        <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center text-center">
                            <span class="label p-2">@lang('total_transactions_profit')</span>
                            <span class="label p-2">[
                                @if (!empty($total['atm_fees']))
                                    <span class="label p-1">@lang('atm_fees'):
                                        <b>{{ number_format($total['atm_fees']) }}</b></span>
                                @endif
                                @if (!empty($total['commission']))
                                    <span class="label p-1">@lang('commission'):
                                        <b>{{ number_format($total['commission']) }}</b></span>
                                @endif
                                @if (!empty($total['fees']))
                                    <span class="label p-1">@lang('fees'):
                                        <b>{{ number_format($total['fees']) }}</b></span>
                                    <span class="label p-1">@lang('profit'):
                                        <b>{{ number_format($total['commission'] - $total['fees']) }}</b></span>
                                @endif
                                ]
                            </span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

@endsection
