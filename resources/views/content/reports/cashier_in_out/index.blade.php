@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __($nameS . '_cashier_in_out'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@push('scripts')
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.buttons.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/datatables/buttons.server-side.js"></script>
    <script src="{{ asset('dashboard_files') }}/dist/js/datatables.js"></script>

    {!! $dataTable->scripts() !!}
@endpush

@section('page-script')
    <script>
        const datepickerList = document.querySelectorAll('.dob-picker');
        // Flat Picker Birth Date
        if (datepickerList) {
            datepickerList.forEach(function(datepicker) {
                datepicker.flatpickr({
                    monthSelectorType: 'static',
                    enableTime: true,
                    dateFormat: "Y-m-d H:i",
                });
            });
        }
    </script>
@endsection

@section('content')
    <h4 class="fw-semibold mb-4">{{ __($nameS . '_cashier_in_out') }}</h4>
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white mb-3">
                    <form action="{{ route($name . '.cashier_in_out.index') }}" method="GET">
                        @include('_partials._errors')
                        <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center row">
                            <div class="col mb-4">
                                <label for="financial_category_id"
                                    class="form-label text-white fw-bold">{{ __('financial_category') }}</label>
                                <select id="financial_category_id" class="form-select" name="financial_category_id">
                                    <option value="" {{ !request('financial_category_id') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    @foreach ($categories as $category)
                                        <option value="{{ $category->id }}"
                                            {{ request('financial_category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->{'title_' . __('lang')} }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label for="status" class="form-label text-white fw-bold">{{ __('status') }}</label>
                                <select id="status" class="form-select" name="status">
                                    <option value="" {{ !request('status') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>
                                        {{ __('invoice_status_0') }}</option>
                                    <option value="1" {{ request('status') == 1 ? 'selected' : '' }}>
                                        {{ __('invoice_status_1') }}</option>
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label for="user_id" class="form-label text-white fw-bold">{{ __('user') }}</label>
                                <select id="user_id" class="form-select" name="user_id">
                                    <option value="" {{ !request('user_id') ? 'selected' : '' }}>
                                        {{ __('all') }}</option>
                                    @foreach ($users as $user)
                                        <option value="{{ $user->id }}"
                                            {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col mb-4">
                                <label class="form-label text-white fw-bold" for="from_date">{{ __('from_date') }}</label>
                                <input type="text" id="from_date" name="from_date" class="form-control dob-picker"
                                    placeholder="{{ __('from_date') }}" style="direction:ltr;"
                                    value="{{ request('from_date', $dayStart) }}" />
                            </div>
                            <div class="col mb-4">
                                <label class="form-label text-white fw-bold" for="to_date">{{ __('to_date') }}</label>
                                <input type="text" id="to_date" name="to_date" class="form-control dob-picker"
                                    placeholder="{{ __('to_date') }}" style="direction:ltr;"
                                    value="{{ request('to_date', $now) }}" />
                            </div>
                            <div class="col text-center mt-4">
                                <button type="submit" class="btn btn-success me-sm-3 me-1">{{ __('filter') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(
                        ['class' => 'dataTable datatables-{{ $nameS }} table table-hover', 'responsive' => 'true'],
                        true,
                    ) !!}
                </div>
                <div class="card-footer bg-primary text-white ">
                    <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center text-center">
                        @foreach ($categories as $category)
                            @if (!request('financial_category_id') || request('financial_category_id') == $category->id)
                                <div class="d-block d-md-flex d-lg-flex justify-content-between align-items-center p-2">
                                    <span class="label">
                                        <span
                                            class="label bg-{{ $category->id !== 3 ? ($category->deduct ? 'danger' : 'success') : 'primary' }} p-1">{{ $category->id !== 3 ? ($category->deduct ? '-' : '+') : '' }}</span>
                                        {{ $category->{'title_' . __('lang')} }}
                                    </span>:
                                    <span class="p-1">
                                        [
                                        @if ($category->id !== 3 && $results->where('financial_category_id', $category->id)->sum('remaining') != 0)
                                            <span class="label">@lang('amount'):
                                                <b>{{ number_format($results->where('financial_category_id', $category->id)->sum('amount')) }}</b>
                                            </span>
                                        @endif
                                        <span class="label">
                                            {{ __($category->id !== 3 ? ($category->deduct ? 'sub' : 'paid') : 'amount') }}:
                                            <b>{{ number_format($results->where('financial_category_id', $category->id)->sum('paid')) }}</b>

                                        </span>
                                        @if ($category->id !== 3 && $results->where('financial_category_id', $category->id)->sum('remaining') != 0)
                                            <span class="label">@lang('remaining'):
                                                <b>{{ number_format($results->where('financial_category_id', $category->id)->sum('remaining')) }}</b>

                                            </span>
                                        @endif
                                        ]
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
