<div class="dropdown">
    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i
            class="ti ti-dots-vertical"></i></button>
    <div class="dropdown-menu">
        @can('read service_category_forms')
            <a href="{{ route('service_categories_forms.index', $id) }}" class="dropdown-item" title="{{ __('service_category_form') }}">
                <i class="ti ti-file-text me-1"></i> {{ __('service_category_form') }}
            </a>
        @endcan
        @can('update service_categories')
            <a href="{{ route('service_categories.edit', $id) }}" class="dropdown-item"
                title="{{ __('edit') }}">{{ __('edit') }}</a>
        @endcan

        @can('delete service_categories')
            <form action="{{ route('service_categories.destroy', $id) }}" method="post" class="mb-0">
                @csrf
                @method('DELETE')
                <button class="dropdown-item delete" title="{{ __('delete') }}">
                    {{ __('delete') }}
                </button>
            </form>
        @endcan
    </div>
</div>
@include('content._datatableAction')
