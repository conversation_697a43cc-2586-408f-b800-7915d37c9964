@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('edit') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('edit') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.update', $item->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_en">{{ __('title_en') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" id="title_en" name="title_en" class="form-control"
                                    placeholder="{{ __('title_en') }}" value="{{ old('title_en', $item->title_en) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_ar">{{ __('title_ar') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" id="title_ar" name="title_ar" class="form-control"
                                    placeholder="{{ __('title_ar') }}" value="{{ old('title_ar', $item->title_ar) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="icon">{{ __('icon') }}</label>
                                <input type="file" id="icon" name="icon" class="form-control" accept="image/*" />
                                @if (isset($item) && $item->icon)
                                    <div class="mt-2">
                                        <img src="{{ asset('storage/' . $item->icon) }}" alt="Icon" style="max-width: 100px;" />
                                    </div>
                                @endif
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="image">{{ __('image') }}</label>
                                <input type="file" id="image" name="image" class="form-control" accept="image/*" />
                                @if (isset($item) && $item->image)
                                    <div class="mt-2">
                                        <img src="{{ asset('storage/' . $item->image) }}" alt="Image" style="max-width: 100px;" />
                                    </div>
                                @endif
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="order">{{ __('order') }}</label>
                                <input type="number" id="order" name="order" class="form-control"
                                    placeholder="{{ __('order') }}" value="{{ old('order', $item->order) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="reservation_type">{{ __('reservation_type') }}</label>
                                <select id="reservation_type" name="reservation_type" class="form-select">
                                    <option value="1" {{ old('reservation_type', $item->reservation_type) == 1 ? 'selected' : '' }}>@lang('reservation_type_1')</option>
                                    <option value="2" {{ old('reservation_type', $item->reservation_type) == 2 ? 'selected' : '' }}>@lang('reservation_type_2')</option>
                                </select>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="reservation_confirmation">{{ __('reservation_confirmation') }}</label>
                                <select id="reservation_confirmation" name="reservation_confirmation" class="form-select">
                                    <option value="0" {{ old('reservation_confirmation', $item->reservation_confirmation) == 0 ? 'selected' : '' }}>@lang('no')</option>
                                    <option value="1" {{ old('reservation_confirmation', $item->reservation_confirmation) == 1 ? 'selected' : '' }}>@lang('yes')</option>
                                </select>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="enable_register">{{ __('enable_register') }}</label>
                                <select id="enable_register" name="enable_register" class="form-select">
                                    <option value="0" {{ old('enable_register', $item->enable_register) == 0 ? 'selected' : '' }}>@lang('no')</option>
                                    <option value="1" {{ old('enable_register', $item->enable_register) == 1 ? 'selected' : '' }}>@lang('yes')</option>
                                </select>
                            </div>

                            <div class="col-6 mb-4">
                                <label class="form-label" for="can_add_item">{{ __('can_add_item') }}</label>
                                <select id="can_add_item" name="can_add_item" class="form-select">
                                    <option value="0" {{ old('can_add_item', $item->can_add_item) == 0 ? 'selected' : '' }}>@lang('no')</option>
                                    <option value="1" {{ old('can_add_item', $item->can_add_item) == 1 ? 'selected' : '' }}>@lang('yes')</option>
                                </select>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="commission">{{ __('commission') }}</label>
                                <input type="number" step="0.01" id="commission" name="commission" class="form-control"
                                    placeholder="{{ __('commission') }}" value="{{ old('commission', $item->commission) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="user_type_id">{{ __('user_type') }} <i
                                        class="required ml-sm">*</i></label>
                                <select id="user_type_id" name="user_type_id" class="form-select">
                                    @foreach($userTypes as $userType)
                                        <option value="{{ $userType->id }}" {{ old('user_type_id', $item->user_type_id) == $userType->id ? 'selected' : '' }}>{{ $userType->title_en }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="parent">{{ __('parent_category') }}</label>
                                <select id="parent" name="parent" class="form-select">
                                    <option value="">@lang('no_parent')</option>
                                    @foreach($parentCategories as $category)
                                        <option value="{{ $category->id }}" {{ old('parent', $item->parent) == $category->id ? 'selected' : '' }}>{{ $category->title_en }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
