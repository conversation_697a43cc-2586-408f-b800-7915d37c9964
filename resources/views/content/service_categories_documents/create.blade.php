@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_en">{{ __('title_en') }} <i class="required ml-sm">*</i></label>
                                <input type="text" id="title_en" name="title_en" class="form-control" placeholder="{{ __('title_en') }}" value="{{ old('title_en') }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_ar">{{ __('title_ar') }} <i class="required ml-sm">*</i></label>
                                <input type="text" id="title_ar" name="title_ar" class="form-control" placeholder="{{ __('title_ar') }}" value="{{ old('title_ar') }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="icon">{{ __('icon') }}</label>
                                <input type="file" id="icon" name="icon" class="form-control" accept="image/*" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="example">{{ __('example') }}</label>
                                <input type="file" id="example" name="example" class="form-control" accept=".pdf,.doc,.docx" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="display">{{ __('display') }}</label>
                                <input type="checkbox" id="display" name="display" class="form-check-input" value="1" {{ old('display') ? 'checked' : '' }} />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="required">{{ __('required') }}</label>
                                <input type="checkbox" id="required" name="required" class="form-check-input" value="1" {{ old('required') ? 'checked' : '' }} />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="multiple">{{ __('multiple') }}</label>
                                <input type="checkbox" id="multiple" name="multiple" class="form-check-input" value="1" {{ old('multiple') ? 'checked' : '' }} />
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection