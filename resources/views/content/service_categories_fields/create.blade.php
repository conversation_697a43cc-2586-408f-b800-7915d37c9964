@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_en">{{ __('title_en') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" id="title_en" name="title_en" class="form-control"
                                    placeholder="{{ __('title_en') }}" value="{{ old('title_en') }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_ar">{{ __('title_ar') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" id="title_ar" name="title_ar" class="form-control"
                                    placeholder="{{ __('title_ar') }}" value="{{ old('title_ar') }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="type">{{ __('type') }} <i
                                        class="required ml-sm">*</i></label>
                                <select id="type" name="type" class="form-select">
                                    @foreach ([
                                        1 => __('text'),
                                        2 => __('numeric'),
                                        3 => __('numeric_decimal'),
                                        4 => __('phone'),
                                        5 => __('password_text'),
                                        6 => __('password_numeric'),
                                        7 => __('checkbox'),
                                        8 => __('radio'),
                                        9 => __('text_area'),
                                        10 => __('date'),
                                        11 => __('date_time'),
                                        12 => __('time'),
                                        13 => __('date_from_to'),
                                        14 => __('date_time_from_to'),
                                    ] as $key => $value)
                                        <option value="{{ $key }}" {{ old('type') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="icon">{{ __('icon') }}</label>
                                <input type="file" id="icon" name="icon" class="form-control" accept="image/*" />
                                @if (isset($item) && $item->icon)
                                    <div class="mt-2">
                                        <img src="{{ asset('storage/' . $item->icon) }}" alt="Icon" style="max-width: 100px;" />
                                    </div>
                                @endif
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="display">{{ __('display') }}</label>
                                <input type="checkbox" id="display" name="display" class="form-check-input" value="1"
                                    {{ old('display') ? 'checked' : '' }} />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="required">{{ __('required') }}</label>
                                <input type="checkbox" id="required" name="required" class="form-check-input" value="1"
                                    {{ old('required') ? 'checked' : '' }} />
                            </div>
                            <!-- Options for type 7 or 8 -->
                            <div class="col-12 mb-4" id="options-section" style="display: {{ in_array(old('type'), [7, 8]) ? 'block' : 'none' }};">
                                <label class="form-label">{{ __('options') }}</label>
                                <div id="options-container">
                                    @if (old('options'))
                                        @foreach (old('options') as $index => $option)
                                            <div class="option-item mb-2 d-flex align-items-center">
                                                <input type="text" name="options[{{ $index }}][title_en]" class="form-control me-2" placeholder="{{ __('title_en') }}" value="{{ $option['title_en'] }}" />
                                                <input type="text" name="options[{{ $index }}][title_ar]" class="form-control me-2" placeholder="{{ __('title_ar') }}" value="{{ $option['title_ar'] }}" />
                                                <button type="button" class="btn btn-danger btn-sm delete-option">×</button>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                                <button type="button" class="btn btn-sm btn-primary" id="add-option">{{ __('add_option') }}</button>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.getElementById('type').addEventListener('change', function() {
            const optionsSection = document.getElementById('options-section');
            if (this.value == 7 || this.value == 8) {
                optionsSection.style.display = 'block';
            } else {
                optionsSection.style.display = 'none';
            }
        });

        let optionIndex = {{ old('options') ? count(old('options')) : 0 }};
        document.getElementById('add-option').addEventListener('click', function() {
            const optionsContainer = document.getElementById('options-container');
            const newOption = document.createElement('div');
            newOption.classList.add('option-item', 'mb-2', 'd-flex', 'align-items-center');
            newOption.innerHTML = `
                <input type="text" name="options[${optionIndex}][title_en]" class="form-control me-2" placeholder="{{ __('title_en') }}" />
                <input type="text" name="options[${optionIndex}][title_ar]" class="form-control me-2" placeholder="{{ __('title_ar') }}" />
                <button type="button" class="btn btn-danger btn-sm delete-option">×</button>
            `;
            optionsContainer.appendChild(newOption);
            optionIndex++;
        });

        // Delete option
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('delete-option')) {
                event.target.closest('.option-item').remove();
            }
        });
    </script>
@endpush