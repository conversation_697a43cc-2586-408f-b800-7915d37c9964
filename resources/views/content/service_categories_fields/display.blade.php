<div class="col-12 mb-4">
    <label class="form-label {{ $item->required ? 'required' : '' }}" for="item_field_{{ $id }}">
        {{ $item->{'title_' . __('lang')} }}
        @if($item->required)
            <i class="required ml-sm">*</i>
        @endif
    </label>

    @if($item->type == 7) <!-- Checkbox -->
        <div>
            @foreach($item->options as $option)
                <div class="form-check">
                    <input type="checkbox" id="item_field_{{ $id }}_{{ $option->id }}" name="formfield[{{ $id }}][]" class="form-check-input"
                           value="{{ $option->id }}" {{ in_array($option->id, old('formfield.' . $id, [])) ? 'checked' : '' }} />
                    <label class="form-check-label" for="item_field_{{ $id }}_{{ $option->id }}">
                        {{ $option->{'title_' . __('lang')} }}
                    </label>
                </div>
            @endforeach
        </div>
    @elseif($item->type == 8) <!-- Radio -->
        <div>
            @foreach($item->options as $option)
                <div class="form-check">
                    <input type="radio" id="item_field_{{ $id }}_{{ $option->id }}" name="formfield[{{ $id }}]" class="form-check-input"
                           value="{{ $option->id }}" {{ old('formfield.' . $id) == $option->id ? 'checked' : '' }} />
                    <label class="form-check-label" for="item_field_{{ $id }}_{{ $option->id }}">
                        {{ $option->{'title_' . __('lang')} }}
                    </label>
                </div>
            @endforeach
        </div>
    @else <!-- Other Field Types -->
        <input type="{{ $item->getInputType() }}" id="item_field_{{ $id }}" name="formfield[{{ $id }}]" class="form-control"
               placeholder="{{ $item->{'title_' . __('lang')} }}" value="{{ old('formfield.' . $id) }}" />
    @endif

    @error('formfield.' . $id)
        <div class="text-danger">{{ $message }}</div>
    @enderror
</div>
