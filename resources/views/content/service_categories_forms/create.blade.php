@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index', $item->id) }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store', $item->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="d-flex align-items-center justify-content-center gap-3">
                                    <!-- Select Type Dropdown -->
                                    <div class="flex-grow-1">
                                        <label class="form-label required" for="type">{{ __('form_type') }} <i class="required ml-sm">*</i></label>
                                        <select id="type" name="type" class="form-control" required>
                                            <option value="">{{ __('select_form_type') }}</option>
                                            <option value="1" {{ old('type') == '1' ? 'selected' : '' }}>{{ __('registration_form') }}</option>
                                            <option value="2" {{ old('type') == '2' ? 'selected' : '' }}>{{ __('add_form') }}</option>
                                            <option value="3" {{ old('type') == '3' ? 'selected' : '' }}>{{ __('reservation_form') }}</option>
                                        </select>
                                    </div>

                                    <!-- Buttons -->
                                    <div class="d-flex align-items-end gap-2">
                                        <button type="button" id="add-field" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> {{ __('add_field') }}
                                        </button>
                                        <button type="button" id="add-document" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> {{ __('add_document') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Items Container -->
                        <div class="col-12 mb-4">
                            <label class="form-label">{{ __('form_items') }}</label>
                            <div id="form-items-container">
                                <!-- Dynamically added form items will appear here -->
                            </div>
                        </div>

                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('page-script')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const formItemsContainer = document.getElementById('form-items-container');
            const addFieldButton = document.getElementById('add-field');
            const addDocumentButton = document.getElementById('add-document');

            let itemCounter = {{ count(old('form_items', [])) }};

            // Repopulate dynamically added form items from old input
            @if(old('form_items'))
                @foreach(old('form_items') as $index => $item)
                    const html = `
                        <div class="form-item mb-3 p-3 border rounded">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">{{ __('select_field') }}</label>
                                    <select name="form_items[${itemCounter}][field_id]" class="form-control select-field" required>
                                        <option value="">{{ __('select_field') }}</option>
                                        @foreach($fields as $field)
                                            <option value="{{ $field->id }}" {{ $item['field_id'] == $field->id ? 'selected' : '' }}>{{ $field->title_en }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">{{ __('order') }}</label>
                                    <input type="number" name="form_items[${itemCounter}][order]" class="form-control" placeholder="{{ __('order') }}" value="{{ $item['order'] }}" required>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-danger mt-2 remove-form-item">
                                <i class="fas fa-trash"></i> {{ __('remove') }}
                            </button>
                        </div>
                    `;

                    const itemDiv = document.createElement('div');
                    itemDiv.innerHTML = html;
                    formItemsContainer.appendChild(itemDiv);

                    // Initialize Select2 for the field dropdown
                    $(itemDiv).find('.select-field').select2();

                    // Add event listener to the remove button
                    itemDiv.querySelector('.remove-form-item').addEventListener('click', function () {
                        formItemsContainer.removeChild(itemDiv);
                    });

                    itemCounter++;
                @endforeach
            @endif

            // Add Field
            addFieldButton.addEventListener('click', function () {
                const html = `
                    <div class="form-item mb-3 p-3 border rounded">
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">{{ __('select_field') }}</label>
                                <select name="form_items[${itemCounter}][field_id]" class="form-control select-field" required>
                                    <option value="">{{ __('select_field') }}</option>
                                    @foreach($fields as $field)
                                        <option value="{{ $field->id }}">{{ $field->title_en }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label">{{ __('order') }}</label>
                                <input type="number" name="form_items[${itemCounter}][order]" class="form-control" placeholder="{{ __('order') }}" required>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger mt-2 remove-form-item">
                            <i class="fas fa-trash"></i> {{ __('remove') }}
                        </button>
                    </div>
                `;

                const itemDiv = document.createElement('div');
                itemDiv.innerHTML = html;
                formItemsContainer.appendChild(itemDiv);

                // Initialize Select2 for the field dropdown
                $(itemDiv).find('.select-field').select2();

                // Add event listener to the remove button
                itemDiv.querySelector('.remove-form-item').addEventListener('click', function () {
                    formItemsContainer.removeChild(itemDiv);
                });

                itemCounter++;
            });

            // Add Document
            addDocumentButton.addEventListener('click', function () {
                const html = `
                    <div class="form-item mb-3 p-3 border rounded">
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">{{ __('select_document') }}</label>
                                <select name="form_items[${itemCounter}][document_id]" class="form-control select-document" required>
                                    <option value="">{{ __('select_document') }}</option>
                                    @foreach($documents as $document)
                                        <option value="{{ $document->id }}">{{ $document->title_en }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label">{{ __('order') }}</label>
                                <input type="number" name="form_items[${itemCounter}][order]" class="form-control" placeholder="{{ __('order') }}" required>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger mt-2 remove-form-item">
                            <i class="fas fa-trash"></i> {{ __('remove') }}
                        </button>
                    </div>
                `;

                const itemDiv = document.createElement('div');
                itemDiv.innerHTML = html;
                formItemsContainer.appendChild(itemDiv);

                // Initialize Select2 for the document dropdown
                $(itemDiv).find('.select-document').select2();

                // Add event listener to the remove button
                itemDiv.querySelector('.remove-form-item').addEventListener('click', function () {
                    formItemsContainer.removeChild(itemDiv);
                });

                itemCounter++;
            });
        });
    </script>
@endsection