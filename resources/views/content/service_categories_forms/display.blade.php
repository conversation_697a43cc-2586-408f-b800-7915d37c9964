@inject('form', 'App\Models\Admin\ServiceCategoryForm')
@inject('category', 'App\Models\Admin\ServiceCategory')
@php
$category = $category->find($service_category_id);

if ($category) {
    // Check if the category is a parent or a child
    if ($category->parent) {
        // It's a child category
        // First, try to get the form items for the child category
        $formData = $form->with(['formItems.field.options', 'formItems.document'])
                         ->where('service_category_id', $service_category_id)
                         ->where('type', $type)
                         ->first();

        // If no form items are found for the child, fall back to the parent category
        if (!$formData) {
            $parentCategoryId = $category->parent;
            $formData = $form->with(['formItems.field.options', 'formItems.document'])
                             ->where('service_category_id', $parentCategoryId)
                             ->where('type', $type)
                             ->first();
        }
    } else {
        // It's a parent category, get the form items directly
        $formData = $form->with(['formItems.field.options', 'formItems.document'])
                         ->where('service_category_id', $service_category_id)
                         ->where('type', $type)
                         ->first();
    }
} else {
    // Handle the case where the category is not found
    $formData = null;
}
@endphp
@if($formData && $formData->formItems->count() > 0)
@foreach ($formData->formItems as $fi)
@if($fi->field)
@include('content.service_categories_fields.display',['id' => $fi->id, 'item' => $fi->field])
@elseif($fi->document)
@include('content.service_categories_documents.display',['id' => $fi->id, 'item' => $fi->document])
@endif
@endforeach
@endif
