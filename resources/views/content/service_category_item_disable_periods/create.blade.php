@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mt-2">{{ __('add') . ' ' . __($nameS) }}</h4>
                        <a href="{{ route($name . '.index', $item->id) }}" class="btn btn-outline-primary float-right">
                            <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                @include('_partials._errors')
                <form action="{{ route($name . '.store', $item->id) }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('disable_start') }}</label>
                                <input type="datetime-local" name="disable_start" class="form-control" required>
                            </div>
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('disable_end') }}</label>
                                <input type="datetime-local" name="disable_end" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
