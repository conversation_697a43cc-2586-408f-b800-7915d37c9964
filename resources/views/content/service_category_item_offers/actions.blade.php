<div class="dropdown">
    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
        <i class="ti ti-dots-vertical"></i>
    </button>
    <div class="dropdown-menu">
        @can('update service_category_item_offers')
            <a href="{{ route('service_category_item_offers.edit', [$service_category_item_id, $item->id]) }}" class="dropdown-item" title="{{ __('edit') }}">
                {{ __('edit') }}
            </a>
        @endcan

        @can('delete service_category_item_offers')
            <form action="{{ route('service_category_item_offers.destroy', [$service_category_item_id, $item->id]) }}" method="post" class="mb-0">
                @csrf
                @method('DELETE')
                <button class="dropdown-item delete" title="{{ __('delete') }}">
                    {{ __('delete') }}
                </button>
            </form>
        @endcan
    </div>
</div>
@include('content._datatableAction')
