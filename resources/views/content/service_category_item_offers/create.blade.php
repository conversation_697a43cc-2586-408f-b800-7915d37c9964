@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mt-2">{{ __('add') . ' ' . __($nameS) }}</h4>
                        <a href="{{ route($name . '.index', $item->id) }}" class="btn btn-outline-primary float-right">
                            <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store', $item->id) }}" method="POST">
                        @csrf
                        <div class="row">
                            <!-- Title (English) -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('title_en') }}</label>
                                <input type="text" name="title_en" class="form-control" required>
                            </div>

                            <!-- Title (Arabic) -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('title_ar') }}</label>
                                <input type="text" name="title_ar" class="form-control" required>
                            </div>

                            <!-- Value -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('value') }}</label>
                                <input type="number" name="value" class="form-control" required>
                            </div>

                            <!-- Type -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('type') }}</label>
                                <select name="type" class="form-control" required>
                                    <option value="1">{{ __('percentage') }}</option>
                                    <option value="2">{{ __('fixed') }}</option>
                                </select>
                            </div>

                            <!-- Apply To -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('apply_to') }}</label>
                                <select name="apply_to" class="form-control" required>
                                    <option value="0">{{ __(key: 'all') }}</option>
                                    <option value="1">{{ __('price') }}</option>
                                    <option value="2">{{ __('weekend_price') }}</option>
                                </select>
                            </div>

                            <!-- Offer Start -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('offer_start') }}</label>
                                <input type="datetime-local" name="offer_start" class="form-control" required>
                            </div>

                            <!-- Offer End -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('offer_end') }}</label>
                                <input type="datetime-local" name="offer_end" class="form-control" required>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
