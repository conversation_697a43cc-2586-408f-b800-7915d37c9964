@extends('layouts/layoutMaster')

@section('title', __('edit') . ' ' . __($nameS))

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mt-2">{{ __('edit') . ' ' . __($nameS) }}</h4>
                        <a href="{{ route($name . '.index', $item->id) }}" class="btn btn-outline-primary float-right">
                            <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.update', [$item->id, $offer->id]) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <!-- Title (English) -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('title_en') }}</label>
                                <input type="text" name="title_en" class="form-control" value="{{ old('title_en', $offer->title_en) }}" required>
                            </div>

                            <!-- Title (Arabic) -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('title_ar') }}</label>
                                <input type="text" name="title_ar" class="form-control" value="{{ old('title_ar', $offer->title_ar) }}" required>
                            </div>

                            <!-- Value -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('value') }}</label>
                                <input type="number" name="value" class="form-control" value="{{ old('value', $offer->value) }}" required>
                            </div>

                            <!-- Type -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('type') }}</label>
                                <select name="type" class="form-control" required>
                                    <option value="1" {{ old('type', $offer->type) == 1 ? 'selected' : '' }}>{{ __('percentage') }}</option>
                                    <option value="2" {{ old('type', $offer->type) == 2 ? 'selected' : '' }}>{{ __('fixed') }}</option>
                                </select>
                            </div>

                            <!-- Apply To -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('apply_to') }}</label>
                                <select name="apply_to" class="form-control" required>
                                    <option value="0" {{ old('apply_to', $offer->apply_to) == 0 ? 'selected' : '' }}>{{ __('all') }}</option>
                                    <option value="1" {{ old('apply_to', $offer->apply_to) == 1 ? 'selected' : '' }}>{{ __('price') }}</option>
                                    <option value="2" {{ old('apply_to', $offer->apply_to) == 2 ? 'selected' : '' }}>{{ __('weekend_price') }}</option>
                                </select>
                            </div>

                            <!-- Offer Start -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('offer_start') }}</label>
                                <input type="datetime-local" name="offer_start" class="form-control" value="{{ old('offer_start', $offer->offer_start ? date('Y-m-d\TH:i', strtotime($offer->offer_start)) : '') }}" required>
                            </div>

                            <!-- Offer End -->
                            <div class="col-12 mb-4">
                                <label class="form-label">{{ __('offer_end') }}</label>
                                <input type="datetime-local" name="offer_end" class="form-control" value="{{ old('offer_end', $offer->offer_end ? date('Y-m-d\TH:i', strtotime($offer->offer_end)) : '') }}" required>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
