@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index', $item->id) }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store', $item->id) }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="reservation_date">{{ __('reservation_date') }} <i class="required ml-sm">*</i></label>
                                <input type="date" id="reservation_date" name="reservation_date" class="form-control" value="{{ old('reservation_date') }}" required>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="reservation_from">{{ __('reservation_from') }} <i class="required ml-sm">*</i></label>
                                <input type="datetime-local" id="reservation_from" name="reservation_from" class="form-control" value="{{ old('reservation_from') }}" required>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="reservation_to">{{ __('reservation_to') }} <i class="required ml-sm">*</i></label>
                                <input type="datetime-local" id="reservation_to" name="reservation_to" class="form-control" value="{{ old('reservation_to') }}" required>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label" for="confirmed">{{ __('confirmed') }}</label>
                                <select id="confirmed" name="confirmed" class="form-control">
                                    <option value="0" {{ old('confirmed') == 0 ? 'selected' : '' }}>{{ __('no') }}</option>
                                    <option value="1" {{ old('confirmed') == 1 ? 'selected' : '' }}>{{ __('yes') }}</option>
                                </select>
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="user_id">{{ __('user') }} <i class="required ml-sm">*</i></label>
                                <select id="user_id" name="user_id" class="form-control" required>
                                    <option value="">{{ __('select_user') }}</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
