<div class="dropdown">
    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i
            class="ti ti-dots-vertical"></i></button>
    <div class="dropdown-menu">
        @can('read service_category_item_reservations')
            <a href="{{ route('service_category_item_reservations.index', $id) }}" class="dropdown-item"
                title="{{ __('service_category_item_reservations') }}">
                <i class="ti ti-file-text me-1"></i> {{ __('service_category_item_reservations') }}
            </a>
        @endcan
        @can('read service_category_item_disable_periods')
            <a href="{{ route('service_category_item_disable_periods.index', $id) }}" class="dropdown-item"
                title="{{ __('service_category_item_disable_periods') }}">
                <i class="ti ti-file-text me-1"></i> {{ __('service_category_item_disable_periods') }}
            </a>
        @endcan
        @can('read service_category_item_offers')
            <a href="{{ route('service_category_item_offers.index', $id) }}" class="dropdown-item"
                title="{{ __('service_category_item_offers') }}">
                <i class="ti ti-file-text me-1"></i> {{ __('service_category_item_offers') }}
            </a>
        @endcan
        @can('read service_category_item_galleries')
            <a href="{{ route('service_category_item_galleries.index', $id) }}" class="dropdown-item"
                title="{{ __('service_category_item_galleries') }}">
                <i class="ti ti-file-text me-1"></i> {{ __('service_category_item_galleries') }}
            </a>
        @endcan
        @can('update service_category_items')
            <a href="{{ route('service_category_items.edit', $id) }}" class="dropdown-item"
                title="{{ __('edit') }}">{{ __('edit') }}</a>
        @endcan

        @can('delete service_category_items')
            <form action="{{ route('service_category_items.destroy', $id) }}" method="post" class="mb-0">
                @csrf
                @method('DELETE')
                <button class="dropdown-item delete" title="{{ __('delete') }}">
                    {{ __('delete') }}
                </button>
            </form>
        @endcan

    </div>
</div>
@include('content._datatableAction')
