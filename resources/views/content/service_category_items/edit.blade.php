@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('edit') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#service_category_id').on('change', function() {
            var serviceCategoryId = $(this).val();
            var type = 2; // Assuming type is fixed as 2

            if (!serviceCategoryId) {
                $('#fields_and_documents_container').html('');
                return;
            }

            // Get the base URL from the meta tag
            var appUrl = $('meta[name="app-url"]').attr('content');

            // Construct the full URL for the AJAX request
            var url = appUrl + 'settings/service_categories/get-fields-and-documents/' + serviceCategoryId + '/' + type;
            $.ajax({
                url: url,
                method: 'GET',
                success: function(response) {
                    $('#fields_and_documents_container').html(response.html);
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching fields and documents:', error);
                }
            });

        });
    });
</script>
@endpush

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('edit') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.update', $item->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="row">
                        <div class="col-12 mb-4">
                            <label class="form-label required" for="title">{{ __('title') }} <i class="required ml-sm">*</i></label>
                            <input type="text" id="title" name="title" class="form-control" placeholder="{{ __('title') }}" value="{{ old('title', $item->title) }}" required>
                        </div>
                        <div class="col-12 mb-4">
                            <label class="form-label" for="content">{{ __('content') }}</label>
                            <textarea id="content" name="content" class="form-control" placeholder="{{ __('content') }}">{{ old('content', $item->content) }}</textarea>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="image">{{ __('image') }}</label>
                            <input type="file" id="image" name="image" class="form-control">
                            @if ($item->image)
                                <img src="{{ $item->image }}" alt="Image" class="img-thumbnail mt-2" width="100">
                            @endif
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="video">{{ __('video') }}</label>
                            <input type="file" id="video" name="video" class="form-control">
                            @if ($item->video)
                                <video src="{{ $item->video }}" controls class="mt-2" width="100"></video>
                            @endif
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label required" for="price">{{ __('price') }} <i class="required ml-sm">*</i></label>
                            <input type="number" step="0.01" id="price" name="price" class="form-control" placeholder="{{ __('price') }}" value="{{ old('price', $item->price) }}" required>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="weekend_price">{{ __('weekend_price') }}</label>
                            <input type="number" step="0.01" id="weekend_price" name="weekend_price" class="form-control" placeholder="{{ __('weekend_price') }}" value="{{ old('weekend_price', $item->weekend_price) }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="week_price">{{ __('week_price') }}</label>
                            <input type="number" step="0.01" id="week_price" name="week_price" class="form-control" placeholder="{{ __('week_price') }}" value="{{ old('week_price', $item->week_price) }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="month_price">{{ __('month_price') }}</label>
                            <input type="number" step="0.01" id="month_price" name="month_price" class="form-control" placeholder="{{ __('month_price') }}" value="{{ old('month_price', $item->month_price) }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="lat">{{ __('latitude') }}</label>
                            <input type="number" step="0.000001" id="lat" name="lat" class="form-control" placeholder="{{ __('latitude') }}" value="{{ old('lat', $item->lat) }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="lon">{{ __('longitude') }}</label>
                            <input type="number" step="0.000001" id="lon" name="lon" class="form-control" placeholder="{{ __('longitude') }}" value="{{ old('lon', $item->lon) }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label required" for="service_category_id">{{ __('service_category') }} <i class="required ml-sm">*</i></label>
                            <select id="service_category_id" name="service_category_id" class="form-control" required>
                                @foreach($serviceCategories as $category)
                                    <option value="{{ $category->id }}" {{ $item->service_category_id == $category->id ? 'selected' : '' }}>{{ $category->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label required" for="user_id">{{ __('user') }} <i class="required ml-sm">*</i></label>
                            <select id="user_id" name="user_id" class="form-control" required>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ $item->user_id == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div id="fields_and_documents_container">
                            <!-- Fields and documents will be loaded here dynamically -->
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary">{{ __('submit') }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
