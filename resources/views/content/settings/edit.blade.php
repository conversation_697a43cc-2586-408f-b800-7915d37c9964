@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('settings'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')

    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('settings') }}</h4>
                </div>
            </div>
            <div class="card p-3 p-md-5">
                @include('_partials._errors')

                <form action="{{ route('settings.update') }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="mb-3">
                        <label for="min">{{ __('wallets_alert') }} <i class="required ml-sm">*</i></label>
                        <input type="number" min="1" name="wallets_alert" class="form-control" id="wallets_alert"
                            value="{{ $settings->wallets_alert ?? '' }}" placeholder="{{ __('wallets_alert') }}">
                    </div>

                    <div class="col-12 text-center mt-4">
                        <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

@endsection
