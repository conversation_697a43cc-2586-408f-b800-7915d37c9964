@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('email_setting'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')

    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('email_setting') }}</h4>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route('settings.email.update') }}" method="POST">
                        @csrf
                        @method('put')

                        <div class="mb-3">
                            <label for="mail_mailer" class="required">{{ __('mail_mailer') }}</label>
                            <input type="text" name="mail_mailer" value="{{ $emailSetting->mail_mailer }}"
                                class="form-control" id="mail_mailer" placeholder="{{ __('mail_mailer') }}">
                        </div>

                        <div class="mb-3">
                            <label for="mail_host" class="required">{{ __('mail_host') }}</label>
                            <input type="text" name="mail_host" value="{{ $emailSetting->mail_host }}"
                                class="form-control" id="mail_host" placeholder="{{ __('mail_host') }}">
                        </div>

                        <div class="mb-3">
                            <label for="mail_port" class="required">{{ __('mail_port') }}</label>
                            <input type="text" name="mail_port" value="{{ $emailSetting->mail_port }}"
                                class="form-control" id="mail_port" placeholder="{{ __('mail_port') }}">
                        </div>

                        <div class="mb-3">
                            <label for="mail_username" class="required">{{ __('mail_username') }}</label>
                            <input type="text" name="mail_username" value="{{ $emailSetting->mail_username }}"
                                class="form-control" id="mail_username" placeholder="{{ __('mail_username') }}">
                        </div>

                        <div class="mb-3">
                            <label for="mail_password" class="required">{{ __('mail_password') }}</label>
                            <input type="text" name="mail_password" value="{{ $emailSetting->mail_password }}"
                                class="form-control" id="mail_password" placeholder="{{ __('mail_password') }}">
                        </div>

                        <div class="mb-3">
                            <label for="mail_encryption" class="required">{{ __('mail_encryption') }}</label>
                            <input type="text" name="mail_encryption" value="{{ $emailSetting->mail_encryption }}"
                                class="form-control" id="mail_encryption" placeholder="{{ __('mail_encryption') }}">
                        </div>

                        <div class="mb-3">
                            <label for="from_address" class="required">{{ __('from_address') }}</label>
                            <input type="text" name="from_address" value="{{ $emailSetting->from_address }}"
                                class="form-control" id="from_address" placeholder="{{ __('from_address') }}">
                        </div>

                        <div class="mb-3">
                            <label for="from_name" class="required">{{ __('from_name') }}</label>
                            <input type="text" name="from_name" value="{{ $emailSetting->from_name }}"
                                class="form-control" id="from_name" placeholder="{{ __('from_name') }}">
                        </div>

                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
