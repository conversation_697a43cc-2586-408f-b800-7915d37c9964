@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('payment_gateway_setting'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('payment_gateway_setting') }}</h4>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route('settings.payment_gateway.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="api_key">{{ __('api_key') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="api_key" class="form-control" id="api_key"
                                value="{{ $paymentGatewaySetting->api_key }}" placeholder="{{ __('api_key') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="merchant_id">{{ __('merchant_id') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="merchant_id" class="form-control" id="merchant_id"
                                value="{{ $paymentGatewaySetting->merchant_id }}" placeholder="{{ __('merchant_id') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="terminal_id">{{ __('terminal_id') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="terminal_id" class="form-control" id="terminal_id"
                                value="{{ $paymentGatewaySetting->terminal_id }}" placeholder="{{ __('terminal_id') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="base_url">{{ __('base_url') }} <i class="required ml-sm">*</i></label>
                            <input type="url" name="base_url" class="form-control" id="base_url"
                                value="{{ $paymentGatewaySetting->base_url }}" placeholder="{{ __('base_url') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="currency">{{ __('currency') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="currency" class="form-control" id="currency"
                                value="{{ $paymentGatewaySetting->currency }}" placeholder="{{ __('currency') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="callback_url">{{ __('callback_url') }} <i class="required ml-sm">*</i></label>
                            <input type="url" name="callback_url" class="form-control" id="callback_url"
                                value="{{ $paymentGatewaySetting->callback_url }}" placeholder="{{ __('callback_url') }}" required>
                        </div>

                        <div class="mb-3">
                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" value="1"
                                {{ $paymentGatewaySetting->is_active ? 'checked' : '' }} />
                            <label class="form-check-label" for="is_active">
                                {{ __('is_active') }}
                            </label>
                        </div>

                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection