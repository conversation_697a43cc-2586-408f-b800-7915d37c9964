@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('sms_setting'))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('sms_setting') }}</h4>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route('settings.sms.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="baseUrl">{{ __('base_url') }} <i class="required ml-sm">*</i></label>
                            <input type="url" name="baseUrl" class="form-control" id="baseUrl"
                                value="{{ $smsSetting->baseUrl }}" placeholder="{{ __('base_url') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="userName">{{ __('username') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="userName" class="form-control" id="userName"
                                value="{{ $smsSetting->userName }}" placeholder="{{ __('username') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="apiKey">{{ __('api_key') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="apiKey" class="form-control" id="apiKey"
                                value="{{ $smsSetting->apiKey }}" placeholder="{{ __('api_key') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="userSender">{{ __('user_sender') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="userSender" class="form-control" id="userSender"
                                value="{{ $smsSetting->userSender }}" placeholder="{{ __('user_sender') }}" required>
                        </div>

                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection