@php
    $configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', __($name))

@push('styles')
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/dtable/css/buttons.dataTables.min.css" />
    <link rel="stylesheet" href="{{ asset('dashboard_files/plugins') }}/noty/noty.min.css" />
@endpush

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@push('scripts')
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/dtable/js/dataTables.buttons.min.js"></script>
    <script src="{{ asset('dashboard_files') }}/plugins/datatables/buttons.server-side.js"></script>
    <script src="{{ asset('dashboard_files') }}/dist/js/datatables.js"></script>

    <script>
        $("#client_id").select2({});
        $("#product_id").select2({});
        window.setTimeout(() => {
            $([document.documentElement, document.body]).animate({
                scrollTop: $("#transactions_table").offset().top / 3
            }, 800);
        }, 1100);
    </script>

    {!! $dataTable->scripts() !!}
@endpush


@section('page-script')
    <script>
        const datepickerList = document.querySelectorAll('.dob-picker');
        // Flat Picker Birth Date
        if (datepickerList) {
            datepickerList.forEach(function(datepicker) {
                datepicker.flatpickr({
                    monthSelectorType: 'static',
                    enableTime: true,
                    dateFormat: "Y-m-d H:i",
                });
            });
        }
    </script>
@endsection

@section('content')
    <h4 class="fw-semibold mb-4">{{ __($name) }}</h4>
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div id="transactions_table">
                        {!! $dataTable->table(
                            [
                                'class' => 'dataTable datatables-{{ $nameS }} table table-hover',
                                'responsive' => 'true',
                            ],
                            true,
                        ) !!}
                        <hr />
                    </div>
                    @canany(['create cash_in', 'create cash_out'])
                        <div class="row g-4 mb-2">
                            <div class="col-12">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h4 class="card-title"> @lang('in_out')</h4>
                                    </div>
                                </div>
                                <div class="card">
                                    <div class="card-body">
                                        @include('_partials._errors')
                                        <div class="row">
                                            @can('create cash_in')
                                                <div class="col">
                                                    <div class="card">
                                                        <div class="card-body">
                                                            <form action="{{ route($name . '.store_cash_in') }}" method="POST">
                                                                @csrf
                                                                <div class="row">
                                                                    <div class="col mb-4">
                                                                        <label for="financial_category_id"
                                                                            class="form-label required">{{ __('type') }} <i
                                                                                class="required ml-sm">*</i></label>
                                                                        <select id="financial_category_id" class="form-select"
                                                                            name="financial_category_id">
                                                                            @foreach ($financial_categories->where('deduct', 0) as $category)
                                                                                <option value="{{ $category->id }}">
                                                                                    {{ $category->{'title_' . __('lang')} }}
                                                                                </option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                    <div class="col mb-4">
                                                                        <label class="form-label required"
                                                                            for="amount">{{ __('in_amount') }}
                                                                            <i class="required ml-sm">*</i></label>
                                                                        <input type="text" id="amount" name="amount"
                                                                            class="form-control"
                                                                            placeholder="{{ __('in_amount') }}" />
                                                                    </div>

                                                                    <div class="col mb-4">
                                                                        <label class="form-label"
                                                                            for="paid">{{ __('paid_in') }}</label>
                                                                        <input type="text" id="paid" name="paid"
                                                                            class="form-control"
                                                                            placeholder="{{ __('paid_in') }}" />
                                                                    </div>
                                                                    <div class="col-12 mb-4">
                                                                        <label class="form-label"
                                                                            for="comment">{{ __('comment') }}</label>
                                                                        <textarea id="comment" name="comment" class="form-control" placeholder="{{ __('comment') }}">{{ old('comment') }}</textarea>
                                                                    </div>
                                                                    <div class="col text-center mb-4">
                                                                        <button type="submit"
                                                                            class="btn btn-success me-sm-3 me-1">{{ __('submit_in') }}</button>
                                                                    </div>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endcan
                                            @can('create cash_out')
                                                <div class="col">
                                                    <div class="card">
                                                        <div class="card-body">
                                                            <form action="{{ route($name . '.store_cash_out') }}" method="POST">
                                                                @csrf
                                                                <div class="row">
                                                                    <div class="col mb-4">
                                                                        <label for="financial_category_id"
                                                                            class="form-label required">{{ __('type') }} <i
                                                                                class="required ml-sm">*</i></label>
                                                                        <select id="financial_category_id" class="form-select"
                                                                            name="financial_category_id">
                                                                            @foreach ($financial_categories->where('deduct', 1) as $category)
                                                                                <option value="{{ $category->id }}">
                                                                                    {{ $category->{'title_' . __('lang')} }}
                                                                                </option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                    <div class="col mb-4">
                                                                        <label class="form-label required"
                                                                            for="amount">{{ __('out_amount') }}
                                                                            <i class="required ml-sm">*</i></label>
                                                                        <input type="text" id="amount" name="amount"
                                                                            class="form-control"
                                                                            placeholder="{{ __('out_amount') }}" />
                                                                    </div>
                                                                    <div class="col mb-4">
                                                                        <label class="form-label"
                                                                            for="paid">{{ __('paid_out') }}</label>
                                                                        <input type="text" id="paid" name="paid"
                                                                            class="form-control"
                                                                            placeholder="{{ __('paid_out') }}" />
                                                                    </div>
                                                                    <div class="col-12 mb-4">
                                                                        <label class="form-label"
                                                                            for="comment">{{ __('comment') }}</label>
                                                                        <textarea id="comment" name="comment" class="form-control" placeholder="{{ __('comment') }}">{{ old('comment') }}</textarea>
                                                                    </div>
                                                                    <div class="col text-center mb-4">
                                                                        <button type="submit"
                                                                            class="btn btn-danger me-sm-3 me-1">{{ __('submit_out') }}</button>
                                                                    </div>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endcan
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endcanany
                </div>
            </div>
        </div>
    </div>

@endsection
