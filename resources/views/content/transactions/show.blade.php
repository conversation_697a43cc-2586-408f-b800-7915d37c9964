@php
    $configData = Helper::appClasses();
@endphp
@inject('ExaminationItems', 'App\Models\Admin\Product')

@extends('layouts/layoutMaster')

@section('title', __('show') . ' ' . __($nameS))


@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/leaflet/leaflet.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/leaflet/leaflet.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
    <script src="{{ asset('dashboard_files/plugins/printThis/printThis.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/form-layouts.js') }}"></script>
    <script src="{{ asset('js/functions/' . $name . '.js') }}"></script>
    <script src="{{ asset('js/functions/general.js') }}"></script>
    <script>
        function print() {
            $("#process").printThis();
        }
    </script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <a href="javascript:void(0);" onclick="print();" class="btn btn-outline-primary float-right">
                        <i class="fas fa-print mr-1"></i> {{ __('print') }}
                    </a>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="p-3" id="process" style="direction: @lang('dir');background-color:white;">
                <h4 class="card-title mt-2 text-center"> {{ __('detailes') . ' ' . __($nameS) }}</h4>
                <div class="row">

                    <div class="col-{{$item->user->store_name ? '4' : '6'}} mb-1">
                        <label class="form-label fw-bold" for="number">{{ __('number') }}</label>
                        <input type="text" id="number" name="number" class="form-control"
                            placeholder="{{ __('number') }}" value="{{ $item->number ?? '' }}" readonly />
                    </div>

                    @if($item->user->store_name)
                        <div class="col-4 mb-1">
                            <label class="form-label fw-bold" for="company_id">{{ __('store_name') }}</label>
                            <input type="text" id="company_id" name="company_id" class="form-control"
                                placeholder="{{ __('store_name') }}" value="{{ $item->user->store_name ?? '' }}" readonly />
                        </div>
                    @endif
                    <div class="col-{{$item->user->store_name ? '4' : '6'}} mb-1">
                        <label class="form-label fw-bold" for="recived_date">{{ __('order_date') }}</label>
                        <input type="text" id="order_date" name="order_date" class="form-control"
                            placeholder="{{ __('order_date') }}" value="{{ $item->created_at->format('Y-m-d g:i A') ?? '' }}" readonly />
                    </div>
                    <div class="col-6 mb-1">
                        <label class="form-label fw-bold" for="client_name">{{ __('client') }}</label>
                        <input type="text" id="client_name" name="client_name" class="form-control"
                            placeholder="{{ __('client') }}" value="{{ $item->client->name ?? '' }}" readonly />
                    </div>
                    <div class="col-6 mb-1">
                        <label class="form-label fw-bold" for="client_phone">{{ __('phone') }}</label>
                        <input type="text" id="client_phone" name="client_phone" class="form-control"
                            placeholder="{{ __('phone') }}" value="{{ $item->phone ?? '' }}" readonly />
                    </div>
                    <div class="col-6 mb-1">
                        <label class="form-label fw-bold" for="client_phone2">{{ __('phone') }}2</label>
                        <input type="text" id="client_phone2" name="client_phone2" class="form-control"
                            placeholder="{{ __('phone') }}2" value="{{ $item->phone2 ?? '' }}" readonly />
                    </div>
                    <div class="col-6 mb-1">
                        <label for="country_id"
                            class="form-label fw-bold">{{ __('country') }}</label>
                        <input type="text" id="country_id"
                            name="country_id" class="form-control"
                            placeholder="{{ __('country') }}" value="{{ $item->shipping->{'title_' . __('lang')} ?? '' }}"
                            readonly />
                    </div>
                    <div class="col-12 mb-1">
                        <label class="form-label fw-bold" for="address">{{ __('address') }}</label>
                        <textarea id="address" name="address" class="form-control"
                            placeholder="{{ __('address') }}"
                            readonly >{{ $item->address ?? '' }}</textarea>
                    </div>
                    <div class="col-6 mb-1">
                        <label class="form-label fw-bold" for="order_total">{{ __('order_total') }}</label>
                        <input type="text" id="order_total" name="order_total" class="form-control"
                            placeholder="{{ __('order_total') }}" value="{{ $item->order_total ?? '' }}" readonly />
                    </div>
                    <div class="col-6 mb-1">
                        <label class="form-label fw-bold" for="order_collect">{{ __('order_collect') }}</label>
                        <input type="text" id="order_collect" name="order_collect" class="form-control"
                            placeholder="{{ __('order_collect') }}" value="{{ $item->collected ?? '' }}" readonly />
                    </div>
                    @if(!empty($item->notes))
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold" for="notes">{{ __('order_notes') }}</label>
                            <textarea id="notes" name="notes" class="form-control" placeholder="{{ __('order_notes') }}" tabindex="-1"
                                readonly>{{ $item->notes ?? '' }}</textarea>
                        </div>
                    @endif
                    <hr/>
                    @php
                    @endphp
                    <div class="col-12 mb-1" id="products">
                        <h3>{{ __('product_info') }}</h3>

                        <table class="table table-responsize" id="main_table">
                            <thead>
                                <th>@lang('sku')</th>
                                <th>@lang('name')</th>
                                <th width="10%" class="text-center">@lang('qty')</th>
                            </thead>
                            <tbody>
                                @foreach ($item->products as $pr)
                                @php
                                @endphp
                                    <tr data-sku="{{$pr->item->sku}}">
                                        <td class="fw-bold">
                                            {{$pr->item->sku}}
                                        </td>
                                        <td class="fw-bold">
                                            {{$pr->item->{'title_' . __('lang')} }}
                                        </td>
                                        <td class="fw-bold text-center">
                                            {{$pr->qty}}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
