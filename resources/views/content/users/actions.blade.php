@inject('user', 'App\Models\User')
@php $user = $user::find($id) @endphp
@if (!$user->can('read all_data') || auth()->user()->id != $id)
    <div class="dropdown">
        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i
                class="ti ti-dots-vertical"></i></button>
        <div class="dropdown-menu">
            @can('update users')
                <a href="{{ route('users.edit', $id) }}" class="dropdown-item"
                    title="{{ __('edit') }}">{{ __('edit') }}</a>
            @endcan


            @can('delete users')
                <form action="{{ route('users.destroy', $id) }}" method="post" class="mb-0">
                    @csrf
                    @method('DELETE')
                    <button class="dropdown-item delete" title="{{ __('delete') }}">
                        {{ __('delete') }}
                    </button>
                </form>
            @endcan
        </div>
    </div>
@endif
@include('content._datatableAction')
