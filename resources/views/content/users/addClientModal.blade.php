<!-- Modal -->
<div class="modal fade" id="createUserModal" data-bs-backdrop="static" data-bs-keyboard="false"
    aria-labelledby="createUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createUserModalLabel">@lang('add_client')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="@lang('close')"></button>
            </div>

            <form action="javascript:void(0);" method="POST" id="addClientForm">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <label class="form-label" for="client_name">{{ __('name') }} <i
                                    class="required ml-sm">*</i></label>
                            <input type="text" name="name" class="form-control" id="client_name" placeholder="{{ __('name') }}">
                            <div id="client_name_error" class="error text-danger"></div>
                        </div>

                        <div class="col-6 mb-3">
                            <label for="client_phone">{{ __('phone') }} <i class="required ml-sm">*</i></label>
                            <input type="text" name="phone" class="form-control" id="client_phone"
                                value="" placeholder="{{ __('phone') }}">
                            <div id="client_phone_error" class="error text-danger"></div>
                        </div>

                        <div class="col-6 mb-3">
                            <label class="form-label" for="client_email">{{ __('email') }} <i
                                    class="required ml-sm">*</i></label>
                            <input type="email" name="email" class="form-control" id="client_email"
                                value="{{ old('email') }}" placeholder="{{ __('email') }}">
                            <div id="client_email_error" class="error text-danger"></div>
                        </div>

                        <div class="col-6 mb-3">
                            <label for="client_country_id" class="required">{{ __('country') }} <i
                                    class="required ml-sm">*</i></label>
                            <select id="client_country_id" class="select2 form-select" name="country_id">
                                <option selected disabled>{{ __('select_country') }}</option>
                                @foreach ($shippingCountryModel->all() as $shc)
                                    <option value="{{ $shc->id }}">{{ ucwords($shc->{'title_' . __('lang')}) }}
                                    </option>
                                @endforeach
                            </select>
                            <div id="client_country_id_error" class="error text-danger"></div>
                        </div>

                        <div class="col-12 mb-3">
                            <label class="form-label" for="client_address">{{ __('address') }}</label>
                            <textarea id="client_address" name="address" class="form-control"
                                placeholder="{{ __('address') }}"
                                tabindex="-1" ></textarea>
                            <div id="client_address_error" class="error text-danger"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        data-bs-dismiss="modal">@lang('cancel')</button>
                    <button type="submit" class="btn btn-primary">@lang('save')</button>
                </div>
            </form>
        </div>
    </div>
</div>
