@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __('user'))


@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/pages-account-settings-account.js') }}"></script>
    <script>
        function updateType() {
            let type = $('#user_type_id').val();
            if(type == 3 || type == 4) {
                $('.business_info').fadeIn();
            } else {
                $('.business_info').fadeOut();
            }
        }
    </script>
@endsection


@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __('user') }}</h4>
                    <a href="{{ route('users.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route('users.store') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-4 mb-3">
                                <label class="form-label" for="name">{{ __('name') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" name="name" class="form-control" id="name"
                                    value="{{ old('name') }}" placeholder="{{ __('name') }}">
                            </div>

                            <div class="col-4 mb-3">
                                <label for="phone">{{ __('phone') }} <i class="required ml-sm">*</i></label>
                                <input type="text" name="phone" class="form-control" id="phone"
                                    value="{{ old('phone') }}" placeholder="{{ __('phone') }}">
                            </div>

                            <div class="col-4 mb-3">
                                <label class="form-label" for="email">{{ __('email') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="email" name="email" class="form-control" id="email"
                                    value="{{ old('email') }}" placeholder="{{ __('email') }}">
                            </div>

                            <div class="col-6 mb-3">
                                <label class="form-label" for="password">{{ __('password') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="password" name="password" class="form-control" id="password"
                                    placeholder="{{ __('password') }}">
                            </div>

                            <div class="col-6 mb-3">
                                <label class="form-label" for="inputPassword4">{{ __('confirm_password') }}</label>
                                <input type="password" name="password_confirmation" class="form-control" id="inputPassword4"
                                    placeholder="{{ __('confirm_password') }}">
                            </div>
                            @if(auth()->user()->user_type_id == 1)
                                <div class="col-6 mb-3 business_info" style="display:{{in_array(old('user_type_id'), [3,4]) ? 'block' : 'none'}}">
                                    <label class="form-label" for="store_name">{{ __('store_name') }} <i
                                    class="required ml-sm">*</i></label>
                                    <input type="text" name="store_name" class="form-control" id="store_name"
                                    value="{{ old('store_name') }}" placeholder="{{ __('store_name') }}">
                                </div>
                                <div class="col-6 mb-3 business_info" style="display:{{in_array(old('user_type_id'), [3,4]) ? 'block' : 'none'}}">
                                    <label class="form-label" for="invoice_prefix">{{ __('invoice_prefix') }} <i
                                    class="required ml-sm">*</i></label>
                                    <input type="text" name="invoice_prefix" class="form-control" id="invoice_prefix"
                                    value="{{ old('invoice_prefix') }}" placeholder="{{ __('invoice_prefix') }}">
                                </div>
                            @endif

                            @if(auth()->user()->user_type_id == 1)
                                <div class="col-6 mb-3">
                                    <label for="role" class="required">{{ __('roles') }} <i
                                            class="required ml-sm">*</i></label>
                                    <select id="role" class="select2 form-select" name="role">
                                        <option selected disabled>{{ __('select_role') }}</option>
                                        @foreach ($roles as $role)
                                            @if (
                                                ($role->name == 'super_admin' &&
                                                    auth()->user()->hasRole('super_admin')) ||
                                                    $role->name != 'super_admin')
                                                <option value="{{ $role->id }}"  {{ old('role') == $role->id ? 'selected' : '' }}>{{ ucwords($role->name) }}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-6 mb-3">
                                    <label for="user_type_id" class="required">{{ __('user_type') }} <i
                                            class="required ml-sm">*</i></label>
                                    <select id="user_type_id" class="select2 form-select" name="user_type_id" onchange="updateType();">
                                        <option selected disabled>{{ __('select_user_type') }}</option>
                                        @foreach ($types as $type)
                                            <option value="{{ $type->id }}" {{ old('user_type_id') == $type->id ? 'selected' : '' }}>{{ ucwords($type->{'title_' . __('lang')}) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
