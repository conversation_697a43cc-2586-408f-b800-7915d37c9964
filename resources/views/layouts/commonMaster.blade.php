<!DOCTYPE html>

<html lang="{{ session()->get('locale') ?? app()->getLocale() }}" class="{{ $configData['style'] }}-style {{ $navbarFixed ?? '' }} {{ $menuFixed ?? '' }} {{ $menuCollapsed ?? '' }} {{ $footerFixed ?? '' }} {{ $customizerHidden ?? '' }}" dir="{{ Auth::user() ? (session()->get('locale', Auth::user()->lang) == 'en' ? 'ltr' : 'rtl') : app()->getLocale() }}" data-theme="{{ $configData['theme'] }}" data-assets-path="{{ asset('/assets') . '/' }}" data-base-url="{{url('/')}}" data-framework="laravel" data-template="{{ $configData['layout'] . '-menu-' . $configData['theme'] . '-' . $configData['style'] }}">

    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
        <meta name="app-url" content="{!! url('/') !!}/" />
        <meta name="app-lang" content="@lang('lang')" />
        <title>@yield('title') |
            {{ config('variables.templateName') ? config('variables.templateName') : 'TemplateName' }} -
            {{ config('variables.templateSuffix') ? config('variables.templateSuffix') : 'TemplateSuffix' }}</title>
        <meta name="description" content="{{ config('variables.templateDescription') ? config('variables.templateDescription') : '' }}" />
        <meta name="keywords" content="{{ config('variables.templateKeyword') ? config('variables.templateKeyword') : '' }}">
        <!-- laravel CRUD token -->
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <!-- Canonical SEO -->
        <link rel="canonical" href="{{ config('variables.productPage') ? config('variables.productPage') : '' }}">
        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('assets/img/favicon/favicon.ico') }}" />



        <!-- Include Styles -->
        @include('layouts/sections/styles')

        <!-- Include Scripts for customizer, helper, analytics, config -->
        @include('layouts/sections/scriptsIncludes')
        @stack('styles')
        <script type="text/javascript">
            let lang = "{{ session()->get('locale') ?? app()->getLocale() }}"
        </script>
    </head>

    <body>


        <!-- Layout Content -->
        @yield('layoutContent')
        <!--/ Layout Content -->



        <!-- Include Scripts -->
        @include('layouts/sections/scripts')
        @include('_partials._session')
        @stack('scripts')
    </body>

</html>
