import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

void main() async {
  final baseUrl = 'http://localhost:8000/api';
  
  print('🧪 Testing Gather Point API Endpoints...\n');
  
  // Test 1: Service Categories
  print('1️⃣ Testing Service Categories API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/service_categories/list'),
      headers: {'Content-Type': 'application/json'},
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      print('✅ Service Categories: ${data['data']?.length ?? 0} categories found');
    } else {
      print('❌ Service Categories failed: ${response.statusCode}');
    }
  } catch (e) {
    print('❌ Service Categories error: $e');
  }
  
  // Test 2: Facilities
  print('\n2️⃣ Testing Facilities API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/facilities/list'),
      headers: {'Content-Type': 'application/json'},
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      print('✅ Facilities: ${data['data']?.length ?? 0} facilities found');
    } else {
      print('❌ Facilities failed: ${response.statusCode}');
    }
  } catch (e) {
    print('❌ Facilities error: $e');
  }
  
  print('\n🎯 API endpoints test completed!');
  print('📱 Flutter app should be able to connect to these endpoints.');
  print('🔧 Next step: Test the full property creation flow in the Flutter app.');
}
